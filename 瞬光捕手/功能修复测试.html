<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瞬光捕手 - 功能修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #16213e;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.danger {
            background: #f44336;
        }
        .test-button.danger:hover {
            background: #da190b;
        }
        .results {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
            white-space: pre-wrap;
        }
        .results.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .results.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .results.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 瞬光捕手 - 功能修复测试</h1>
        
        <div class="test-section">
            <h2>🎮 游戏界面测试</h2>
            <p>在下方的游戏界面中测试修复的功能：</p>
            <div class="test-controls">
                <button class="test-button" onclick="loadGame()">加载游戏</button>
                <button class="test-button" onclick="testUserCreation()">测试用户创建</button>
                <button class="test-button" onclick="testLeaderboard()">测试排行榜</button>
                <button class="test-button danger" onclick="clearTestData()">清除测试数据</button>
            </div>
            
            <div class="iframe-container" id="game-container" style="display: none;">
                <iframe id="game-frame" src=""></iframe>
            </div>
            
            <div id="test-results" class="results info" style="display: none;">
                测试结果将显示在这里...
            </div>
        </div>

        <div class="test-section">
            <h2>👤 用户创建功能测试</h2>
            <div class="test-controls">
                <button class="test-button" onclick="testUserCreationDirect()">直接测试用户创建</button>
                <button class="test-button" onclick="testUserCreationWithInvalidData()">测试无效数据处理</button>
                <button class="test-button" onclick="testUserCreationErrorHandling()">测试错误处理</button>
            </div>
            
            <div id="user-test-results" class="results info">
                <h3>用户创建功能测试说明：</h3>
                <p>1. 点击"直接测试用户创建"测试正常的用户创建流程</p>
                <p>2. 点击"测试无效数据处理"测试输入验证</p>
                <p>3. 点击"测试错误处理"测试异常情况处理</p>
                <p>4. 在游戏界面中点击"用户管理"按钮进行实际测试</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🏆 排行榜功能测试</h2>
            <div class="test-controls">
                <button class="test-button" onclick="testLeaderboardDirect()">直接测试排行榜</button>
                <button class="test-button" onclick="testLeaderboardData()">测试排行榜数据</button>
                <button class="test-button" onclick="addTestScores()">添加测试分数</button>
                <button class="test-button danger" onclick="clearLeaderboardData()">清除排行榜数据</button>
            </div>
            
            <div id="leaderboard-test-results" class="results info">
                <h3>排行榜功能测试说明：</h3>
                <p>1. 点击"直接测试排行榜"检查排行榜管理器状态</p>
                <p>2. 点击"测试排行榜数据"验证数据加载</p>
                <p>3. 点击"添加测试分数"生成测试数据</p>
                <p>4. 在游戏界面中点击"排行榜"按钮进行实际测试</p>
            </div>
        </div>

        <div class="test-section">
            <h2>🔍 系统状态检查</h2>
            <div class="test-controls">
                <button class="test-button" onclick="checkSystemStatus()">检查系统状态</button>
                <button class="test-button" onclick="checkDependencies()">检查依赖项</button>
                <button class="test-button" onclick="runFullDiagnostic()">运行完整诊断</button>
            </div>
            
            <div id="system-status-results" class="results info">
                点击"检查系统状态"开始检查...
            </div>
        </div>
    </div>

    <script>
        let gameFrame = null;

        function loadGame() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            
            frame.src = './index.html';
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                updateResults('test-results', 'success', '✅ 游戏界面已加载，可以进行功能测试');
            };
        }

        function testUserCreation() {
            if (!gameFrame) {
                updateResults('test-results', 'error', '❌ 请先加载游戏界面');
                return;
            }

            updateResults('test-results', 'info', 
                '📝 用户创建测试步骤：\n' +
                '1. 在游戏界面中点击"用户管理"按钮\n' +
                '2. 在弹出的对话框中填写用户信息\n' +
                '3. 点击"创建用户"按钮\n' +
                '4. 观察是否出现"获取null对象的get字段出错"错误\n' +
                '5. 检查浏览器控制台的错误信息'
            );
        }

        function testLeaderboard() {
            if (!gameFrame) {
                updateResults('test-results', 'error', '❌ 请先加载游戏界面');
                return;
            }

            updateResults('test-results', 'info', 
                '🏆 排行榜测试步骤：\n' +
                '1. 在游戏界面中点击"排行榜"按钮\n' +
                '2. 检查排行榜页面是否正常显示\n' +
                '3. 尝试切换不同的排行榜标签\n' +
                '4. 点击"刷新"按钮测试刷新功能\n' +
                '5. 观察是否有JavaScript错误'
            );
        }

        function testUserCreationDirect() {
            let report = '🧪 直接用户创建测试：\n\n';
            
            try {
                // 检查必要的依赖
                const dependencies = [
                    'window.storageService',
                    'window.StorageKeySchema', 
                    'window.UserDataValidator',
                    'window.UserDataStructure',
                    'window.userManager'
                ];

                report += '检查依赖项：\n';
                dependencies.forEach(dep => {
                    const exists = eval(dep);
                    report += `  ${dep}: ${exists ? '✅ 存在' : '❌ 缺失'}\n`;
                });

                report += '\n测试结果：\n';
                if (dependencies.every(dep => eval(dep))) {
                    report += '✅ 所有依赖项都存在，用户创建功能应该正常工作\n';
                    report += '💡 建议在游戏界面中进行实际测试';
                } else {
                    report += '❌ 发现缺失的依赖项，这可能导致用户创建失败\n';
                    report += '🔧 需要检查模块加载顺序和初始化过程';
                }

            } catch (error) {
                report += `❌ 测试过程中出现错误: ${error.message}\n`;
                report += '🔧 需要在游戏环境中进行测试';
            }

            updateResults('user-test-results', 'info', report);
        }

        function testUserCreationWithInvalidData() {
            updateResults('user-test-results', 'info', 
                '🧪 无效数据测试场景：\n\n' +
                '1. 空用户标识符测试\n' +
                '2. 空显示名称测试\n' +
                '3. 无效字符测试（特殊符号）\n' +
                '4. 过长字符串测试\n' +
                '5. 重复用户标识符测试\n\n' +
                '💡 在游戏界面的用户管理中尝试这些无效输入，\n' +
                '   系统应该显示相应的错误提示而不是崩溃。'
            );
        }

        function testUserCreationErrorHandling() {
            updateResults('user-test-results', 'info', 
                '🧪 错误处理测试场景：\n\n' +
                '1. 存储服务不可用时的处理\n' +
                '2. 网络连接问题的处理\n' +
                '3. 数据验证失败的处理\n' +
                '4. 系统资源不足的处理\n\n' +
                '💡 这些场景需要在特定条件下测试，\n' +
                '   主要检查错误信息是否友好且不会导致系统崩溃。'
            );
        }

        function testLeaderboardDirect() {
            let report = '🧪 直接排行榜测试：\n\n';
            
            try {
                // 检查排行榜相关依赖
                const dependencies = [
                    'window.leaderboardManager',
                    'window.playerManager',
                    'window.storageService'
                ];

                report += '检查依赖项：\n';
                dependencies.forEach(dep => {
                    const exists = eval(dep);
                    report += `  ${dep}: ${exists ? '✅ 存在' : '❌ 缺失'}\n`;
                });

                if (window.leaderboardManager) {
                    report += `\n排行榜管理器状态：\n`;
                    report += `  初始化状态: ${window.leaderboardManager.initialized ? '✅ 已初始化' : '❌ 未初始化'}\n`;
                    
                    if (window.leaderboardManager.initialized) {
                        const availableBoards = window.leaderboardManager.getAvailableLeaderboards();
                        report += `  可用排行榜: ${availableBoards.length} 个\n`;
                        availableBoards.forEach(board => {
                            report += `    - ${board.type}: ${board.hasData ? '有数据' : '无数据'}\n`;
                        });
                    }
                }

            } catch (error) {
                report += `❌ 测试过程中出现错误: ${error.message}\n`;
            }

            updateResults('leaderboard-test-results', 'info', report);
        }

        function testLeaderboardData() {
            updateResults('leaderboard-test-results', 'info', 
                '🧪 排行榜数据测试：\n\n' +
                '1. 检查排行榜数据结构\n' +
                '2. 验证数据加载机制\n' +
                '3. 测试数据显示格式\n' +
                '4. 检查玩家排名计算\n\n' +
                '💡 在游戏界面的排行榜中查看实际数据显示效果'
            );
        }

        function addTestScores() {
            updateResults('leaderboard-test-results', 'info', 
                '🧪 添加测试分数：\n\n' +
                '为了测试排行榜功能，建议：\n' +
                '1. 先创建几个测试用户\n' +
                '2. 进行几局游戏获得分数\n' +
                '3. 检查分数是否正确提交到排行榜\n' +
                '4. 验证排行榜排序是否正确\n\n' +
                '💡 也可以通过开发者工具手动添加测试数据'
            );
        }

        function clearTestData() {
            if (confirm('确定要清除所有测试数据吗？这将删除用户数据和排行榜数据。')) {
                try {
                    localStorage.clear();
                    updateResults('test-results', 'success', '✅ 测试数据已清除，请刷新页面重新开始测试');
                } catch (error) {
                    updateResults('test-results', 'error', `❌ 清除数据失败: ${error.message}`);
                }
            }
        }

        function clearLeaderboardData() {
            updateResults('leaderboard-test-results', 'info', 
                '🧪 清除排行榜数据：\n\n' +
                '可以通过以下方式清除排行榜数据：\n' +
                '1. 使用浏览器开发者工具清除localStorage\n' +
                '2. 在游戏中使用管理员功能（如果有）\n' +
                '3. 点击上方的"清除测试数据"按钮\n\n' +
                '⚠️ 注意：这将删除所有排行榜记录'
            );
        }

        function checkSystemStatus() {
            let report = '🔍 系统状态检查：\n\n';
            
            // 检查基础环境
            report += '基础环境：\n';
            report += `  浏览器: ${navigator.userAgent.split(' ')[0]}\n`;
            report += `  localStorage: ${typeof Storage !== 'undefined' ? '✅ 支持' : '❌ 不支持'}\n`;
            report += `  IndexedDB: ${typeof indexedDB !== 'undefined' ? '✅ 支持' : '❌ 不支持'}\n`;
            
            // 检查全局对象
            report += '\n全局对象检查：\n';
            const globalObjects = [
                'window', 'document', 'console', 'localStorage'
            ];
            
            globalObjects.forEach(obj => {
                report += `  ${obj}: ${typeof window[obj] !== 'undefined' ? '✅ 存在' : '❌ 缺失'}\n`;
            });

            updateResults('system-status-results', 'info', report);
        }

        function checkDependencies() {
            let report = '🔍 依赖项检查：\n\n';
            
            const dependencies = [
                'storageService',
                'userManager', 
                'playerManager',
                'leaderboardManager',
                'screenManager',
                'i18nService',
                'StorageKeySchema',
                'UserDataValidator',
                'UserDataStructure'
            ];

            dependencies.forEach(dep => {
                const exists = typeof window[dep] !== 'undefined';
                const initialized = exists && window[dep].initialized;
                report += `  ${dep}: ${exists ? '✅ 存在' : '❌ 缺失'}`;
                if (exists && typeof initialized === 'boolean') {
                    report += ` (${initialized ? '已初始化' : '未初始化'})`;
                }
                report += '\n';
            });

            updateResults('system-status-results', 'info', report);
        }

        function runFullDiagnostic() {
            let report = '🔍 完整系统诊断：\n\n';
            
            report += '=== 环境检查 ===\n';
            report += `浏览器: ${navigator.userAgent}\n`;
            report += `页面URL: ${window.location.href}\n`;
            report += `存储支持: localStorage=${typeof Storage !== 'undefined'}, IndexedDB=${typeof indexedDB !== 'undefined'}\n`;
            
            report += '\n=== 脚本加载检查 ===\n';
            const scripts = document.querySelectorAll('script[src]');
            report += `已加载脚本数量: ${scripts.length}\n`;
            
            report += '\n=== 控制台错误检查 ===\n';
            report += '请打开浏览器开发者工具查看控制台错误信息\n';
            
            report += '\n=== 建议的测试步骤 ===\n';
            report += '1. 加载游戏界面\n';
            report += '2. 测试用户创建功能\n';
            report += '3. 测试排行榜功能\n';
            report += '4. 检查浏览器控制台的错误信息\n';
            report += '5. 验证修复效果\n';

            updateResults('system-status-results', 'info', report);
        }

        function updateResults(elementId, type, content) {
            const element = document.getElementById(elementId);
            element.className = `results ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        // 页面加载完成后自动检查系统状态
        document.addEventListener('DOMContentLoaded', () => {
            checkSystemStatus();
        });
    </script>
</body>
</html>
