# 瞬光捕手 - 国际化（i18n）配置说明

## 概述

瞬光捕手游戏实现了完整的国际化支持，目前支持中文（zh-CN）和英文（en-US）两种语言。国际化系统采用模块化设计，支持动态语言切换和自动翻译应用。

## 📁 核心文件

### 1. 国际化服务文件
- **文件路径**: `js/utils/i18n.js`
- **主要类**: `I18nService`
- **全局实例**: `window.i18nService`

### 2. 测试文件
- **测试页面**: `i18n测试.html`
- **说明文档**: `docs/i18n配置说明.md`

## 🌐 主要功能

### 语言支持
- **中文（zh-CN）**: 默认语言，完整翻译
- **英文（en-US）**: 备选语言，完整翻译
- **浏览器语言检测**: 自动检测用户浏览器语言偏好
- **语言持久化**: 用户选择的语言会保存到本地存储

### 核心 API
```javascript
// 获取翻译文本
i18nService.t(key, params)

// 切换语言
i18nService.setLanguage(language)

// 获取当前语言
i18nService.getCurrentLanguage()

// 获取支持的语言列表
i18nService.getSupportedLanguages()

// 应用翻译到 DOM
i18nService.applyTranslations()

// 检测浏览器语言
i18nService.detectBrowserLanguage()
```

## 翻译键结构

### 游戏基本信息
```javascript
'game.title': '瞬光捕手'
'game.subtitle': '捕捉决定性瞬间，引燃无限可能'
'loading.text': '正在加载...'
```

### 主菜单
```javascript
'menu.start': '开始游戏'
'menu.levelEditor': '关卡编辑器'
'menu.customLevels': '自定义关卡'
'menu.leaderboard': '排行榜'
'menu.settings': '设置'
```

### 游戏界面
```javascript
'game.score': '得分'
'game.level': '关卡'
'game.lives': '生命'
'game.pause': '暂停'
'game.hint.ready': '准备好了吗？'
'game.hint.start': '点击开始！'
'game.hint.perfect': '完美时机！'
'game.hint.good': '不错！'
'game.hint.miss': '错过了！'
'game.hint.gameOver': '游戏结束'
```

### 设置界面
```javascript
'settings.title': '设置'
'settings.language': '语言'
'settings.sound': '音效'
'settings.music': '音乐'
'settings.save': '保存设置'
'settings.cancel': '取消'
```

### 关卡编辑器
```javascript
'editor.title': '关卡编辑器'
'editor.tools': '工具'
'editor.select': '选择'
'editor.spark': '光点'
'editor.obstacle': '障碍'
'editor.powerup': '道具'
'editor.save': '保存'
'editor.test': '测试'
'editor.publish': '发布'
```

### 排行榜
```javascript
'leaderboard.title': '排行榜'
'leaderboard.global': '全球排行'
'leaderboard.daily': '今日排行'
'leaderboard.rank': '排名'
'leaderboard.player': '玩家'
'leaderboard.score': '分数'
```

### 错误信息
```javascript
'error.storageNotSupported': '您的浏览器不支持数据存储功能'
'error.saveGameFailed': '保存游戏失败'
'error.loadGameFailed': '加载游戏失败'
'error.networkError': '网络错误'
```

## HTML 中的使用

### 1. 文本内容翻译
使用 `data-i18n` 属性：
```html
<h1 data-i18n="game.title">瞬光捕手</h1>
<button data-i18n="menu.start">开始游戏</button>
<p data-i18n="game.subtitle">捕捉决定性瞬间，引燃无限可能</p>
```

### 2. 占位符翻译
使用 `data-i18n-placeholder` 属性：
```html
<input type="text" data-i18n-placeholder="player.namePlaceholder">
```

### 3. 标题翻译
使用 `data-i18n-title` 属性：
```html
<button data-i18n-title="editor.selectTool" class="tool-btn">🔧</button>
```

## JavaScript 中的使用

### 1. 基本翻译
```javascript
// 获取翻译文本
const title = i18nService.t('game.title');
const startText = i18nService.t('menu.start');
```

### 2. 参数化翻译
```javascript
// 带参数的翻译
const message = i18nService.t('customLevels.confirmDelete', { name: '关卡名称' });
// 结果: "确定要删除关卡 "关卡名称" 吗？"
```

### 3. 动态更新界面
```javascript
// 切换语言后自动更新所有翻译
await i18nService.setLanguage('en-US');
```

## 语言切换实现

### 1. 设置界面
在设置界面中提供语言选择下拉框：
```html
<div class="setting-group">
    <label data-i18n="settings.language">语言:</label>
    <select id="language-select">
        <option value="zh-CN">中文</option>
        <option value="en-US">English</option>
    </select>
</div>
```

### 2. 保存设置逻辑
```javascript
async saveSettings() {
    const language = document.getElementById('language-select')?.value || 'zh-CN';
    
    // 应用语言设置
    await i18nService.setLanguage(language);
    
    // 保存到玩家设置
    await playerManager.updatePlayerSettings({ language });
}
```

## 自动翻译应用

### 1. 初始化时应用
```javascript
async init() {
    // 从存储中恢复语言设置
    const savedLanguage = await storageService.get('settings.language', 'zh-CN');
    this.currentLanguage = savedLanguage;
    
    // 应用翻译
    this.applyTranslations();
}
```

### 2. 翻译应用逻辑
```javascript
applyTranslations() {
    // 翻译所有带有 data-i18n 属性的元素
    const elements = document.querySelectorAll('[data-i18n]');
    elements.forEach(element => {
        const key = element.getAttribute('data-i18n');
        element.textContent = this.t(key);
    });

    // 翻译占位符
    const placeholderElements = document.querySelectorAll('[data-i18n-placeholder]');
    placeholderElements.forEach(element => {
        const key = element.getAttribute('data-i18n-placeholder');
        element.placeholder = this.t(key);
    });

    // 翻译标题
    const titleElements = document.querySelectorAll('[data-i18n-title]');
    titleElements.forEach(element => {
        const key = element.getAttribute('data-i18n-title');
        element.title = this.t(key);
    });

    // 更新页面标题
    document.title = this.t('game.title') + ' - Split-Second Spark';
}
```

## 浏览器语言检测

```javascript
detectBrowserLanguage() {
    const browserLang = navigator.language || navigator.userLanguage;
    
    // 检查是否支持检测到的语言
    if (this.translations[browserLang]) {
        return browserLang;
    }
    
    // 检查语言的主要部分（如 en-GB -> en）
    const mainLang = browserLang.split('-')[0];
    const supportedLangs = Object.keys(this.translations);
    const matchedLang = supportedLangs.find(lang => lang.startsWith(mainLang));
    
    return matchedLang || 'zh-CN'; // 默认返回中文
}
```

## 存储集成

国际化服务与存储服务集成，支持语言设置的持久化：

```javascript
// 保存语言设置
await storageService.put('settings.language', language);

// 恢复语言设置
const savedLanguage = await storageService.get('settings.language', 'zh-CN');
```

## 模块初始化顺序

在主应用中，国际化服务在模块初始化序列中的位置：

```javascript
const initSteps = [
    { name: '存储服务', module: 'storageService' },
    { name: '国际化服务', module: 'i18nService' },  // 第二个初始化
    { name: '玩家管理器', module: 'playerManager' },
    // ... 其他模块
];
```

## 最佳实践

### 1. 翻译键命名规范
- 使用点号分隔的层级结构
- 采用语义化命名
- 保持一致的命名风格

### 2. 参数化翻译
- 对于包含动态内容的文本使用参数
- 使用 `{{参数名}}` 格式

### 3. 回退机制
- 优先使用当前语言翻译
- 回退到中文翻译
- 最后显示原始键名

### 4. 性能优化
- 翻译数据在初始化时一次性加载
- 避免频繁的 DOM 查询和更新

## 扩展新语言

要添加新语言支持：

1. 在 `loadTranslations()` 方法中添加新语言的翻译数据
2. 更新 `getSupportedLanguages()` 方法
3. 在设置界面的语言选择器中添加新选项
4. 测试所有界面的翻译效果

## 🧪 测试和调试

### 测试页面使用
1. 打开 `i18n测试.html` 页面
2. 点击各种测试按钮验证功能
3. 使用语言切换器测试动态切换
4. 查看控制台日志了解详细信息

### 测试功能
- **基础服务测试**: 验证 i18n 服务是否正确初始化
- **翻译功能测试**: 测试各种翻译键的正确性
- **语言切换测试**: 验证动态语言切换功能
- **DOM 更新测试**: 确认页面元素正确更新翻译

### 调试技巧
```javascript
// 在控制台中调试
console.log('当前语言:', i18nService.getCurrentLanguage());
console.log('翻译测试:', i18nService.t('game.title'));
console.log('支持的语言:', i18nService.getSupportedLanguages());

// 手动应用翻译
i18nService.applyTranslations();

// 切换语言并观察变化
await i18nService.setLanguage('en-US');
```

## 📊 翻译覆盖率

当前翻译覆盖的功能模块：
- ✅ 游戏基本信息（标题、副标题等）
- ✅ 主菜单界面
- ✅ 游戏界面（得分、关卡、生命等）
- ✅ 暂停和游戏结束界面
- ✅ 设置界面
- ✅ 玩家管理
- ✅ 排行榜系统
- ✅ 关卡编辑器（完整功能）
- ✅ 自定义关卡系统
- ✅ 错误信息和提示
- ✅ 通用界面元素

总计超过 **260+ 个翻译键**，覆盖游戏的所有用户界面。

## 🔧 维护和扩展

### 添加新翻译键
1. 在 `loadTranslations()` 方法中为两种语言添加翻译
2. 使用语义化的键名（如 `module.function.element`）
3. 在 HTML 中使用 `data-i18n` 属性
4. 在 JavaScript 中使用 `i18nService.t()` 方法

### 添加新语言
1. 在翻译数据中添加新语言对象
2. 更新 `getSupportedLanguages()` 方法
3. 在设置界面添加语言选项
4. 测试所有界面的翻译效果

### 性能优化建议
- 翻译数据在初始化时一次性加载，避免运行时加载
- 使用事件委托减少 DOM 查询
- 缓存常用翻译结果
- 避免频繁的语言切换操作

## 🎯 总结

瞬光捕手的国际化系统具有以下特点：

1. **完整性**: 覆盖游戏所有用户界面
2. **易用性**: 简单的 API 和 HTML 属性支持
3. **可维护性**: 模块化设计，易于扩展
4. **性能优化**: 高效的翻译应用机制
5. **用户友好**: 自动语言检测和持久化存储
6. **开发友好**: 完整的测试工具和调试支持

这个国际化系统为瞬光捕手游戏提供了完整的多语言支持，确保了良好的用户体验和易于维护的代码结构。
