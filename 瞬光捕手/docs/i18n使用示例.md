# 瞬光捕手 - i18n 使用示例

## HTML 中的使用示例

### 1. 基本文本翻译
```html
<!-- 使用 data-i18n 属性进行文本翻译 -->
<h1 data-i18n="game.title">瞬光捕手</h1>
<p data-i18n="game.subtitle">捕捉决定性瞬间，引燃无限可能</p>
<button data-i18n="menu.start">开始游戏</button>
```

### 2. 表单元素翻译
```html
<!-- 输入框占位符翻译 -->
<input type="text" data-i18n-placeholder="player.namePlaceholder" placeholder="输入玩家名称">

<!-- 按钮标题翻译 -->
<button data-i18n-title="editor.selectTool" title="请选择工具">🔧</button>

<!-- 选择框选项翻译 -->
<select id="language-select">
    <option value="zh-CN">中文</option>
    <option value="en-US">English</option>
</select>
```

### 3. 复杂界面结构
```html
<div class="settings-content">
    <h2 data-i18n="settings.title">设置</h2>
    
    <div class="setting-group">
        <label data-i18n="settings.language">语言:</label>
        <select id="language-select">
            <option value="zh-CN">中文</option>
            <option value="en-US">English</option>
        </select>
    </div>

    <div class="setting-group">
        <label data-i18n="settings.sound">音效:</label>
        <input type="range" id="sound-volume" min="0" max="100" value="50">
    </div>

    <div class="menu-buttons">
        <button id="save-settings-btn" data-i18n="settings.save">保存设置</button>
        <button id="cancel-settings-btn" data-i18n="settings.cancel">取消</button>
    </div>
</div>
```

## JavaScript 中的使用示例

### 1. 基本翻译调用
```javascript
// 获取简单翻译
const title = i18nService.t('game.title');
console.log(title); // "瞬光捕手" 或 "Split-Second Spark"

// 获取菜单文本
const startText = i18nService.t('menu.start');
const settingsText = i18nService.t('menu.settings');
```

### 2. 参数化翻译
```javascript
// 带参数的翻译
const confirmMessage = i18nService.t('customLevels.confirmDelete', { 
    name: '我的关卡' 
});
console.log(confirmMessage); // "确定要删除关卡 "我的关卡" 吗？"

// 时间格式化翻译
const timeAgo = i18nService.t('time.minutesAgo', { 
    minutes: 5 
});
console.log(timeAgo); // "5分钟前"
```

### 3. 动态内容更新
```javascript
// 动态更新游戏状态显示
function updateGameUI(score, level, lives) {
    document.getElementById('score-label').textContent = i18nService.t('game.score');
    document.getElementById('level-label').textContent = i18nService.t('game.level');
    document.getElementById('lives-label').textContent = i18nService.t('game.lives');
    
    document.getElementById('current-score').textContent = score;
    document.getElementById('current-level').textContent = level;
    document.getElementById('current-lives').textContent = lives;
}

// 动态创建菜单项
function createMenuItem(key, action) {
    const button = document.createElement('button');
    button.className = 'menu-btn';
    button.textContent = i18nService.t(key);
    button.onclick = action;
    return button;
}

// 使用示例
const startButton = createMenuItem('menu.start', startGame);
const editorButton = createMenuItem('menu.levelEditor', openLevelEditor);
```

### 4. 语言切换处理
```javascript
// 切换语言
async function switchLanguage(languageCode) {
    try {
        await i18nService.setLanguage(languageCode);
        console.log(`语言已切换到: ${languageCode}`);
        
        // 可选：显示成功消息
        showNotification(i18nService.t('common.success'));
        
    } catch (error) {
        console.error('语言切换失败:', error);
        showNotification(i18nService.t('common.error'));
    }
}

// 获取当前语言
function getCurrentLanguageInfo() {
    const currentLang = i18nService.getCurrentLanguage();
    const supportedLangs = i18nService.getSupportedLanguages();
    const currentLangInfo = supportedLangs.find(lang => lang.code === currentLang);
    
    return {
        code: currentLang,
        name: currentLangInfo ? currentLangInfo.name : currentLang
    };
}
```

### 5. 错误处理和提示
```javascript
// 错误消息显示
function showError(errorType, details = '') {
    let message;
    
    switch (errorType) {
        case 'network':
            message = i18nService.t('error.networkError');
            break;
        case 'storage':
            message = i18nService.t('error.storageNotSupported');
            break;
        case 'save':
            message = i18nService.t('error.saveGameFailed');
            break;
        default:
            message = i18nService.t('common.error');
    }
    
    if (details) {
        message += `: ${details}`;
    }
    
    alert(message);
}

// 成功消息显示
function showSuccess(messageKey = 'common.success') {
    const message = i18nService.t(messageKey);
    showNotification(message, 'success');
}
```

### 6. 关卡编辑器中的使用
```javascript
// 工具提示更新
function updateToolTooltips() {
    const tools = [
        { id: 'select-tool', key: 'editor.select' },
        { id: 'spark-tool', key: 'editor.spark' },
        { id: 'obstacle-tool', key: 'editor.obstacle' },
        { id: 'powerup-tool', key: 'editor.powerup' }
    ];
    
    tools.forEach(tool => {
        const element = document.getElementById(tool.id);
        if (element) {
            element.title = i18nService.t(tool.key);
        }
    });
}

// 关卡验证消息
function validateLevel(level) {
    if (!level.name) {
        return {
            valid: false,
            message: i18nService.t('editor.nameRequired')
        };
    }
    
    if (level.objects.length === 0) {
        return {
            valid: false,
            message: i18nService.t('editor.noObjects')
        };
    }
    
    const sparkCount = level.objects.filter(obj => obj.type === 'spark').length;
    if (sparkCount === 0) {
        return {
            valid: false,
            message: i18nService.t('editor.noSparks')
        };
    }
    
    return {
        valid: true,
        message: i18nService.t('common.success')
    };
}
```

### 7. 排行榜显示
```javascript
// 排行榜表头
function createLeaderboardHeader() {
    const headers = [
        'leaderboard.rank',
        'leaderboard.player', 
        'leaderboard.score',
        'leaderboard.level',
        'leaderboard.date'
    ];
    
    const headerRow = document.createElement('tr');
    headers.forEach(headerKey => {
        const th = document.createElement('th');
        th.textContent = i18nService.t(headerKey);
        headerRow.appendChild(th);
    });
    
    return headerRow;
}

// 空状态显示
function showEmptyLeaderboard() {
    const emptyDiv = document.createElement('div');
    emptyDiv.className = 'empty-state';
    emptyDiv.innerHTML = `
        <h3>${i18nService.t('leaderboard.empty')}</h3>
        <p>${i18nService.t('leaderboard.beFirst')}</p>
    `;
    return emptyDiv;
}
```

## 最佳实践示例

### 1. 组件化翻译
```javascript
class GameComponent {
    constructor() {
        this.translations = {
            title: 'component.title',
            description: 'component.description',
            action: 'component.action'
        };
    }
    
    render() {
        return `
            <div class="game-component">
                <h3>${i18nService.t(this.translations.title)}</h3>
                <p>${i18nService.t(this.translations.description)}</p>
                <button onclick="this.handleAction()">
                    ${i18nService.t(this.translations.action)}
                </button>
            </div>
        `;
    }
    
    updateTranslations() {
        // 当语言切换时更新组件
        const element = this.getElement();
        if (element) {
            element.innerHTML = this.render();
        }
    }
}
```

### 2. 批量翻译更新
```javascript
// 批量更新特定区域的翻译
function updateSectionTranslations(sectionId) {
    const section = document.getElementById(sectionId);
    if (!section) return;
    
    // 更新所有翻译元素
    const i18nElements = section.querySelectorAll('[data-i18n]');
    i18nElements.forEach(element => {
        const key = element.getAttribute('data-i18n');
        element.textContent = i18nService.t(key);
    });
    
    // 更新占位符
    const placeholderElements = section.querySelectorAll('[data-i18n-placeholder]');
    placeholderElements.forEach(element => {
        const key = element.getAttribute('data-i18n-placeholder');
        element.placeholder = i18nService.t(key);
    });
}
```

### 3. 响应式翻译
```javascript
// 监听语言变更事件
window.addEventListener('languageChanged', (event) => {
    const newLanguage = event.detail.language;
    console.log(`语言已切换到: ${newLanguage}`);
    
    // 更新特定组件
    updateGameUI();
    updateToolTooltips();
    updateLeaderboardHeaders();
});

// 自定义语言变更事件触发
function triggerLanguageChangeEvent(language) {
    const event = new CustomEvent('languageChanged', {
        detail: { language }
    });
    window.dispatchEvent(event);
}
```

这些示例展示了如何在瞬光捕手游戏中有效使用国际化功能，涵盖了从基本翻译到复杂场景的各种用法。
