# 瞬光捕手 - 用户配置管理系统说明文档

> 全新的用户管理系统，实现用户标识与显示名称分离，确保数据完全隔离

## 📋 系统概述

瞬光捕手的用户配置管理系统经过全面重构，引入了现代化的用户管理理念：

### 🎯 核心特性

1. **用户标识与显示名称分离**
   - `标识符(identifier)`：用户的唯一标识，用作主键，创建后不可修改
   - `显示名称(display_name)`：用户的可读名称，可以随时修改

2. **完全的数据隔离**
   - 所有用户数据使用用户标识符作为前缀
   - 存储键格式：`{用户标识}_{数据类型}[.{子类型}]`
   - 确保不同用户的数据完全独立

3. **无缝的用户切换**
   - 快速切换用户身份
   - 自动加载对应用户的所有配置和数据
   - 保持数据一致性和完整性

4. **向后兼容性**
   - 自动迁移现有用户数据
   - 兼容原有的PlayerManager和UserCredentialSystem
   - 平滑升级，无需重新创建用户

## 🏗️ 系统架构

### 核心组件

```
用户配置管理系统
├── UserManager (用户管理核心类)
├── UserStorageService (用户存储服务)
├── UserSwitchManager (用户切换管理器)
├── UserSystemAdapter (系统适配器)
├── UserManagementUI (用户管理界面)
└── UserDataStructure (数据结构定义)
```

### 数据结构

#### 用户核心数据结构
```javascript
{
    identifier: "user123",           // 用户唯一标识符
    displayName: "张三",             // 显示名称
    type: "registered",              // 用户类型：registered|guest|anonymous
    createdAt: 1691234567890,        // 创建时间戳
    lastActiveAt: 1691234567890,     // 最后活跃时间戳
    metadata: {
        version: "1.0",              // 数据结构版本
        source: "manual",            // 创建来源
        deviceInfo: {...},           // 设备信息
        preferences: {...}           // 用户偏好
    }
}
```

#### 存储键值规范
```javascript
// 用户核心数据
user123_core

// 游戏数据
user123_gameData

// 用户设置
user123_settings.audio
user123_settings.graphics

// 游戏进度
user123_progress

// 统计数据
user123_stats

// 成就数据
user123_achievements

// 自定义关卡
user123_customLevels

// 排行榜数据
user123_leaderboard.normal.score
```

## 🚀 快速开始

### 1. 系统初始化

```javascript
// 在main.js中，用户系统适配器会自动初始化
// 无需手动初始化，系统会自动处理迁移和设置
```

### 2. 创建新用户

```javascript
// 通过用户管理器创建用户
const user = await userManager.createUser('myuser123', '我的显示名称');
console.log('用户创建成功:', user);

// 通过界面创建用户
// 点击用户切换按钮 -> 用户管理 -> 创建新用户
```

### 3. 切换用户

```javascript
// 程序化切换
const success = await userManager.switchToUser('myuser123');

// 使用切换管理器（推荐）
const result = await userSwitchManager.switchUser('myuser123');
console.log('切换结果:', result);

// 通过界面切换
// 点击用户切换按钮 -> 选择目标用户 -> 点击切换
```

### 4. 用户数据操作

```javascript
// 获取当前用户
const currentUser = userManager.getCurrentUser();

// 保存用户数据（自动使用当前用户标识符作为前缀）
await userStorageService.putUserData('gameData', gameData);

// 读取用户数据
const gameData = await userStorageService.getUserData('gameData');

// 删除用户数据
await userStorageService.deleteUserData('gameData');
```

## 🔧 API 参考

### UserManager 类

#### 核心方法

```javascript
// 创建用户
async createUser(identifier, displayName, options = {})

// 切换用户
async switchToUser(identifier)

// 删除用户
async deleteUser(identifier, options = {})

// 获取当前用户
getCurrentUser()

// 获取所有用户
getAllUsers()

// 检查用户是否存在
async userExists(identifier)

// 更新用户显示名称
async updateUserDisplayName(identifier, newDisplayName)

// 导出用户数据
async exportUserData(identifier)

// 导入用户数据
async importUserData(exportData, options = {})
```

#### 事件监听

```javascript
// 监听用户切换事件
userManager.addEventListener('userSwitched', (eventData) => {
    console.log('用户已切换:', eventData.currentUser);
});

// 监听用户创建事件
userManager.addEventListener('userCreated', (eventData) => {
    console.log('用户已创建:', eventData.user);
});

// 监听用户删除事件
userManager.addEventListener('userDeleted', (eventData) => {
    console.log('用户已删除:', eventData.identifier);
});
```

### UserStorageService 类

#### 数据操作方法

```javascript
// 设置当前用户
setCurrentUser(userIdentifier)

// 保存用户数据
async putUserData(dataType, value, subType = null, userIdentifier = null)

// 读取用户数据
async getUserData(dataType, defaultValue = null, subType = null, userIdentifier = null)

// 删除用户数据
async deleteUserData(dataType, subType = null, userIdentifier = null)

// 列出用户数据键
async listUserData(userIdentifier = null)

// 清空用户数据
async clearUserData(userIdentifier)

// 复制用户数据
async copyUserData(sourceIdentifier, targetIdentifier, options = {})
```

### StorageKeySchema 类

#### 键值管理方法

```javascript
// 生成用户数据键
StorageKeySchema.generateUserKey(userIdentifier, dataType, subType = null)

// 解析用户数据键
StorageKeySchema.parseUserKey(key)

// 获取用户键前缀
StorageKeySchema.getUserKeyPrefix(userIdentifier)

// 数据类型常量
StorageKeySchema.DATA_TYPES.CORE          // 'core'
StorageKeySchema.DATA_TYPES.GAME_DATA     // 'gameData'
StorageKeySchema.DATA_TYPES.SETTINGS      // 'settings'
// ... 更多数据类型
```

## 🎮 使用示例

### 完整的用户管理流程

```javascript
// 1. 创建新用户
const newUser = await userManager.createUser('player001', '超级玩家');

// 2. 切换到新用户
await userManager.switchToUser('player001');

// 3. 保存游戏数据
await userStorageService.putUserData('gameData', {
    level: 10,
    score: 50000,
    achievements: ['first_win', 'perfect_score']
});

// 4. 保存用户设置
await userStorageService.putUserData('settings', {
    soundVolume: 80,
    musicVolume: 60,
    language: 'zh-CN'
}, 'audio');

// 5. 读取数据
const gameData = await userStorageService.getUserData('gameData');
const audioSettings = await userStorageService.getUserData('settings', {}, 'audio');

// 6. 切换到其他用户
await userManager.switchToUser('guest');

// 7. 验证数据隔离
const guestGameData = await userStorageService.getUserData('gameData');
// guestGameData 应该是空的或默认值，不会包含 player001 的数据
```

### 数据迁移示例

```javascript
// 系统会自动检测并迁移旧格式数据
// 旧格式：player.abc123.profile -> 新格式：abc123_core
// 旧格式：user.credential -> 新格式：{identifier}_credential

// 手动触发迁移（通常不需要）
const migrationResult = await userStorageService.migrateLegacyData('target_user');
console.log('迁移结果:', migrationResult);
```

## 🔍 故障排除

### 常见问题

1. **用户创建失败**
   - 检查用户标识符是否符合规范（只允许字母、数字、下划线、连字符）
   - 确认标识符未被使用
   - 验证显示名称长度和字符

2. **用户切换失败**
   - 确认目标用户存在
   - 检查存储服务是否正常工作
   - 查看控制台错误信息

3. **数据丢失**
   - 检查存储键是否正确使用用户前缀
   - 验证用户切换是否正确执行
   - 使用数据导出功能备份重要数据

4. **性能问题**
   - 启用用户数据缓存
   - 定期清理不活跃用户的缓存
   - 使用预加载功能提前加载常用数据

### 调试工具

```javascript
// 查看当前用户信息
console.log('当前用户:', userManager.getCurrentUser());

// 查看用户存储使用情况
const usage = await userManager.getUserStorageUsage('user123');
console.log('存储使用情况:', usage);

// 查看切换历史
const history = userSwitchManager.getSwitchHistory();
console.log('切换历史:', history);

// 查看系统状态
const status = userSwitchManager.getSwitchStatus();
console.log('系统状态:', status);
```

## 🔒 安全考虑

1. **数据隔离**：每个用户的数据完全独立，无法访问其他用户的数据
2. **标识符验证**：严格的标识符格式验证，防止注入攻击
3. **数据验证**：所有用户数据都经过结构验证
4. **错误处理**：完善的错误处理机制，防止数据损坏

## 📈 性能优化

1. **数据缓存**：用户数据缓存机制，减少存储访问
2. **延迟加载**：按需加载用户数据，提高启动速度
3. **批量操作**：支持批量数据操作，提高效率
4. **预加载**：智能预加载常用用户数据

## 🔄 迁移指南

### 从旧系统迁移

系统会自动检测并迁移以下数据：

1. **玩家数据**：`player.{id}.profile` → `{id}_core`
2. **用户凭证**：`user.credential` → `{identifier}_credential`
3. **系统设置**：`system.lastPlayer` → `system_currentUser`
4. **行为数据**：`player.behavior.data` → `{identifier}_behavior`

### 手动迁移

如果需要手动迁移特定数据：

```javascript
// 迁移特定用户的数据
const result = await userStorageService.migrateLegacyData('target_user', {
    deleteOldKeys: true  // 迁移后删除旧键
});

// 复制用户数据
await userStorageService.copyUserData('source_user', 'target_user', {
    excludeDataTypes: ['cache']  // 排除缓存数据
});
```

## 🎨 界面使用

### 用户管理界面

1. **打开方式**：
   - 点击右上角的用户切换按钮
   - 使用快捷键 `Ctrl+U`

2. **功能说明**：
   - **当前用户信息**：显示当前用户的详细信息
   - **用户列表**：显示所有用户，支持切换、编辑、删除
   - **创建新用户**：输入标识符和显示名称创建新用户
   - **用户操作**：导出/导入用户数据，查看数据统计

### 快捷操作

- `Ctrl+U`：打开用户管理界面
- `ESC`：关闭模态框
- 点击用户列表项：快速切换用户

## 🧪 测试验证

### 运行测试

1. 打开测试页面：`tests/user-management-test.html`
2. 点击"运行所有测试"按钮
3. 查看测试结果和统计信息

### 测试覆盖

- ✅ 用户数据结构验证
- ✅ 存储键值规范验证
- ✅ 用户标识符和显示名称验证
- ✅ 用户创建功能
- ✅ 用户切换功能
- ✅ 数据隔离验证
- ✅ 用户删除功能
- ✅ 数据导出导入功能
- ✅ 错误处理测试

## 📚 最佳实践

### 用户标识符命名

```javascript
// ✅ 推荐的标识符格式
'user123'
'player_001'
'zhang-san'
'test_user_2024'

// ❌ 不推荐的格式
'user@123'      // 包含特殊字符
'张三'          // 包含中文（虽然技术上可行，但不推荐）
'ab'            // 太短
'very_long_identifier_that_exceeds_limit'  // 太长
```

### 显示名称设计

```javascript
// ✅ 推荐的显示名称
'张三'
'超级玩家'
'游戏大师 2024'
'Team Alpha'

// ❌ 需要注意的情况
''              // 空名称
'   '           // 纯空白
'a'.repeat(51)  // 超长名称
```

### 数据操作模式

```javascript
// ✅ 推荐的数据操作方式
// 1. 设置当前用户
userStorageService.setCurrentUser('user123');

// 2. 操作数据（自动使用当前用户前缀）
await userStorageService.putUserData('gameData', data);
const data = await userStorageService.getUserData('gameData');

// ✅ 跨用户数据操作
await userStorageService.putUserData('gameData', data, null, 'other_user');
```

## 🔮 未来规划

### 计划中的功能

1. **用户组管理**：支持用户分组和权限管理
2. **数据同步**：跨设备用户数据同步
3. **用户分析**：用户行为分析和统计
4. **社交功能**：好友系统和数据分享
5. **云端备份**：自动云端数据备份

### 扩展接口

系统设计时考虑了扩展性，可以轻松添加：

- 新的用户类型
- 自定义数据类型
- 第三方认证集成
- 高级数据操作功能

## 📞 技术支持

如果在使用过程中遇到问题：

1. 查看浏览器控制台的错误信息
2. 运行测试页面验证系统功能
3. 检查数据结构和键值格式
4. 参考本文档的故障排除部分

---

*本文档随系统更新而持续维护，最后更新时间：2024年8月*
