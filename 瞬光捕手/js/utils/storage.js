/**
 * 瞬光捕手 - 自定义KV存储服务
 * 提供统一的键值存储接口，支持多种后端适配器
 */

class StorageService {
    constructor() {
        this.adapter = null;
        this.initialized = false;
        this.storageType = 'memory'; // 'edgeone', 'indexeddb', 'localstorage', 'memory'
        // 不在构造函数中自动调用 init()，让外部控制初始化时机
    }

    /**
     * 初始化存储服务
     * 自动选择最佳的存储适配器
     * 优先检查是否存在 EdgeOne 云存储方案
     */
    async init() {
        try {
            console.log('🗄️ 初始化存储服务...');

            // 等待 EdgeOne 存储初始化完成（如果存在）
            await this.waitForEdgeOneStorage();

            // 首先检查是否存在 edgeOneStorage 全局变量
            if (typeof window !== 'undefined' && window.edgeOneStorage) {
                console.log('🌐 检测到 edgeOneStorage，验证可用性...');
                console.log('🔍 EdgeOne 存储实例状态:', {
                    isAvailable: window.edgeOneStorage.isAvailable,
                    storageMode: window.edgeOneStorage.storageMode,
                    hasInitPromise: !!window.edgeOneStorage.initializationPromise
                });

                // 验证 EdgeOne 存储是否真正可用
                const isEdgeOneAvailable = await this.verifyEdgeOneStorage();
                if (isEdgeOneAvailable) {
                    console.log('🌐 EdgeOne 存储验证通过，使用云存储方案');
                    await this.initEdgeOneStorage();
                    this.storageType = 'edgeone';
                    console.log('✅ 使用 EdgeOne 云存储');
                    this.initialized = true;
                    console.log(`✅ 存储服务初始化完成，使用: ${this.storageType}`);
                    return;
                } else {
                    console.warn('⚠️ EdgeOne 存储验证失败，回退到本地存储');
                    console.warn('🔍 验证失败时的 EdgeOne 状态:', {
                        isAvailable: window.edgeOneStorage.isAvailable,
                        storageMode: window.edgeOneStorage.storageMode
                    });
                }
            } else {
                console.log('📝 未检测到 edgeOneStorage 全局变量');
            }

            // 如果没有云存储，使用原有逻辑
            console.log('📱 使用本地存储方案');

            // 优先尝试使用 IndexedDB
            if (this.isIndexedDBSupported()) {
                this.adapter = new IndexedDBAdapter();
                await this.adapter.init();
                this.storageType = 'indexeddb';
                console.log('存储服务：使用 IndexedDB 适配器');
            } else if (this.isLocalStorageSupported()) {
                // 回退到 localStorage
                this.adapter = new LocalStorageAdapter();
                await this.adapter.init();
                this.storageType = 'localstorage';
                console.log('存储服务：使用 localStorage 适配器');
            } else {
                // 最后回退到内存存储
                this.adapter = new MemoryAdapter();
                await this.adapter.init();
                this.storageType = 'memory';
                console.log('存储服务：使用内存适配器（数据不会持久化）');
            }
            this.initialized = true;
            console.log(`✅ 存储服务初始化完成，使用: ${this.storageType}`);

        } catch (error) {
            console.error('❌ 存储服务初始化失败:', error);
            // 使用内存适配器作为最后的回退
            this.adapter = new MemoryAdapter();
            await this.adapter.init();
            this.storageType = 'memory';
            this.initialized = true;
        }
    }

    /**
     * 等待 EdgeOne 存储初始化完成
     * 最多等待 8 秒钟，给 EdgeOne 存储更多时间完成初始化
     */
    async waitForEdgeOneStorage() {
        const maxWaitTime = 8000; // 8秒，增加等待时间
        const checkInterval = 200; // 200毫秒，减少检查频率
        let waitTime = 0;

        console.log('🔍 开始等待 EdgeOne 存储初始化...');

        while (waitTime < maxWaitTime) {
            // 检查 EdgeOne 存储是否已经初始化
            if (typeof window !== 'undefined' && window.edgeOneStorage) {
                console.log('🌐 检测到 EdgeOne 存储实例');

                // 检查是否有初始化状态信息
                if (window.edgeOneStorage.isAvailable !== null) {
                    console.log(`🌐 EdgeOne 存储初始化检测完成，可用状态: ${window.edgeOneStorage.isAvailable}`);
                    return;
                }

                // 如果有初始化 Promise，等待它完成
                if (window.edgeOneStorage.initializationPromise) {
                    console.log('🔄 等待 EdgeOne 存储异步初始化完成...');
                    try {
                        await window.edgeOneStorage.initializationPromise;
                        console.log('✅ EdgeOne 存储异步初始化完成');
                        return;
                    } catch (error) {
                        console.warn('⚠️ EdgeOne 存储异步初始化失败:', error);
                        break;
                    }
                }
            }

            // 等待一段时间后再检查
            await new Promise(resolve => setTimeout(resolve, checkInterval));
            waitTime += checkInterval;
        }

        console.log('⏰ EdgeOne 存储等待超时，继续使用本地存储');
    }

    /**
     * 验证 EdgeOne 存储是否真正可用
     */
    async verifyEdgeOneStorage() {
        try {
            if (!window.edgeOneStorage) {
                return false;
            }

            // 检查 EdgeOne 存储的可用性状态
            if (window.edgeOneStorage.isAvailable === false) {
                console.log('🌐 EdgeOne 存储标记为不可用');
                return false;
            }

            // 检查必要的方法是否存在
            const requiredMethods = ['put', 'get', 'delete'];
            for (const method of requiredMethods) {
                if (typeof window.edgeOneStorage[method] !== 'function') {
                    console.warn(`⚠️ EdgeOne 存储缺少必要方法: ${method}`);
                    return false;
                }
            }

            // 尝试进行一个简单的测试操作
            try {
                const testKey = '__storage_test__';
                const testValue = 'test';

                await window.edgeOneStorage.put(testKey, testValue);
                const retrievedValue = await window.edgeOneStorage.get(testKey);
                await window.edgeOneStorage.delete(testKey);

                if (retrievedValue === testValue) {
                    console.log('🌐 EdgeOne 存储功能测试通过');
                    return true;
                } else {
                    console.warn('⚠️ EdgeOne 存储功能测试失败：数据不匹配');
                    return false;
                }
            } catch (testError) {
                console.warn('⚠️ EdgeOne 存储功能测试失败:', testError);
                return false;
            }

        } catch (error) {
            console.warn('⚠️ EdgeOne 存储验证过程中发生错误:', error);
            return false;
        }
    }

    /**
     * 初始化 EdgeOne 存储适配器
     */
    async initEdgeOneStorage() {
        // EdgeOne 存储使用全局实例，不需要创建适配器
        // 直接使用 window.edgeOneStorage 作为存储后端
        this.adapter = {
            // 包装 EdgeOne 存储方法以符合适配器接口
            async get(key) {
                return await window.edgeOneStorage.get(key);
            },

            async set(key, value) {
                const result = await window.edgeOneStorage.put(key, value);
                return result.success;
            },

            async put(key, value) {
                try {
                    console.log(`🔍 EdgeOne 存储 put 操作，键: "${key}"`);
                    const result = await window.edgeOneStorage.put(key, value);

                    // 根据 EdgeOne 存储的响应格式处理结果
                    if (result && result.success) {
                        console.log(`✅ EdgeOne 存储 put 成功，键: "${key}"`);
                        return true;
                    } else if (result && typeof result.success === 'boolean') {
                        // 明确的成功/失败响应
                        console.log(`${result.success ? '✅' : '❌'} EdgeOne 存储 put 结果，键: "${key}", 成功: ${result.success}`);
                        return result.success;
                    } else {
                        // 如果没有明确的 success 字段，假设操作成功（某些实现可能不返回 success 字段）
                        console.log(`✅ EdgeOne 存储 put 完成（假设成功），键: "${key}"`);
                        return true;
                    }
                } catch (error) {
                    console.error(`❌ EdgeOne 存储 put 操作失败，键: "${key}":`, error);
                    return false;
                }
            },

            async remove(key) {
                const result = await window.edgeOneStorage.delete(key);
                return result.success;
            },

            async delete(key) {
                try {
                    console.log(`🔍 EdgeOne 存储 delete 操作，键: "${key}"`);
                    const result = await window.edgeOneStorage.delete(key);

                    // 根据 EdgeOne 存储的响应格式处理结果
                    if (result && result.success) {
                        console.log(`✅ EdgeOne 存储 delete 成功，键: "${key}"`);
                        return true;
                    } else if (result && typeof result.success === 'boolean') {
                        // 明确的成功/失败响应
                        console.log(`${result.success ? '✅' : '❌'} EdgeOne 存储 delete 结果，键: "${key}", 成功: ${result.success}`);
                        return result.success;
                    } else {
                        // 如果没有明确的 success 字段，假设操作成功
                        console.log(`✅ EdgeOne 存储 delete 完成（假设成功），键: "${key}"`);
                        return true;
                    }
                } catch (error) {
                    console.error(`❌ EdgeOne 存储 delete 操作失败，键: "${key}":`, error);
                    return false;
                }
            },

            async clear() {
                // EdgeOne 存储可能不支持 clear 操作
                console.warn('⚠️ EdgeOne 存储不支持 clear 操作');
                return false;
            },

            async keys() {
                try {
                    const result = await window.edgeOneStorage.list();
                    return result.keys || [];
                } catch (error) {
                    console.warn('⚠️ EdgeOne 存储 keys 操作失败:', error);
                    return [];
                }
            },

            async list(prefix = '') {
                try {
                    // 构建查询选项对象
                    const options = {};
                    if (prefix) {
                        options.prefix = prefix;
                    }

                    // 调用 EdgeOne 存储的 list 方法，传递选项对象
                    console.log(`🔍 EdgeOne 存储 list 查询，前缀: "${prefix}"`);
                    const result = await window.edgeOneStorage.list(options);

                    // 根据 functions/storage/list.js 的响应格式处理结果
                    if (result && result.success && Array.isArray(result.keys)) {
                        console.log(`✅ EdgeOne 存储 list 成功，返回 ${result.keys.length} 个键`);
                        return result.keys;
                    } else if (result && Array.isArray(result.keys)) {
                        // 兼容其他格式的响应
                        console.log(`✅ EdgeOne 存储 list 成功（兼容格式），返回 ${result.keys.length} 个键`);
                        return result.keys;
                    } else if (Array.isArray(result)) {
                        // 如果直接返回数组
                        console.log(`✅ EdgeOne 存储 list 成功（数组格式），返回 ${result.length} 个键`);
                        return result;
                    } else {
                        console.warn('⚠️ EdgeOne 存储 list 返回格式异常:', result);
                        return [];
                    }
                } catch (error) {
                    console.warn('⚠️ EdgeOne 存储 list 操作失败:', error);

                    // 降级处理：尝试使用无参数的 list 方法并手动过滤
                    try {
                        console.log('🔄 尝试使用无参数 list 方法作为降级方案...');
                        const keysResult = await window.edgeOneStorage.list();

                        if (keysResult && keysResult.success && Array.isArray(keysResult.keys)) {
                            let keys = keysResult.keys;

                            // 手动过滤前缀
                            if (prefix) {
                                keys = keys.filter(key => key.startsWith(prefix));
                                console.log(`🔄 降级方案：手动过滤后返回 ${keys.length} 个键`);
                            }

                            return keys;
                        } else {
                            console.error('⚠️ EdgeOne 存储降级方案返回格式异常:', keysResult);
                            return [];
                        }
                    } catch (fallbackError) {
                        console.error('⚠️ EdgeOne 存储降级方案也失败:', fallbackError);
                        return [];
                    }
                }
            },

            async getStorageInfo() {
                try {
                    // EdgeOne 存储可能不提供存储信息 API
                    // 返回基本的存储信息
                    console.log('🔍 EdgeOne 存储 getStorageInfo 查询');

                    // 尝试获取键的数量作为使用情况的指标
                    const keysResult = await window.edgeOneStorage.list();
                    let keyCount = 0;

                    if (keysResult && keysResult.success && Array.isArray(keysResult.keys)) {
                        keyCount = keysResult.keys.length;
                    } else if (keysResult && Array.isArray(keysResult.keys)) {
                        keyCount = keysResult.keys.length;
                    }

                    const storageInfo = {
                        used: keyCount, // 使用键的数量作为使用情况
                        available: Infinity, // 云存储通常有很大的容量
                        type: 'EdgeOne Cloud Storage',
                        keyCount: keyCount,
                        storageMode: window.edgeOneStorage.storageMode || 'remote'
                    };

                    console.log('✅ EdgeOne 存储 getStorageInfo 成功:', storageInfo);
                    return storageInfo;
                } catch (error) {
                    console.warn('⚠️ EdgeOne 存储 getStorageInfo 操作失败:', error);

                    // 返回默认的存储信息
                    return {
                        used: 0,
                        available: Infinity,
                        type: 'EdgeOne Cloud Storage (Error)',
                        keyCount: 0,
                        storageMode: 'unknown'
                    };
                }
            }
        };
    }

    /**
     * 检查 IndexedDB 是否支持
     */
    isIndexedDBSupported() {
        return typeof window !== 'undefined' && 
               'indexedDB' in window && 
               indexedDB !== null;
    }

    /**
     * 检查 localStorage 是否支持
     */
    isLocalStorageSupported() {
        try {
            if (typeof window === 'undefined' || !window.localStorage) {
                return false;
            }
            const testKey = '__storage_test__';
            window.localStorage.setItem(testKey, 'test');
            window.localStorage.removeItem(testKey);
            return true;
        } catch (e) {
            return false;
        }
    }

    /**
     * 确保存储服务已初始化
     */
    async ensureInitialized() {
        if (!this.initialized) {
            await this.init();
        }
    }

    /**
     * 保存数据
     * @param {string} key - 键名
     * @param {any} value - 值
     * @returns {Promise<boolean>} 是否成功
     */
    async put(key, value) {
        await this.ensureInitialized();
        try {
            return await this.adapter.put(key, value);
        } catch (error) {
            console.error(`存储数据失败 [${key}]:`, error);
            return false;
        }
    }

    /**
     * 读取数据
     * @param {string} key - 键名
     * @param {any} defaultValue - 默认值
     * @returns {Promise<any>} 数据值
     */
    async get(key, defaultValue = null) {
        await this.ensureInitialized();
        try {
            const result = await this.adapter.get(key);
            return result !== null ? result : defaultValue;
        } catch (error) {
            console.error(`读取数据失败 [${key}]:`, error);
            return defaultValue;
        }
    }

    /**
     * 删除数据
     * @param {string} key - 键名
     * @returns {Promise<boolean>} 是否成功
     */
    async delete(key) {
        await this.ensureInitialized();
        try {
            return await this.adapter.delete(key);
        } catch (error) {
            console.error(`删除数据失败 [${key}]:`, error);
            return false;
        }
    }

    /**
     * 列出指定前缀的所有键
     * @param {string} prefix - 前缀
     * @returns {Promise<string[]>} 键名列表
     */
    async list(prefix = '') {
        await this.ensureInitialized();
        try {
            return await this.adapter.list(prefix);
        } catch (error) {
            console.error(`列出键失败 [${prefix}]:`, error);
            return [];
        }
    }

    /**
     * 清空所有数据
     * @returns {Promise<boolean>} 是否成功
     */
    async clear() {
        await this.ensureInitialized();
        try {
            return await this.adapter.clear();
        } catch (error) {
            console.error('清空数据失败:', error);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     * @returns {Promise<object>} 存储信息
     */
    async getStorageInfo() {
        await this.ensureInitialized();
        try {
            return await this.adapter.getStorageInfo();
        } catch (error) {
            console.error('获取存储信息失败:', error);
            return { used: 0, available: 0, type: 'unknown' };
        }
    }
}

/**
 * IndexedDB 适配器
 */
class IndexedDBAdapter {
    constructor() {
        this.dbName = 'SplitSecondSparkDB';
        this.dbVersion = 1;
        this.storeName = 'gameData';
        this.db = null;
    }

    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.dbVersion);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve();
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains(this.storeName)) {
                    db.createObjectStore(this.storeName, { keyPath: 'key' });
                }
            };
        });
    }

    async put(key, value) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('IndexedDB not initialized'));
                return;
            }
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.put({ 
                key: key, 
                value: value, 
                timestamp: Date.now() 
            });
            
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    async get(key) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('IndexedDB not initialized'));
                return;
            }
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.get(key);
            
            request.onsuccess = () => {
                const result = request.result;
                resolve(result ? result.value : null);
            };
            request.onerror = () => reject(request.error);
        });
    }

    async delete(key) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('IndexedDB not initialized'));
                return;
            }
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.delete(key);
            
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    async list(prefix) {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('IndexedDB not initialized'));
                return;
            }
            const transaction = this.db.transaction([this.storeName], 'readonly');
            const store = transaction.objectStore(this.storeName);
            const request = store.getAllKeys();
            
            request.onsuccess = () => {
                const keys = request.result.filter(key => key.startsWith(prefix));
                resolve(keys);
            };
            request.onerror = () => reject(request.error);
        });
    }

    async clear() {
        return new Promise((resolve, reject) => {
            if (!this.db) {
                reject(new Error('IndexedDB not initialized'));
                return;
            }
            const transaction = this.db.transaction([this.storeName], 'readwrite');
            const store = transaction.objectStore(this.storeName);
            const request = store.clear();
            
            request.onsuccess = () => resolve(true);
            request.onerror = () => reject(request.error);
        });
    }

    async getStorageInfo() {
        if ('storage' in navigator && 'estimate' in navigator.storage) {
            const estimate = await navigator.storage.estimate();
            return {
                used: estimate.usage || 0,
                available: estimate.quota || 0,
                type: 'IndexedDB'
            };
        }
        return { used: 0, available: 0, type: 'IndexedDB' };
    }
}

/**
 * localStorage 适配器
 */
class LocalStorageAdapter {
    constructor() {
        this.prefix = 'sss_'; // Split-Second Spark prefix
    }

    async init() {
        // localStorage 不需要异步初始化
        return Promise.resolve();
    }

    async put(key, value) {
        try {
            const data = {
                value: value,
                timestamp: Date.now()
            };
            localStorage.setItem(this.prefix + key, JSON.stringify(data));
            return true;
        } catch (error) {
            console.error('localStorage 存储失败:', error);
            return false;
        }
    }

    async get(key) {
        try {
            const item = localStorage.getItem(this.prefix + key);
            if (item === null) return null;
            
            const data = JSON.parse(item);
            return data.value;
        } catch (error) {
            console.error('localStorage 读取失败:', error);
            return null;
        }
    }

    async delete(key) {
        try {
            localStorage.removeItem(this.prefix + key);
            return true;
        } catch (error) {
            console.error('localStorage 删除失败:', error);
            return false;
        }
    }

    async list(prefix) {
        try {
            const keys = [];
            const fullPrefix = this.prefix + prefix;
            
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(fullPrefix)) {
                    keys.push(key.substring(this.prefix.length));
                }
            }
            return keys;
        } catch (error) {
            console.error('localStorage 列表失败:', error);
            return [];
        }
    }

    async clear() {
        try {
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.prefix)) {
                    keysToRemove.push(key);
                }
            }
            
            keysToRemove.forEach(key => localStorage.removeItem(key));
            return true;
        } catch (error) {
            console.error('localStorage 清空失败:', error);
            return false;
        }
    }

    async getStorageInfo() {
        try {
            let used = 0;
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key.startsWith(this.prefix)) {
                    used += key.length + (localStorage.getItem(key) || '').length;
                }
            }
            
            // localStorage 通常限制为 5-10MB
            const available = 5 * 1024 * 1024; // 假设 5MB
            return { used, available, type: 'localStorage' };
        } catch (error) {
            return { used: 0, available: 0, type: 'localStorage' };
        }
    }
}

/**
 * 内存适配器（临时存储，页面刷新后丢失）
 */
class MemoryAdapter {
    constructor() {
        this.data = new Map();
    }

    async init() {
        return Promise.resolve();
    }

    async put(key, value) {
        this.data.set(key, {
            value: value,
            timestamp: Date.now()
        });
        return true;
    }

    async get(key) {
        const item = this.data.get(key);
        return item ? item.value : null;
    }

    async delete(key) {
        return this.data.delete(key);
    }

    async list(prefix) {
        const keys = [];
        for (const key of this.data.keys()) {
            if (key.startsWith(prefix)) {
                keys.push(key);
            }
        }
        return keys;
    }

    async clear() {
        this.data.clear();
        return true;
    }

    async getStorageInfo() {
        const used = JSON.stringify([...this.data.entries()]).length;
        return { used, available: Infinity, type: 'Memory' };
    }

    /**
     * 检查是否可以升级到 EdgeOne 存储
     */
    async checkForEdgeOneUpgrade() {
        // 如果已经是 EdgeOne 存储，无需升级
        if (this.storageType === 'edgeone') {
            return false;
        }

        // 检查 EdgeOne 存储是否现在可用
        if (typeof window !== 'undefined' && window.edgeOneStorage) {
            const isAvailable = await this.verifyEdgeOneStorage();
            if (isAvailable) {
                console.log('🌐 检测到 EdgeOne 存储现在可用，准备升级...');
                return true;
            }
        }

        return false;
    }

    /**
     * 升级到 EdgeOne 存储
     */
    async upgradeToEdgeOne() {
        try {
            console.log('🚀 开始升级到 EdgeOne 存储...');

            // 验证 EdgeOne 存储可用性
            const isAvailable = await this.verifyEdgeOneStorage();
            if (!isAvailable) {
                console.warn('⚠️ EdgeOne 存储不可用，无法升级');
                return false;
            }

            // 备份当前数据（如果需要）
            const currentData = await this.exportAllData();

            // 初始化 EdgeOne 存储
            await this.initEdgeOneStorage();
            this.storageType = 'edgeone';

            // 迁移数据（如果有）
            if (currentData && Object.keys(currentData).length > 0) {
                console.log('🔄 迁移现有数据到 EdgeOne 存储...');
                for (const [key, value] of Object.entries(currentData)) {
                    try {
                        await this.set(key, value);
                    } catch (error) {
                        console.warn(`⚠️ 迁移数据失败 (${key}):`, error);
                    }
                }
                console.log('✅ 数据迁移完成');
            }

            console.log('✅ 成功升级到 EdgeOne 存储');
            return true;

        } catch (error) {
            console.error('❌ 升级到 EdgeOne 存储失败:', error);
            return false;
        }
    }

    /**
     * 导出所有数据
     */
    async exportAllData() {
        try {
            if (!this.initialized || !this.adapter) {
                return {};
            }

            const data = {};

            if (this.adapter.keys && typeof this.adapter.keys === 'function') {
                const keys = await this.adapter.keys();
                for (const key of keys) {
                    try {
                        const value = await this.get(key);
                        if (value !== null) {
                            data[key] = value;
                        }
                    } catch (error) {
                        console.warn(`⚠️ 导出数据失败 (${key}):`, error);
                    }
                }
            }

            return data;
        } catch (error) {
            console.warn('⚠️ 导出数据过程中发生错误:', error);
            return {};
        }
    }
}

// 创建全局存储服务实例
window.storageService = new StorageService();
