/**
 * 瞬光捕手 - 模块诊断工具
 * 用于检查和修复模块初始化问题
 */

class ModuleDiagnostics {
    constructor() {
        this.moduleChecks = new Map();
        this.initializationOrder = [
            'storageService',
            'i18nService', 
            'difficultyConfigManager',
            'userCredentialSystem',
            'playerManager',
            'levelManager',
            'leaderboardManager',
            'difficultyLeaderboardManager',
            'gameEngine',
            'levelEditor',
            'screenManager',
            'inputHandler'
        ];
    }

    /**
     * 运行完整的模块诊断
     */
    async runFullDiagnostics() {
        console.log('🔍 开始模块诊断...');
        
        const results = {
            timestamp: new Date().toISOString(),
            totalModules: this.initializationOrder.length,
            passedChecks: 0,
            failedChecks: 0,
            warnings: 0,
            details: []
        };

        for (const moduleName of this.initializationOrder) {
            const checkResult = await this.checkModule(moduleName);
            results.details.push(checkResult);
            
            if (checkResult.status === 'passed') {
                results.passedChecks++;
            } else if (checkResult.status === 'failed') {
                results.failedChecks++;
            } else if (checkResult.status === 'warning') {
                results.warnings++;
            }
        }

        this.displayDiagnosticsResults(results);
        return results;
    }

    /**
     * 检查单个模块
     * @param {string} moduleName - 模块名称
     */
    async checkModule(moduleName) {
        const result = {
            module: moduleName,
            status: 'unknown',
            issues: [],
            suggestions: [],
            details: {}
        };

        try {
            const moduleInstance = window[moduleName];
            
            // 检查模块是否存在
            if (!moduleInstance) {
                result.status = 'failed';
                result.issues.push('模块实例不存在');
                result.suggestions.push('检查模块是否正确加载');
                return result;
            }

            // 检查模块是否有 init 方法
            if (typeof moduleInstance.init !== 'function') {
                result.status = 'warning';
                result.issues.push('模块没有 init 方法');
                result.suggestions.push('确认模块是否需要初始化');
            }

            // 检查模块是否有 initialized 属性
            if (!moduleInstance.hasOwnProperty('initialized')) {
                result.status = 'warning';
                result.issues.push('模块缺少 initialized 属性');
                result.suggestions.push('在模块构造函数中添加 this.initialized = false');
            }

            // 检查初始化状态
            if (moduleInstance.initialized !== true) {
                result.status = 'failed';
                result.issues.push(`初始化状态异常: ${moduleInstance.initialized}`);
                result.suggestions.push('在 init 方法成功完成后设置 this.initialized = true');
            }

            // 模块特定检查
            await this.runModuleSpecificChecks(moduleName, moduleInstance, result);

            // 如果没有问题，标记为通过
            if (result.issues.length === 0) {
                result.status = 'passed';
            }

        } catch (error) {
            result.status = 'failed';
            result.issues.push(`检查过程中出错: ${error.message}`);
            result.suggestions.push('检查模块代码是否有语法错误');
        }

        return result;
    }

    /**
     * 运行模块特定的检查
     * @param {string} moduleName - 模块名称
     * @param {Object} moduleInstance - 模块实例
     * @param {Object} result - 检查结果对象
     */
    async runModuleSpecificChecks(moduleName, moduleInstance, result) {
        switch (moduleName) {
            case 'levelEditor':
                await this.checkLevelEditor(moduleInstance, result);
                break;
                
            case 'difficultyConfigManager':
                await this.checkDifficultyConfigManager(moduleInstance, result);
                break;
                
            case 'userCredentialSystem':
                await this.checkUserCredentialSystem(moduleInstance, result);
                break;
                
            case 'difficultyLeaderboardManager':
                await this.checkDifficultyLeaderboardManager(moduleInstance, result);
                break;
        }
    }

    /**
     * 检查关卡编辑器特定问题
     */
    async checkLevelEditor(moduleInstance, result) {
        // 检查画布元素
        const canvas = document.getElementById('level-editor-canvas');
        if (!canvas) {
            result.issues.push('找不到关卡编辑器画布元素');
            result.suggestions.push('确保 HTML 中存在 id="level-editor-canvas" 的元素');
        }

        // 检查难度配置集成
        if (!moduleInstance.currentDifficultyConfig) {
            result.issues.push('未加载难度配置');
            result.suggestions.push('确保难度配置管理器在关卡编辑器之前初始化');
        }

        // 检查光点类型配置
        if (moduleInstance.sparkTypes) {
            for (const [type, config] of Object.entries(moduleInstance.sparkTypes)) {
                if (!config.hasOwnProperty('baseSize')) {
                    result.issues.push(`光点类型 ${type} 缺少 baseSize 属性`);
                    result.suggestions.push('将 size 属性重命名为 baseSize');
                }
            }
        }

        result.details.levelEditor = {
            hasCanvas: !!canvas,
            hasDifficultyConfig: !!moduleInstance.currentDifficultyConfig,
            sparkTypesCount: moduleInstance.sparkTypes ? Object.keys(moduleInstance.sparkTypes).length : 0
        };
    }

    /**
     * 检查难度配置管理器
     */
    async checkDifficultyConfigManager(moduleInstance, result) {
        // 检查难度等级
        const difficulties = moduleInstance.getAvailableDifficulties();
        if (!difficulties || difficulties.length === 0) {
            result.issues.push('没有可用的难度等级');
            result.suggestions.push('检查难度配置是否正确加载');
        }

        // 检查当前难度
        const currentDifficulty = moduleInstance.getCurrentDifficulty();
        if (!currentDifficulty) {
            result.issues.push('当前难度未设置');
            result.suggestions.push('设置默认难度');
        }

        result.details.difficultyConfigManager = {
            difficultiesCount: difficulties ? difficulties.length : 0,
            currentDifficulty: currentDifficulty
        };
    }

    /**
     * 检查用户凭证系统
     */
    async checkUserCredentialSystem(moduleInstance, result) {
        // 检查凭证类型
        const credentialTypes = moduleInstance.getSupportedCredentialTypes();
        if (!credentialTypes || credentialTypes.length === 0) {
            result.issues.push('没有支持的凭证类型');
            result.suggestions.push('检查凭证类型配置');
        }

        result.details.userCredentialSystem = {
            credentialTypesCount: credentialTypes ? credentialTypes.length : 0,
            hasCurrentCredential: !!moduleInstance.getCurrentCredential()
        };
    }

    /**
     * 检查难度排行榜管理器
     */
    async checkDifficultyLeaderboardManager(moduleInstance, result) {
        // 检查支持的难度
        const difficulties = moduleInstance.getSupportedDifficulties();
        if (!difficulties || difficulties.length === 0) {
            result.issues.push('没有支持的难度等级');
            result.suggestions.push('检查难度等级配置');
        }

        // 检查排行榜类别
        const categories = moduleInstance.getSupportedCategories();
        if (!categories || Object.keys(categories).length === 0) {
            result.issues.push('没有支持的排行榜类别');
            result.suggestions.push('检查排行榜类别配置');
        }

        result.details.difficultyLeaderboardManager = {
            difficultiesCount: difficulties ? difficulties.length : 0,
            categoriesCount: categories ? Object.keys(categories).length : 0
        };
    }

    /**
     * 显示诊断结果
     */
    displayDiagnosticsResults(results) {
        console.log('\n📊 模块诊断结果:');
        console.log(`总模块数: ${results.totalModules}`);
        console.log(`✅ 通过: ${results.passedChecks}`);
        console.log(`⚠️ 警告: ${results.warnings}`);
        console.log(`❌ 失败: ${results.failedChecks}`);
        console.log('\n详细结果:');

        results.details.forEach(detail => {
            const statusIcon = this.getStatusIcon(detail.status);
            console.log(`${statusIcon} ${detail.module}`);
            
            if (detail.issues.length > 0) {
                detail.issues.forEach(issue => {
                    console.log(`  ❌ ${issue}`);
                });
            }
            
            if (detail.suggestions.length > 0) {
                detail.suggestions.forEach(suggestion => {
                    console.log(`  💡 ${suggestion}`);
                });
            }
        });

        // 生成修复建议
        this.generateFixSuggestions(results);
    }

    /**
     * 获取状态图标
     */
    getStatusIcon(status) {
        switch (status) {
            case 'passed': return '✅';
            case 'warning': return '⚠️';
            case 'failed': return '❌';
            default: return '❓';
        }
    }

    /**
     * 生成修复建议
     */
    generateFixSuggestions(results) {
        console.log('\n🔧 修复建议:');

        const failedModules = results.details.filter(d => d.status === 'failed');
        const warningModules = results.details.filter(d => d.status === 'warning');

        if (failedModules.length > 0) {
            console.log('\n优先修复 (失败的模块):');
            failedModules.forEach(module => {
                console.log(`1. ${module.module}:`);
                module.suggestions.forEach(suggestion => {
                    console.log(`   - ${suggestion}`);
                });
            });
        }

        if (warningModules.length > 0) {
            console.log('\n建议修复 (警告的模块):');
            warningModules.forEach(module => {
                console.log(`2. ${module.module}:`);
                module.suggestions.forEach(suggestion => {
                    console.log(`   - ${suggestion}`);
                });
            });
        }

        // 通用修复建议
        console.log('\n通用修复步骤:');
        console.log('1. 确保所有模块文件已正确加载');
        console.log('2. 检查模块初始化顺序');
        console.log('3. 确保每个模块的 init 方法设置 this.initialized = true');
        console.log('4. 检查模块间的依赖关系');
    }

    /**
     * 自动修复常见问题
     */
    async autoFix() {
        console.log('🔧 开始自动修复...');

        // 修复 levelEditor 的 initialized 属性
        if (window.levelEditor && !window.levelEditor.hasOwnProperty('initialized')) {
            window.levelEditor.initialized = false;
            console.log('✅ 已为 levelEditor 添加 initialized 属性');
        }

        // 修复其他模块的 initialized 属性
        const modules = ['difficultyConfigManager', 'userCredentialSystem', 'difficultyLeaderboardManager'];
        modules.forEach(moduleName => {
            const module = window[moduleName];
            if (module && !module.hasOwnProperty('initialized')) {
                module.initialized = false;
                console.log(`✅ 已为 ${moduleName} 添加 initialized 属性`);
            }
        });

        console.log('🔧 自动修复完成');
    }

    /**
     * 导出诊断报告
     */
    exportDiagnosticsReport(results) {
        const report = {
            timestamp: results.timestamp,
            summary: {
                totalModules: results.totalModules,
                passedChecks: results.passedChecks,
                failedChecks: results.failedChecks,
                warnings: results.warnings
            },
            details: results.details,
            systemInfo: {
                userAgent: navigator.userAgent,
                url: window.location.href,
                timestamp: new Date().toISOString()
            }
        };

        const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `module-diagnostics-${Date.now()}.json`;
        a.click();
        URL.revokeObjectURL(url);

        console.log('📄 诊断报告已导出');
    }
}

// 创建全局诊断工具实例
const moduleDiagnostics = new ModuleDiagnostics();

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.ModuleDiagnostics = ModuleDiagnostics;
    window.moduleDiagnostics = moduleDiagnostics;
    
    // 添加便捷的全局函数
    window.runDiagnostics = () => moduleDiagnostics.runFullDiagnostics();
    window.autoFixModules = () => moduleDiagnostics.autoFix();
}

// 支持CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        ModuleDiagnostics,
        moduleDiagnostics
    };
}
