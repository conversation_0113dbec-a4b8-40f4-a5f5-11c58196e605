/**
 * 瞬光捕手 - 用户存储服务
 * 基于用户标识符的存储键值规范化服务
 * 确保不同用户的数据完全隔离
 */

class UserStorageService {
    constructor(baseStorageService) {
        this.baseStorage = baseStorageService;
        this.currentUserIdentifier = null;
        this.initialized = false;
        
        console.log('🗄️ 用户存储服务已创建');
    }

    /**
     * 初始化用户存储服务
     */
    async init() {
        try {
            // 确保基础存储服务已初始化
            if (!this.baseStorage.initialized) {
                await this.baseStorage.init();
            }
            
            this.initialized = true;
            console.log('✅ 用户存储服务初始化完成');
            
        } catch (error) {
            console.error('❌ 用户存储服务初始化失败:', error);
            throw error;
        }
    }

    /**
     * 验证用户标识符的有效性
     * @param {string} userIdentifier - 用户标识符
     * @returns {boolean} 是否有效
     */
    isValidUserIdentifier(userIdentifier) {
        if (!userIdentifier || typeof userIdentifier !== 'string') {
            return false;
        }

        // 检查格式（只允许字母、数字、下划线、连字符）
        if (!/^[a-zA-Z0-9_-]+$/.test(userIdentifier)) {
            return false;
        }

        // 检查长度
        if (userIdentifier.length < 1 || userIdentifier.length > 64) {
            return false;
        }

        return true;
    }

    /**
     * 安全设置当前用户标识符 - 清除之前的用户数据缓存
     * @param {string} userIdentifier - 用户标识符
     */
    setCurrentUser(userIdentifier) {
        // 验证用户标识符的有效性
        if (!this.isValidUserIdentifier(userIdentifier)) {
            console.error('🔐 访问控制：无效的用户标识符，拒绝设置');
            throw new Error('无效的用户标识符');
        }

        // 如果切换到不同的用户，清除缓存
        if (this.currentUserIdentifier && this.currentUserIdentifier !== userIdentifier) {
            console.log('🧹 切换用户，清除之前的数据缓存');
            this.clearUserDataCache();
        }

        this.currentUserIdentifier = userIdentifier;
        console.log(`👤 当前用户已安全设置: ${userIdentifier}`);
    }

    /**
     * 清除用户数据缓存
     */
    clearUserDataCache() {
        // 这里可以添加清除缓存的逻辑
        // 例如清除内存中的用户数据缓存
        console.log('🧹 用户数据缓存已清除');
    }

    /**
     * 获取当前用户标识符
     * @returns {string|null} 当前用户标识符
     */
    getCurrentUser() {
        return this.currentUserIdentifier;
    }

    /**
     * 安全保存用户数据 - 带访问控制验证
     * @param {string} dataType - 数据类型
     * @param {any} value - 数据值
     * @param {string} subType - 子类型（可选）
     * @param {string} userIdentifier - 用户标识符（可选，默认使用当前用户）
     * @returns {Promise<boolean>} 是否成功
     */
    async putUserData(dataType, value, subType = null, userIdentifier = null) {
        const userId = userIdentifier || this.currentUserIdentifier;

        // 严格的用户标识符验证
        if (!userId) {
            console.error('🔐 访问控制：未设置用户标识符，拒绝保存数据');
            throw new Error('未设置用户标识符，无法保存用户数据');
        }

        // 验证用户是否有权限访问指定用户的数据
        if (!this.validateUserAccess(userId)) {
            console.error('🔐 访问控制：用户无权限访问指定用户数据');
            throw new Error('用户无权限访问指定用户数据');
        }

        const key = StorageKeySchema.generateUserKey(userId, dataType, subType);
        console.log(`💾 安全保存用户数据: ${key}`);

        return await this.baseStorage.put(key, value);
    }

    /**
     * 安全读取用户数据 - 带访问控制验证
     * @param {string} dataType - 数据类型
     * @param {any} defaultValue - 默认值
     * @param {string} subType - 子类型（可选）
     * @param {string} userIdentifier - 用户标识符（可选，默认使用当前用户）
     * @returns {Promise<any>} 数据值
     */
    async getUserData(dataType, defaultValue = null, subType = null, userIdentifier = null) {
        const userId = userIdentifier || this.currentUserIdentifier;

        // 严格的用户标识符验证
        if (!userId) {
            console.warn('🔐 访问控制：未设置用户标识符，返回默认值');
            return defaultValue;
        }

        // 验证用户是否有权限访问指定用户的数据
        if (!this.validateUserAccess(userId)) {
            console.error('🔐 访问控制：用户无权限访问指定用户数据');
            return defaultValue;
        }

        const key = StorageKeySchema.generateUserKey(userId, dataType, subType);
        return await this.baseStorage.get(key, defaultValue);
    }

    /**
     * 验证用户访问权限
     * @param {string} targetUserId - 目标用户标识符
     * @returns {boolean} 是否有权限访问
     */
    validateUserAccess(targetUserId) {
        // 只能访问当前用户的数据
        if (targetUserId !== this.currentUserIdentifier) {
            console.warn(`🔐 访问控制：尝试访问其他用户数据 ${targetUserId}，当前用户 ${this.currentUserIdentifier}`);
            return false;
        }

        // 验证当前用户标识符的有效性
        if (!this.isValidUserIdentifier(targetUserId)) {
            console.warn(`🔐 访问控制：无效的用户标识符 ${targetUserId}`);
            return false;
        }

        return true;
    }

    /**
     * 删除用户数据
     * @param {string} dataType - 数据类型
     * @param {string} subType - 子类型（可选）
     * @param {string} userIdentifier - 用户标识符（可选，默认使用当前用户）
     * @returns {Promise<boolean>} 是否成功
     */
    async deleteUserData(dataType, subType = null, userIdentifier = null) {
        const userId = userIdentifier || this.currentUserIdentifier;

        // 严格的用户标识符验证
        if (!userId) {
            console.error('🔐 访问控制：未设置用户标识符，拒绝删除数据');
            throw new Error('未设置用户标识符，无法删除用户数据');
        }

        // 验证用户是否有权限访问指定用户的数据
        if (!this.validateUserAccess(userId)) {
            console.error('🔐 访问控制：用户无权限删除指定用户数据');
            throw new Error('用户无权限删除指定用户数据');
        }

        const key = StorageKeySchema.generateUserKey(userId, dataType, subType);
        console.log(`🗑️ 安全删除用户数据: ${key}`);

        return await this.baseStorage.delete(key);
    }

    /**
     * 列出用户的所有数据键
     * @param {string} userIdentifier - 用户标识符（可选，默认使用当前用户）
     * @returns {Promise<Array>} 数据键列表
     */
    async listUserData(userIdentifier = null) {
        const userId = userIdentifier || this.currentUserIdentifier;
        if (!userId) {
            return [];
        }
        
        const keyPrefix = StorageKeySchema.getUserKeyPrefix(userId);
        return await this.baseStorage.list(keyPrefix);
    }

    /**
     * 清空用户的所有数据
     * @param {string} userIdentifier - 用户标识符
     * @returns {Promise<boolean>} 是否成功
     */
    async clearUserData(userIdentifier) {
        try {
            const userKeys = await this.listUserData(userIdentifier);
            
            for (const key of userKeys) {
                await this.baseStorage.delete(key);
            }
            
            console.log(`🧹 清空用户数据完成: ${userIdentifier} (${userKeys.length} 个键)`);
            return true;
            
        } catch (error) {
            console.error(`❌ 清空用户数据失败: ${userIdentifier}`, error);
            return false;
        }
    }

    /**
     * 复制用户数据到另一个用户
     * @param {string} sourceIdentifier - 源用户标识符
     * @param {string} targetIdentifier - 目标用户标识符
     * @param {Object} options - 复制选项
     * @returns {Promise<boolean>} 是否成功
     */
    async copyUserData(sourceIdentifier, targetIdentifier, options = {}) {
        try {
            console.log(`📋 复制用户数据: ${sourceIdentifier} -> ${targetIdentifier}`);
            
            const sourceKeys = await this.listUserData(sourceIdentifier);
            let copiedCount = 0;
            
            for (const sourceKey of sourceKeys) {
                const keyInfo = StorageKeySchema.parseUserKey(sourceKey);
                if (!keyInfo) continue;
                
                // 跳过某些数据类型（如果指定）
                if (options.excludeDataTypes && options.excludeDataTypes.includes(keyInfo.dataType)) {
                    continue;
                }
                
                // 读取源数据
                const data = await this.baseStorage.get(sourceKey);
                if (data === null) continue;
                
                // 生成目标键
                const targetKey = StorageKeySchema.generateUserKey(
                    targetIdentifier, 
                    keyInfo.dataType, 
                    keyInfo.subType
                );
                
                // 保存到目标用户
                await this.baseStorage.put(targetKey, data);
                copiedCount++;
            }
            
            console.log(`✅ 用户数据复制完成: ${copiedCount} 个数据项`);
            return true;
            
        } catch (error) {
            console.error(`❌ 用户数据复制失败: ${sourceIdentifier} -> ${targetIdentifier}`, error);
            return false;
        }
    }

    /**
     * 迁移旧格式数据到新格式
     * @param {string} userIdentifier - 目标用户标识符
     * @param {Object} options - 迁移选项
     * @returns {Promise<Object>} 迁移结果
     */
    async migrateLegacyData(userIdentifier, options = {}) {
        try {
            console.log(`🔄 迁移旧格式数据到用户: ${userIdentifier}`);
            
            const migrationResult = {
                success: false,
                migratedKeys: [],
                skippedKeys: [],
                errors: []
            };
            
            // 获取所有存储键
            const allKeys = await this.baseStorage.list('');
            
            for (const key of allKeys) {
                try {
                    // 检查是否为旧格式键
                    if (DataMigrationSchema.isLegacyKey(key)) {
                        // 转换为新格式键
                        const newKey = DataMigrationSchema.convertLegacyKey(key, userIdentifier);
                        
                        if (newKey) {
                            // 读取旧数据
                            const data = await this.baseStorage.get(key);
                            
                            if (data !== null) {
                                // 保存到新键
                                await this.baseStorage.put(newKey, data);
                                migrationResult.migratedKeys.push({ oldKey: key, newKey: newKey });
                                
                                // 删除旧键（如果指定）
                                if (options.deleteOldKeys) {
                                    await this.baseStorage.delete(key);
                                }
                            }
                        } else {
                            migrationResult.skippedKeys.push(key);
                        }
                    }
                } catch (error) {
                    migrationResult.errors.push({ key: key, error: error.message });
                }
            }
            
            migrationResult.success = migrationResult.errors.length === 0;
            
            console.log(`✅ 数据迁移完成: ${migrationResult.migratedKeys.length} 个键已迁移`);
            if (migrationResult.errors.length > 0) {
                console.warn(`⚠️ 迁移过程中出现 ${migrationResult.errors.length} 个错误`);
            }
            
            return migrationResult;
            
        } catch (error) {
            console.error(`❌ 数据迁移失败: ${userIdentifier}`, error);
            throw error;
        }
    }

    /**
     * 获取用户数据统计信息
     * @param {string} userIdentifier - 用户标识符
     * @returns {Promise<Object>} 统计信息
     */
    async getUserDataStats(userIdentifier) {
        try {
            const userKeys = await this.listUserData(userIdentifier);
            const stats = {
                totalKeys: userKeys.length,
                dataTypes: {},
                totalSize: 0,
                lastUpdated: 0
            };
            
            for (const key of userKeys) {
                const keyInfo = StorageKeySchema.parseUserKey(key);
                const data = await this.baseStorage.get(key);
                
                if (keyInfo && data !== null) {
                    // 统计数据类型
                    if (!stats.dataTypes[keyInfo.dataType]) {
                        stats.dataTypes[keyInfo.dataType] = 0;
                    }
                    stats.dataTypes[keyInfo.dataType]++;
                    
                    // 计算数据大小
                    const dataSize = JSON.stringify(data).length;
                    stats.totalSize += dataSize;
                    
                    // 更新最后更新时间
                    if (data.lastActiveAt && data.lastActiveAt > stats.lastUpdated) {
                        stats.lastUpdated = data.lastActiveAt;
                    }
                }
            }
            
            return stats;
            
        } catch (error) {
            console.error(`❌ 获取用户数据统计失败: ${userIdentifier}`, error);
            return null;
        }
    }

    /**
     * 直接访问基础存储服务（用于系统级操作）
     * @param {string} key - 存储键
     * @param {any} value - 值（可选，如果提供则为保存操作）
     * @returns {Promise<any>} 读取的值或保存结果
     */
    async systemStorage(key, value = undefined) {
        if (value !== undefined) {
            // 保存操作
            return await this.baseStorage.put(key, value);
        } else {
            // 读取操作
            return await this.baseStorage.get(key);
        }
    }
}

// 创建全局用户存储服务实例
if (typeof window !== 'undefined') {
    window.UserStorageService = UserStorageService;
}

// 模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserStorageService;
}
