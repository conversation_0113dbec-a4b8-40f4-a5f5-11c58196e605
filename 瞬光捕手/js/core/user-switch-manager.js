/**
 * 瞬光捕手 - 用户切换管理器
 * 负责用户切换时的数据加载、保存和状态管理
 * 确保切换过程中数据的完整性和一致性
 */

class UserSwitchManager {
    constructor(userManager, userStorageService) {
        this.userManager = userManager;
        this.userStorageService = userStorageService;
        this.switchInProgress = false;
        this.switchHistory = [];
        this.maxHistorySize = 10;
        
        // 数据缓存，用于快速切换
        this.userDataCache = new Map();
        this.cacheTimeout = 5 * 60 * 1000; // 5分钟缓存超时
        
        console.log('🔄 用户切换管理器已创建');
    }

    /**
     * 执行用户切换
     * @param {string} targetIdentifier - 目标用户标识符
     * @param {Object} options - 切换选项
     * @returns {Promise<Object>} 切换结果
     */
    async switchUser(targetIdentifier, options = {}) {
        if (this.switchInProgress) {
            throw new Error('用户切换正在进行中，请稍后再试');
        }

        this.switchInProgress = true;
        const switchStartTime = Date.now();
        
        try {
            console.log(`🔄 开始用户切换: ${targetIdentifier}`);
            
            const result = {
                success: false,
                previousUser: null,
                currentUser: null,
                switchTime: 0,
                dataLoaded: {},
                errors: []
            };

            // 记录当前用户
            const previousUser = this.userManager.getCurrentUser();
            result.previousUser = previousUser;

            // 第一步：保存当前用户数据
            if (previousUser && !options.skipSave) {
                await this.saveCurrentUserData(previousUser.identifier);
            }

            // 第二步：验证目标用户
            if (!await this.userManager.userExists(targetIdentifier)) {
                throw new Error(`目标用户不存在: ${targetIdentifier}`);
            }

            // 第三步：执行用户切换
            const switchSuccess = await this.userManager.switchToUser(targetIdentifier);
            if (!switchSuccess) {
                throw new Error('用户切换失败');
            }

            // 第四步：加载目标用户数据
            const loadedData = await this.loadUserData(targetIdentifier, options);
            result.dataLoaded = loadedData;

            // 第五步：更新系统状态
            try {
                await this.updateSystemState(targetIdentifier);
                result.systemStateUpdated = true;
            } catch (error) {
                console.error('❌ 更新系统状态失败:', targetIdentifier, error);
                result.systemStateUpdated = false;
                result.systemStateError = error.message;
                // 不重新抛出错误，让用户切换继续完成
                console.warn('⚠️ 系统状态更新失败，但用户切换将继续完成');
            }

            // 第六步：触发切换完成事件
            const currentUser = this.userManager.getCurrentUser();
            result.currentUser = currentUser;
            result.success = true;
            result.switchTime = Date.now() - switchStartTime;

            // 记录切换历史
            this.recordSwitchHistory(previousUser, currentUser, result.switchTime);

            console.log(`✅ 用户切换完成: ${currentUser.displayName} (耗时: ${result.switchTime}ms)`);
            
            // 触发切换完成事件
            this.dispatchSwitchEvent('completed', result);
            
            return result;

        } catch (error) {
            console.error(`❌ 用户切换失败: ${targetIdentifier}`, error);
            
            // 触发切换失败事件
            this.dispatchSwitchEvent('failed', { 
                targetIdentifier, 
                error: error.message,
                switchTime: Date.now() - switchStartTime
            });
            
            throw error;
            
        } finally {
            this.switchInProgress = false;
        }
    }

    /**
     * 保存当前用户数据
     * @param {string} userIdentifier - 用户标识符
     */
    async saveCurrentUserData(userIdentifier) {
        try {
            console.log(`💾 保存用户数据: ${userIdentifier}`);
            
            // 保存游戏状态（如果游戏正在进行）
            if (window.gameEngine && window.gameEngine.gameState === 'playing') {
                const gameState = window.gameEngine.getGameState();
                await this.userStorageService.putUserData(
                    StorageKeySchema.DATA_TYPES.CACHE, 
                    gameState, 
                    'gameState', 
                    userIdentifier
                );
            }

            // 保存用户设置
            if (window.gameEngine && window.gameEngine.settings) {
                await this.userStorageService.putUserData(
                    StorageKeySchema.DATA_TYPES.SETTINGS, 
                    window.gameEngine.settings, 
                    null, 
                    userIdentifier
                );
            }

            // 更新最后活跃时间
            await this.userManager.updateUserLastActive(userIdentifier);
            
        } catch (error) {
            console.error(`❌ 保存用户数据失败: ${userIdentifier}`, error);
        }
    }

    /**
     * 加载用户数据
     * @param {string} userIdentifier - 用户标识符
     * @param {Object} options - 加载选项
     * @returns {Promise<Object>} 加载的数据
     */
    async loadUserData(userIdentifier, options = {}) {
        try {
            console.log(`📥 加载用户数据: ${userIdentifier}`);
            
            const loadedData = {};

            // 检查缓存
            if (this.userDataCache.has(userIdentifier) && !options.forceReload) {
                const cachedData = this.userDataCache.get(userIdentifier);
                if (Date.now() - cachedData.timestamp < this.cacheTimeout) {
                    console.log(`📋 从缓存加载用户数据: ${userIdentifier}`);
                    return cachedData.data;
                }
            }

            // 加载游戏数据
            loadedData.gameData = await this.userStorageService.getUserData(
                StorageKeySchema.DATA_TYPES.GAME_DATA, 
                UserDataStructure.getUserGameDataStructure(), 
                null, 
                userIdentifier
            );

            // 加载用户设置
            loadedData.settings = await this.userStorageService.getUserData(
                StorageKeySchema.DATA_TYPES.SETTINGS, 
                UserDataStructure.getUserGameDataStructure().settings, 
                null, 
                userIdentifier
            );

            // 加载游戏进度
            loadedData.progress = await this.userStorageService.getUserData(
                StorageKeySchema.DATA_TYPES.PROGRESS, 
                UserDataStructure.getUserGameDataStructure().progress, 
                null, 
                userIdentifier
            );

            // 加载统计数据
            loadedData.stats = await this.userStorageService.getUserData(
                StorageKeySchema.DATA_TYPES.STATS, 
                UserDataStructure.getUserGameDataStructure().stats, 
                null, 
                userIdentifier
            );

            // 加载成就数据
            loadedData.achievements = await this.userStorageService.getUserData(
                StorageKeySchema.DATA_TYPES.ACHIEVEMENTS, 
                UserDataStructure.getUserGameDataStructure().achievements, 
                null, 
                userIdentifier
            );

            // 缓存加载的数据
            this.userDataCache.set(userIdentifier, {
                data: loadedData,
                timestamp: Date.now()
            });

            console.log(`✅ 用户数据加载完成: ${userIdentifier}`);
            return loadedData;

        } catch (error) {
            console.error(`❌ 加载用户数据失败: ${userIdentifier}`, error);
            return {};
        }
    }

    /**
     * 更新系统状态
     * @param {string} userIdentifier - 用户标识符
     */
    async updateSystemState(userIdentifier) {
        try {
            // 更新用户存储服务的当前用户
            this.userStorageService.setCurrentUser(userIdentifier);

            // 加载用户数据到游戏系统
            const userData = await this.loadUserData(userIdentifier);

            // 更新游戏引擎配置
            if (window.gameEngine) {
                try {
                    // 检查游戏引擎可用的方法
                    if (typeof window.gameEngine.updateLevelConfig === 'function') {
                        window.gameEngine.updateLevelConfig();
                        console.log('✅ 游戏引擎关卡配置已更新');
                    }

                    // 如果有动态难度调整功能，也更新一下
                    if (typeof window.gameEngine.setAdaptiveDifficulty === 'function' && userData.settings) {
                        // 根据用户偏好设置动态难度
                        const adaptiveDifficulty = userData.settings.adaptiveDifficulty !== false;
                        window.gameEngine.setAdaptiveDifficulty(adaptiveDifficulty);
                        console.log(`✅ 动态难度调整已设置为: ${adaptiveDifficulty}`);
                    }

                    console.log('✅ 游戏引擎配置已更新');
                } catch (error) {
                    console.error('❌ 更新游戏引擎配置失败:', error);
                    // 不重新抛出错误，让用户切换继续进行
                    console.warn('⚠️ 游戏引擎配置更新失败，但用户切换将继续');
                }
            } else {
                console.warn('⚠️ 游戏引擎不可用，跳过配置更新');
            }

            // 更新国际化语言设置
            if (window.i18nService && userData.settings && userData.settings.language) {
                await window.i18nService.setLanguage(userData.settings.language);
            }

            // 更新难度设置
            if (window.difficultyConfigManager && userData.settings && userData.settings.difficulty) {
                window.difficultyConfigManager.setDifficulty(userData.settings.difficulty);
            }

            console.log(`🔧 系统状态更新完成: ${userIdentifier}`);

        } catch (error) {
            console.error(`❌ 更新系统状态失败: ${userIdentifier}`, error);
        }
    }

    /**
     * 记录切换历史
     * @param {Object} previousUser - 之前的用户
     * @param {Object} currentUser - 当前用户
     * @param {number} switchTime - 切换耗时
     */
    recordSwitchHistory(previousUser, currentUser, switchTime) {
        const historyEntry = {
            timestamp: Date.now(),
            previousUser: previousUser ? {
                identifier: previousUser.identifier,
                displayName: previousUser.displayName
            } : null,
            currentUser: {
                identifier: currentUser.identifier,
                displayName: currentUser.displayName
            },
            switchTime: switchTime
        };

        this.switchHistory.unshift(historyEntry);

        // 限制历史记录大小
        if (this.switchHistory.length > this.maxHistorySize) {
            this.switchHistory = this.switchHistory.slice(0, this.maxHistorySize);
        }
    }

    /**
     * 获取切换历史
     * @returns {Array} 切换历史记录
     */
    getSwitchHistory() {
        return [...this.switchHistory];
    }

    /**
     * 清理用户数据缓存
     * @param {string} userIdentifier - 用户标识符（可选，如果不提供则清理所有缓存）
     */
    clearUserDataCache(userIdentifier = null) {
        if (userIdentifier) {
            this.userDataCache.delete(userIdentifier);
            console.log(`🧹 清理用户数据缓存: ${userIdentifier}`);
        } else {
            this.userDataCache.clear();
            console.log('🧹 清理所有用户数据缓存');
        }
    }

    /**
     * 预加载用户数据
     * @param {string} userIdentifier - 用户标识符
     */
    async preloadUserData(userIdentifier) {
        try {
            if (!this.userDataCache.has(userIdentifier)) {
                console.log(`📋 预加载用户数据: ${userIdentifier}`);
                await this.loadUserData(userIdentifier);
            }
        } catch (error) {
            console.error(`❌ 预加载用户数据失败: ${userIdentifier}`, error);
        }
    }

    /**
     * 触发切换事件
     * @param {string} eventType - 事件类型
     * @param {Object} eventData - 事件数据
     */
    dispatchSwitchEvent(eventType, eventData) {
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const customEvent = new CustomEvent(`userSwitch:${eventType}`, {
                detail: eventData
            });
            window.dispatchEvent(customEvent);
        }
    }

    /**
     * 获取切换状态
     * @returns {Object} 切换状态信息
     */
    getSwitchStatus() {
        return {
            inProgress: this.switchInProgress,
            currentUser: this.userManager.getCurrentUser(),
            cacheSize: this.userDataCache.size,
            historySize: this.switchHistory.length
        };
    }
}

// 创建全局用户切换管理器实例
if (typeof window !== 'undefined') {
    window.UserSwitchManager = UserSwitchManager;
}

// 模块导出（如果支持）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserSwitchManager;
}
