/**
 * EdgeOne 云存储初始化模块
 * 负责在应用启动前初始化和注入 EdgeOne 存储服务
 * 
 * 使用方式：
 * 1. 在主页面加载此模块
 * 2. 调用 initEdgeOneStorage() 函数
 * 3. 存储服务会自动检测并使用云存储
 */

/**
 * EdgeOne 存储配置管理器
 * 管理不同环境下的存储配置
 */
class EdgeOneStorageConfig {
    constructor() {
        this.configs = {
            // 开发环境配置
            development: {
                useRelativePath: true,  // 使用相对路径调用本地 Functions
                enableFallback: true,   // 启用本地存储降级
                enableCache: true,      // 启用缓存
                timeout: 10000,         // 10秒超时
                retryCount: 2,          // 重试2次
                retryDelay: 1000        // 重试延迟1秒
            },
            
            // 生产环境配置
            production: {
                baseUrl: null,          // 将在运行时设置
                enableFallback: true,   // 启用降级机制
                enableCache: true,      // 启用缓存
                timeout: 15000,         // 15秒超时
                retryCount: 3,          // 重试3次
                retryDelay: 1500        // 重试延迟1.5秒
            },
            
            // 测试环境配置
            testing: {
                useRelativePath: true,
                enableFallback: true,
                enableCache: false,     // 测试时禁用缓存
                timeout: 5000,          // 5秒超时
                retryCount: 1,          // 重试1次
                retryDelay: 500         // 重试延迟0.5秒
            }
        };
    }

    /**
     * 获取当前环境配置
     * @returns {Object} 存储配置对象
     */
    getCurrentConfig() {
        // 检测当前环境
        const hostname = window.location.hostname;
        const isLocalhost = hostname === 'localhost' || hostname === '127.0.0.1';
        const isTestDomain = hostname.includes('test') || hostname.includes('staging');
        
        let environment;
        if (isLocalhost) {
            environment = 'development';
        } else if (isTestDomain) {
            environment = 'testing';
        } else {
            environment = 'production';
        }
        
        console.log(`🌍 检测到环境: ${environment} (${hostname})`);
        return { ...this.configs[environment], environment };
    }

    /**
     * 设置生产环境的 API 基础 URL
     * @param {string} baseUrl - API 基础 URL
     */
    setProductionBaseUrl(baseUrl) {
        this.configs.production.baseUrl = baseUrl;
        console.log(`🔗 设置生产环境 API 地址: ${baseUrl}`);
    }
}

/**
 * EdgeOne 存储初始化器
 * 负责创建和配置 EdgeOneStorageImpl 实例
 */
class EdgeOneStorageInitializer {
    constructor() {
        this.config = new EdgeOneStorageConfig();
        this.isInitialized = false;
        this.storageInstance = null;
    }

    /**
     * 初始化 EdgeOne 存储服务
     * @param {Object} options - 可选配置参数
     * @param {string} options.productionApiUrl - 生产环境 API URL
     * @param {Object} options.customConfig - 自定义配置覆盖
     * @returns {Promise<boolean>} 初始化是否成功
     */
    async initEdgeOneStorage(options = {}) {
        if (this.isInitialized) {
            console.log('⚠️ EdgeOne 存储已初始化，跳过重复初始化');
            return true;
        }

        try {
            console.log('🚀 开始初始化 EdgeOne 云存储...');

            // 设置生产环境 API URL（如果提供）
            if (options.productionApiUrl) {
                this.config.setProductionBaseUrl(options.productionApiUrl);
            }

            // 获取当前环境配置
            const currentConfig = this.config.getCurrentConfig();
            
            // 应用自定义配置覆盖
            const finalConfig = {
                ...currentConfig,
                ...options.customConfig
            };

            console.log('🔧 使用配置:', finalConfig);

            // 检查 EdgeOneStorageImpl 类是否可用
            if (typeof EdgeOneStorageImpl === 'undefined') {
                throw new Error('EdgeOneStorageImpl 类未找到，请确保已加载 edgeOneStorageImpl.js');
            }

            // 创建 EdgeOneStorageImpl 实例
            let storageInstance;
            if (finalConfig.baseUrl) {
                // 使用指定的 API URL
                storageInstance = new EdgeOneStorageImpl(finalConfig.baseUrl, finalConfig);
                console.log(`🔗 使用远程 API: ${finalConfig.baseUrl}`);
            } else {
                // 使用相对路径（本地 Functions）
                storageInstance = new EdgeOneStorageImpl(finalConfig);
                console.log('🔗 使用相对路径: /storage/');
            }

            // 将实例注入到全局变量
            window.edgeOneStorage = storageInstance;
            this.storageInstance = storageInstance;

            console.log('✅ EdgeOne 存储实例已注入到 window.edgeOneStorage');

            // 可选：进行连接测试
            if (finalConfig.testConnection !== false) {
                await this.testConnection();
            }

            this.isInitialized = true;
            console.log('🎉 EdgeOne 云存储初始化完成');

            return true;

        } catch (error) {
            console.error('❌ EdgeOne 云存储初始化失败:', error);
            console.warn('🔄 将回退到本地存储模式');
            return false;
        }
    }

    /**
     * 测试 EdgeOne 存储连接
     * @returns {Promise<boolean>} 连接测试是否成功
     */
    async testConnection() {
        if (!this.storageInstance) {
            return false;
        }

        try {
            console.log('🔍 测试 EdgeOne 存储连接...');
            
            // 执行简单的连接测试
            const testKey = `connection_test_${Date.now()}`;
            const testValue = 'test_connection';
            
            await this.storageInstance.put(testKey, testValue);
            const retrievedValue = await this.storageInstance.get(testKey);
            await this.storageInstance.delete(testKey);
            
            if (retrievedValue === testValue) {
                console.log('✅ EdgeOne 存储连接测试成功');
                return true;
            } else {
                console.warn('⚠️ EdgeOne 存储连接测试失败：数据不匹配');
                return false;
            }
            
        } catch (error) {
            console.warn('⚠️ EdgeOne 存储连接测试失败:', error.message);
            return false;
        }
    }

    /**
     * 获取存储实例状态信息
     * @returns {Object} 状态信息
     */
    getStatus() {
        if (!this.storageInstance) {
            return {
                initialized: false,
                available: false,
                mode: 'none'
            };
        }

        const stats = this.storageInstance.getStats();
        return {
            initialized: this.isInitialized,
            available: stats.isAvailable,
            mode: stats.storageMode,
            requestCount: stats.requestCount,
            environment: this.config.getCurrentConfig().environment
        };
    }
}

// 创建全局初始化器实例
const edgeOneInitializer = new EdgeOneStorageInitializer();

/**
 * 便捷的初始化函数
 * @param {Object} options - 初始化选项
 * @returns {Promise<boolean>} 初始化是否成功
 */
async function initEdgeOneStorage(options = {}) {
    return await edgeOneInitializer.initEdgeOneStorage(options);
}

/**
 * 获取 EdgeOne 存储状态
 * @returns {Object} 状态信息
 */
function getEdgeOneStorageStatus() {
    return edgeOneInitializer.getStatus();
}

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.EdgeOneStorageConfig = EdgeOneStorageConfig;
    window.EdgeOneStorageInitializer = EdgeOneStorageInitializer;
    window.initEdgeOneStorage = initEdgeOneStorage;
    window.getEdgeOneStorageStatus = getEdgeOneStorageStatus;
    window.edgeOneInitializer = edgeOneInitializer;
}

// 模块导出
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        EdgeOneStorageConfig,
        EdgeOneStorageInitializer,
        initEdgeOneStorage,
        getEdgeOneStorageStatus
    };
}

console.log('📦 EdgeOne 存储初始化模块已加载');
