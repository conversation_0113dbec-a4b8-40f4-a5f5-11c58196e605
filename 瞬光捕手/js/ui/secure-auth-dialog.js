/**
 * 瞬光捕手 - 安全认证对话框
 * 提供安全的用户认证界面，需要用户明确同意
 */

class SecureAuthDialog {
    constructor() {
        this.isVisible = false;
        this.onAuthSuccess = null;
        this.onAuthCancel = null;
        
        console.log('🔐 安全认证对话框已创建');
    }

    /**
     * 显示认证对话框
     * @param {Object} options - 显示选项
     * @returns {Promise<Object|null>} 认证结果
     */
    async show(options = {}) {
        return new Promise((resolve, reject) => {
            this.onAuthSuccess = resolve;
            this.onAuthCancel = () => resolve(null);
            
            this.createDialog();
            this.isVisible = true;
        });
    }

    /**
     * 创建认证对话框
     */
    createDialog() {
        // 移除可能存在的旧对话框
        this.removeDialog();

        const dialog = document.createElement('div');
        dialog.id = 'secure-auth-dialog';
        dialog.className = 'secure-auth-overlay';
        
        dialog.innerHTML = `
            <div class="secure-auth-modal">
                <div class="auth-header">
                    <h2 data-i18n="auth.title">用户认证</h2>
                    <p data-i18n="auth.description">为了保护您的数据安全，请选择认证方式</p>
                </div>
                
                <div class="auth-options">
                    <div class="auth-option">
                        <h3 data-i18n="auth.guest.title">访客模式</h3>
                        <p data-i18n="auth.guest.description">临时游戏，数据不会保存</p>
                        <button id="auth-guest-btn" class="auth-btn guest-btn" data-i18n="auth.guest.button">
                            以访客身份继续
                        </button>
                    </div>
                    
                    <div class="auth-option">
                        <h3 data-i18n="auth.username.title">用户名登录</h3>
                        <p data-i18n="auth.username.description">创建或使用用户名，数据会保存</p>
                        <input type="text" id="username-input" placeholder="输入用户名" 
                               data-i18n-placeholder="auth.username.placeholder">
                        <button id="auth-username-btn" class="auth-btn username-btn" data-i18n="auth.username.button">
                            使用用户名
                        </button>
                    </div>
                    
                    <div class="auth-option">
                        <h3 data-i18n="auth.device.title">设备识别</h3>
                        <p data-i18n="auth.device.description">基于设备特征自动识别，数据会保存</p>
                        <button id="auth-device-btn" class="auth-btn device-btn" data-i18n="auth.device.button">
                            使用设备识别
                        </button>
                    </div>
                </div>
                
                <div class="auth-footer">
                    <div class="privacy-notice">
                        <p data-i18n="auth.privacy">我们重视您的隐私，不会收集不必要的个人信息</p>
                    </div>
                    <button id="auth-cancel-btn" class="auth-btn cancel-btn" data-i18n="auth.cancel">
                        取消
                    </button>
                </div>
            </div>
        `;

        document.body.appendChild(dialog);
        this.bindEvents();
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        const dialog = document.getElementById('secure-auth-dialog');
        if (!dialog) return;

        // 访客模式
        const guestBtn = dialog.querySelector('#auth-guest-btn');
        guestBtn?.addEventListener('click', () => this.handleGuestAuth());

        // 用户名认证
        const usernameBtn = dialog.querySelector('#auth-username-btn');
        const usernameInput = dialog.querySelector('#username-input');
        usernameBtn?.addEventListener('click', () => this.handleUsernameAuth());
        usernameInput?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.handleUsernameAuth();
        });

        // 设备识别
        const deviceBtn = dialog.querySelector('#auth-device-btn');
        deviceBtn?.addEventListener('click', () => this.handleDeviceAuth());

        // 取消
        const cancelBtn = dialog.querySelector('#auth-cancel-btn');
        cancelBtn?.addEventListener('click', () => this.handleCancel());

        // 点击遮罩层取消
        dialog.addEventListener('click', (e) => {
            if (e.target === dialog) this.handleCancel();
        });
    }

    /**
     * 处理访客认证
     */
    async handleGuestAuth() {
        try {
            console.log('👤 用户选择访客模式');
            
            const authResult = {
                type: 'anonymous',
                userConsent: true,
                sessionOnly: true,
                displayName: '访客用户'
            };

            this.hideDialog();
            this.onAuthSuccess?.(authResult);
        } catch (error) {
            console.error('❌ 访客认证失败:', error);
            this.showError('访客认证失败，请重试');
        }
    }

    /**
     * 处理用户名认证
     */
    async handleUsernameAuth() {
        try {
            const usernameInput = document.querySelector('#username-input');
            const username = usernameInput?.value?.trim();

            if (!username) {
                this.showError('请输入用户名');
                return;
            }

            if (username.length < 3) {
                this.showError('用户名至少需要3个字符');
                return;
            }

            console.log('👤 用户选择用户名认证:', username);

            const authResult = {
                type: 'username_suffix',
                username: username,
                userConsent: true,
                sessionOnly: false
            };

            this.hideDialog();
            this.onAuthSuccess?.(authResult);
        } catch (error) {
            console.error('❌ 用户名认证失败:', error);
            this.showError('用户名认证失败，请重试');
        }
    }

    /**
     * 处理设备认证
     */
    async handleDeviceAuth() {
        try {
            console.log('👤 用户选择设备识别认证');

            const authResult = {
                type: 'device_fingerprint',
                userConsent: true,
                sessionOnly: false
            };

            this.hideDialog();
            this.onAuthSuccess?.(authResult);
        } catch (error) {
            console.error('❌ 设备认证失败:', error);
            this.showError('设备认证失败，请重试');
        }
    }

    /**
     * 处理取消
     */
    handleCancel() {
        console.log('🔐 用户取消认证');
        this.hideDialog();
        this.onAuthCancel?.();
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        const dialog = document.getElementById('secure-auth-dialog');
        if (!dialog) return;

        // 移除旧的错误信息
        const oldError = dialog.querySelector('.auth-error');
        oldError?.remove();

        // 添加新的错误信息
        const errorDiv = document.createElement('div');
        errorDiv.className = 'auth-error';
        errorDiv.textContent = message;

        const authOptions = dialog.querySelector('.auth-options');
        authOptions?.insertBefore(errorDiv, authOptions.firstChild);

        // 3秒后自动移除错误信息
        setTimeout(() => errorDiv?.remove(), 3000);
    }

    /**
     * 隐藏对话框
     */
    hideDialog() {
        this.removeDialog();
        this.isVisible = false;
    }

    /**
     * 移除对话框
     */
    removeDialog() {
        const dialog = document.getElementById('secure-auth-dialog');
        dialog?.remove();
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecureAuthDialog;
} else {
    window.SecureAuthDialog = SecureAuthDialog;
}
