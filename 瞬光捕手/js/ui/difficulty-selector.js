/**
 * 瞬光捕手 - 难度选择器UI组件
 * 基于DifficultyConfigManager的美观难度选择界面
 */

class DifficultySelectorUI {
    constructor() {
        this.isVisible = false;
        this.currentDifficulty = 'normal';
        this.animationDuration = 300;
        this.containerElement = null;
        
        // 难度等级的视觉配置
        this.difficultyVisuals = {
            'beginner': {
                gradient: 'linear-gradient(135deg, #4CAF50 0%, #8BC34A 100%)',
                icon: '🌱',
                particles: 'gentle',
                glow: '#4CAF50'
            },
            'easy': {
                gradient: 'linear-gradient(135deg, #8BC34A 0%, #CDDC39 100%)',
                icon: '🍃',
                particles: 'light',
                glow: '#8BC34A'
            },
            'normal': {
                gradient: 'linear-gradient(135deg, #FFC107 0%, #FF9800 100%)',
                icon: '⚡',
                particles: 'moderate',
                glow: '#FFC107'
            },
            'hard': {
                gradient: 'linear-gradient(135deg, #FF9800 0%, #FF5722 100%)',
                icon: '🔥',
                particles: 'intense',
                glow: '#FF9800'
            },
            'expert': {
                gradient: 'linear-gradient(135deg, #F44336 0%, #E91E63 100%)',
                icon: '💀',
                particles: 'extreme',
                glow: '#F44336'
            },
            'master': {
                gradient: 'linear-gradient(135deg, #9C27B0 0%, #673AB7 100%)',
                icon: '👑',
                particles: 'legendary',
                glow: '#9C27B0'
            }
        };
        
        console.log('🎨 难度选择器UI组件已创建');
    }

    /**
     * 显示难度选择器
     */
    show() {
        if (this.isVisible) return;
        
        this.createUI();
        this.isVisible = true;
        
        // 添加到页面
        document.body.appendChild(this.containerElement);
        
        // 获取当前难度
        if (window.difficultyConfigManager && window.difficultyConfigManager.initialized) {
            this.currentDifficulty = difficultyConfigManager.getCurrentDifficulty();
        }
        
        // 更新UI显示
        this.updateCurrentDifficulty();
        
        // 添加显示动画
        requestAnimationFrame(() => {
            this.containerElement.classList.add('show');
        });
        
        // 启动粒子效果
        this.startParticleEffects();
    }

    /**
     * 隐藏难度选择器
     */
    hide() {
        if (!this.isVisible) return;
        
        this.containerElement.classList.remove('show');
        
        setTimeout(() => {
            if (this.containerElement && this.containerElement.parentNode) {
                this.containerElement.parentNode.removeChild(this.containerElement);
            }
            this.isVisible = false;
        }, this.animationDuration);
    }

    /**
     * 创建UI元素
     */
    createUI() {
        this.containerElement = document.createElement('div');
        this.containerElement.className = 'difficulty-selector-overlay';
        this.containerElement.innerHTML = this.getOverlayHTML();
        
        // 绑定事件
        this.bindEvents();
    }

    /**
     * 获取覆盖层HTML
     */
    getOverlayHTML() {
        const difficulties = window.difficultyConfigManager ? 
            difficultyConfigManager.getAvailableDifficulties() : 
            this.getDefaultDifficulties();

        return `
            <div class="difficulty-selector-modal">
                <div class="modal-header">
                    <h2>🎯 选择游戏难度</h2>
                    <button class="close-btn" data-action="close">×</button>
                </div>
                
                <div class="modal-content">
                    <div class="difficulty-description">
                        <p>选择适合您的游戏难度。不同难度会影响光点速度、生成频率、时机窗口等参数。</p>
                    </div>
                    
                    <div class="difficulty-grid">
                        ${difficulties.map(diff => this.getDifficultyCardHTML(diff)).join('')}
                    </div>
                    
                    <div class="current-difficulty-info">
                        <div class="info-header">
                            <h3>当前难度详情</h3>
                        </div>
                        <div class="info-content">
                            <div class="difficulty-stats">
                                <!-- 动态生成的难度统计信息 -->
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-actions">
                        <button class="btn btn-secondary" data-action="cancel">取消</button>
                        <button class="btn btn-primary" data-action="apply">应用难度</button>
                    </div>
                </div>
                
                <!-- 粒子效果容器 -->
                <div class="particles-container"></div>
            </div>
        `;
    }

    /**
     * 获取难度卡片HTML
     */
    getDifficultyCardHTML(difficulty) {
        const visual = this.difficultyVisuals[difficulty.key] || this.difficultyVisuals['normal'];
        const isRecommended = difficulty.key === 'normal';
        
        return `
            <div class="difficulty-card" 
                 data-difficulty="${difficulty.key}"
                 data-multiplier="${difficulty.multiplier}">
                <div class="card-background" style="background: ${visual.gradient}"></div>
                <div class="card-content">
                    <div class="difficulty-icon">${visual.icon}</div>
                    <div class="difficulty-info">
                        <h3 class="difficulty-name">${difficulty.name}</h3>
                        <p class="difficulty-desc">${difficulty.description}</p>
                        <div class="difficulty-multiplier">
                            难度倍数: <span class="multiplier-value">${difficulty.multiplier}x</span>
                        </div>
                        ${isRecommended ? '<div class="recommended-badge">推荐</div>' : ''}
                    </div>
                    <div class="card-glow" style="box-shadow: 0 0 30px ${visual.glow}40"></div>
                </div>
                <div class="selection-indicator">
                    <div class="indicator-dot"></div>
                </div>
            </div>
        `;
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 关闭按钮
        this.containerElement.addEventListener('click', (e) => {
            if (e.target.dataset.action === 'close' || e.target === this.containerElement) {
                this.hide();
            }
        });

        // 难度卡片选择
        this.containerElement.addEventListener('click', (e) => {
            const card = e.target.closest('.difficulty-card');
            if (card) {
                this.selectDifficulty(card.dataset.difficulty);
            }
        });

        // 按钮事件
        this.containerElement.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action) {
                this.handleAction(action);
            }
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (!this.isVisible) return;
            
            if (e.key === 'Escape') {
                this.hide();
            } else if (e.key >= '1' && e.key <= '6') {
                const difficulties = ['beginner', 'easy', 'normal', 'hard', 'expert', 'master'];
                const index = parseInt(e.key) - 1;
                if (difficulties[index]) {
                    this.selectDifficulty(difficulties[index]);
                }
            }
        });
    }

    /**
     * 选择难度
     */
    selectDifficulty(difficulty) {
        // 移除之前的选中状态
        const cards = this.containerElement.querySelectorAll('.difficulty-card');
        cards.forEach(card => card.classList.remove('selected'));
        
        // 添加新的选中状态
        const selectedCard = this.containerElement.querySelector(`[data-difficulty="${difficulty}"]`);
        if (selectedCard) {
            selectedCard.classList.add('selected');
            this.currentDifficulty = difficulty;
            
            // 更新难度信息显示
            this.updateDifficultyInfo(difficulty);
            
            // 播放选择音效
            this.playSelectionSound();
            
            // 更新粒子效果
            this.updateParticleEffects(difficulty);
        }
    }

    /**
     * 更新难度信息显示
     */
    updateDifficultyInfo(difficulty) {
        const statsContainer = this.containerElement.querySelector('.difficulty-stats');
        
        if (window.difficultyConfigManager && window.difficultyConfigManager.initialized) {
            const config = difficultyConfigManager.getGameDifficultyConfig('spark-catcher', difficulty);
            const difficultyScore = difficultyConfigManager.calculateDifficultyScore('spark-catcher', difficulty);
            
            statsContainer.innerHTML = `
                <div class="stat-item">
                    <span class="stat-label">难度评分</span>
                    <div class="stat-bar">
                        <div class="stat-fill" style="width: ${difficultyScore}%; background: ${this.difficultyVisuals[difficulty].glow}"></div>
                        <span class="stat-value">${difficultyScore}/100</span>
                    </div>
                </div>
                
                <div class="stat-grid">
                    <div class="stat-item">
                        <span class="stat-label">生命值</span>
                        <span class="stat-value">${config.gameplay.player.lives}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">光点生成间隔</span>
                        <span class="stat-value">${config.gameplay.sparks.spawnRate}ms</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">完美时机窗口</span>
                        <span class="stat-value">${config.gameplay.timing.perfectWindow}ms</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">光点移动速度</span>
                        <span class="stat-value">${config.gameplay.sparks.speed.toFixed(1)}x</span>
                    </div>
                </div>
                
                <div class="difficulty-features">
                    <h4>难度特性</h4>
                    <div class="features-list">
                        ${config.userExperience.showHints ? '<span class="feature-tag positive">显示提示</span>' : '<span class="feature-tag negative">无提示</span>'}
                        ${config.userExperience.visualFeedback.screenShake ? '<span class="feature-tag">屏幕震动</span>' : ''}
                        ${config.userExperience.visualFeedback.slowMotion ? '<span class="feature-tag positive">慢动作效果</span>' : ''}
                        ${config.userExperience.retryLimit > 0 ? `<span class="feature-tag negative">重试限制: ${config.userExperience.retryLimit}</span>` : '<span class="feature-tag positive">无限重试</span>'}
                    </div>
                </div>
            `;
        } else {
            // 回退到默认信息显示
            const difficultyInfo = this.getDefaultDifficultyInfo(difficulty);
            statsContainer.innerHTML = `
                <div class="stat-item">
                    <span class="stat-label">难度倍数</span>
                    <span class="stat-value">${difficultyInfo.multiplier}x</span>
                </div>
                <p class="difficulty-desc">${difficultyInfo.description}</p>
            `;
        }
    }

    /**
     * 更新当前难度显示
     */
    updateCurrentDifficulty() {
        this.selectDifficulty(this.currentDifficulty);
    }

    /**
     * 处理用户操作
     */
    async handleAction(action) {
        switch (action) {
            case 'close':
            case 'cancel':
                this.hide();
                break;
                
            case 'apply':
                await this.applyDifficulty();
                break;
        }
    }

    /**
     * 应用难度设置
     */
    async applyDifficulty() {
        try {
            if (window.difficultyConfigManager && window.difficultyConfigManager.initialized) {
                const success = await difficultyConfigManager.setDifficulty(this.currentDifficulty);
                
                if (success) {
                    this.showSuccessMessage();
                    
                    // 延迟关闭以显示成功消息
                    setTimeout(() => {
                        this.hide();
                    }, 1500);
                } else {
                    this.showErrorMessage('难度设置失败，请重试');
                }
            } else {
                // 如果难度管理器不可用，直接关闭
                console.warn('⚠️ 难度配置管理器不可用');
                this.hide();
            }
        } catch (error) {
            console.error('❌ 应用难度设置失败:', error);
            this.showErrorMessage(error.message);
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage() {
        const difficultyInfo = this.difficultyVisuals[this.currentDifficulty];
        const message = document.createElement('div');
        message.className = 'success-message';
        message.innerHTML = `
            <div class="success-icon">${difficultyInfo.icon}</div>
            <div class="success-text">难度已设置为 ${this.getDifficultyName(this.currentDifficulty)}</div>
        `;
        
        this.containerElement.querySelector('.modal-content').appendChild(message);
        
        // 添加动画
        requestAnimationFrame(() => {
            message.classList.add('show');
        });
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        const errorElement = document.createElement('div');
        errorElement.className = 'error-toast';
        errorElement.textContent = message;
        
        document.body.appendChild(errorElement);
        
        setTimeout(() => {
            if (errorElement.parentNode) {
                errorElement.parentNode.removeChild(errorElement);
            }
        }, 3000);
    }

    /**
     * 启动粒子效果
     */
    startParticleEffects() {
        const container = this.containerElement.querySelector('.particles-container');
        
        // 创建粒子
        for (let i = 0; i < 20; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.cssText = `
                position: absolute;
                width: 4px;
                height: 4px;
                background: rgba(255, 255, 255, 0.6);
                border-radius: 50%;
                pointer-events: none;
                left: ${Math.random() * 100}%;
                top: ${Math.random() * 100}%;
                animation: float ${3 + Math.random() * 4}s infinite ease-in-out;
                animation-delay: ${Math.random() * 2}s;
            `;
            
            container.appendChild(particle);
        }
    }

    /**
     * 更新粒子效果
     */
    updateParticleEffects(difficulty) {
        const visual = this.difficultyVisuals[difficulty];
        const particles = this.containerElement.querySelectorAll('.particle');
        
        particles.forEach(particle => {
            particle.style.background = visual.glow + '80';
        });
    }

    /**
     * 播放选择音效
     */
    playSelectionSound() {
        // 创建简单的音效
        if (window.AudioContext || window.webkitAudioContext) {
            try {
                const audioContext = new (window.AudioContext || window.webkitAudioContext)();
                const oscillator = audioContext.createOscillator();
                const gainNode = audioContext.createGain();
                
                oscillator.connect(gainNode);
                gainNode.connect(audioContext.destination);
                
                oscillator.frequency.setValueAtTime(800, audioContext.currentTime);
                oscillator.frequency.exponentialRampToValueAtTime(1200, audioContext.currentTime + 0.1);
                
                gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
                gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.1);
                
                oscillator.start(audioContext.currentTime);
                oscillator.stop(audioContext.currentTime + 0.1);
            } catch (error) {
                // 音效播放失败，忽略错误
            }
        }
    }

    /**
     * 获取默认难度列表
     */
    getDefaultDifficulties() {
        return [
            { key: 'beginner', name: '新手', description: '适合初次接触游戏的玩家', multiplier: 0.6 },
            { key: 'easy', name: '简单', description: '轻松愉快的游戏体验', multiplier: 0.8 },
            { key: 'normal', name: '普通', description: '标准的游戏难度', multiplier: 1.0 },
            { key: 'hard', name: '困难', description: '具有挑战性的游戏体验', multiplier: 1.3 },
            { key: 'expert', name: '专家', description: '极具挑战性，适合高手', multiplier: 1.6 },
            { key: 'master', name: '大师', description: '终极挑战，考验极限', multiplier: 2.0 }
        ];
    }

    /**
     * 获取默认难度信息
     */
    getDefaultDifficultyInfo(difficulty) {
        const difficulties = this.getDefaultDifficulties();
        return difficulties.find(d => d.key === difficulty) || difficulties[2];
    }

    /**
     * 获取难度名称
     */
    getDifficultyName(difficulty) {
        const info = this.getDefaultDifficultyInfo(difficulty);
        return info.name;
    }

    /**
     * 获取当前选中的难度
     */
    getSelectedDifficulty() {
        return this.currentDifficulty;
    }

    /**
     * 设置难度（外部调用）
     */
    setDifficulty(difficulty) {
        this.currentDifficulty = difficulty;
        if (this.isVisible) {
            this.updateCurrentDifficulty();
        }
    }
}

// 创建全局难度选择器实例
const difficultySelectorUI = new DifficultySelectorUI();

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.DifficultySelectorUI = DifficultySelectorUI;
    window.difficultySelectorUI = difficultySelectorUI;
}

// 支持CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DifficultySelectorUI,
        difficultySelectorUI
    };
}
