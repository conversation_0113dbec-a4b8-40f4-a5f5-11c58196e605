/**
 * 瞬光捕手 - 难度分类排行榜UI组件
 * 提供按难度等级分类的排行榜显示界面
 */

class DifficultyLeaderboardUI {
    constructor() {
        this.isVisible = false;
        this.currentDifficulty = 'normal';
        this.currentCategory = 'high_score';
        this.containerElement = null;
        
        // 难度等级的视觉配置
        this.difficultyVisuals = {
            'beginner': { color: '#4CAF50', icon: '🌱', name: '新手' },
            'easy': { color: '#8BC34A', icon: '🍃', name: '简单' },
            'normal': { color: '#FFC107', icon: '⚡', name: '普通' },
            'hard': { color: '#FF9800', icon: '🔥', name: '困难' },
            'expert': { color: '#F44336', icon: '💀', name: '专家' },
            'master': { color: '#9C27B0', icon: '👑', name: '大师' }
        };
        
        // 排行榜类别配置
        this.categoryConfig = {
            'high_score': { name: '最高分', icon: '🏆', description: '历史最高分数排行榜' },
            'daily_high_score': { name: '每日最高分', icon: '📅', description: '今日最高分数排行榜' },
            'weekly_high_score': { name: '每周最高分', icon: '📊', description: '本周最高分数排行榜' },
            'monthly_high_score': { name: '每月最高分', icon: '📈', description: '本月最高分数排行榜' },
            'perfect_hits': { name: '完美击中', icon: '🎯', description: '完美击中次数排行榜' },
            'combo_record': { name: '连击记录', icon: '⚡', description: '最高连击数排行榜' },
            'level_completion': { name: '关卡通关', icon: '🚀', description: '通关关卡数排行榜' }
        };
        
        console.log('🏆 难度排行榜UI组件已创建');
    }

    /**
     * 显示难度排行榜界面
     */
    show() {
        if (this.isVisible) return;
        
        this.createUI();
        this.isVisible = true;
        
        // 添加到页面
        document.body.appendChild(this.containerElement);
        
        // 加载初始数据
        this.loadLeaderboardData();
        
        // 添加显示动画
        requestAnimationFrame(() => {
            this.containerElement.classList.add('show');
        });
    }

    /**
     * 隐藏难度排行榜界面
     */
    hide() {
        if (!this.isVisible) return;
        
        this.containerElement.classList.remove('show');
        
        setTimeout(() => {
            if (this.containerElement && this.containerElement.parentNode) {
                this.containerElement.parentNode.removeChild(this.containerElement);
            }
            this.isVisible = false;
        }, 300);
    }

    /**
     * 创建UI元素
     */
    createUI() {
        this.containerElement = document.createElement('div');
        this.containerElement.className = 'difficulty-leaderboard-overlay';
        this.containerElement.innerHTML = this.getOverlayHTML();
        
        // 绑定事件
        this.bindEvents();
    }

    /**
     * 获取覆盖层HTML
     */
    getOverlayHTML() {
        return `
            <div class="difficulty-leaderboard-modal">
                <div class="modal-header">
                    <h2>🏆 难度排行榜</h2>
                    <div class="header-controls">
                        <button class="btn btn-icon" data-action="crossDifficulty" title="跨难度比较">
                            <span>🌟</span>
                        </button>
                        <button class="btn btn-icon" data-action="stats" title="统计信息">
                            <span>📊</span>
                        </button>
                        <button class="close-btn" data-action="close">×</button>
                    </div>
                </div>
                
                <div class="modal-content">
                    <!-- 难度选择器 -->
                    <div class="difficulty-selector">
                        <h3>选择难度等级</h3>
                        <div class="difficulty-tabs">
                            ${this.getDifficultyTabsHTML()}
                        </div>
                    </div>
                    
                    <!-- 类别选择器 -->
                    <div class="category-selector">
                        <h3>排行榜类别</h3>
                        <div class="category-tabs">
                            ${this.getCategoryTabsHTML()}
                        </div>
                    </div>
                    
                    <!-- 排行榜内容 -->
                    <div class="leaderboard-content">
                        <div class="leaderboard-header">
                            <div class="current-selection">
                                <span class="difficulty-badge"></span>
                                <span class="category-name"></span>
                            </div>
                            <div class="leaderboard-actions">
                                <button class="btn btn-small" data-action="refresh">刷新</button>
                                <button class="btn btn-small" data-action="export">导出</button>
                            </div>
                        </div>
                        
                        <div class="leaderboard-list">
                            <div class="loading-state">
                                <div class="spinner"></div>
                                <p>加载排行榜数据...</p>
                            </div>
                        </div>
                        
                        <!-- 玩家排名信息 -->
                        <div class="player-rank-info">
                            <h4>您的排名</h4>
                            <div class="rank-details">
                                <!-- 动态生成的排名信息 -->
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取难度标签页HTML
     */
    getDifficultyTabsHTML() {
        const difficulties = window.difficultyLeaderboardManager ? 
            difficultyLeaderboardManager.getSupportedDifficulties() : 
            Object.keys(this.difficultyVisuals);

        return difficulties.map(difficulty => {
            const visual = this.difficultyVisuals[difficulty];
            const isActive = difficulty === this.currentDifficulty;
            
            return `
                <div class="difficulty-tab ${isActive ? 'active' : ''}" 
                     data-difficulty="${difficulty}"
                     style="--difficulty-color: ${visual.color}">
                    <div class="tab-icon">${visual.icon}</div>
                    <div class="tab-name">${visual.name}</div>
                </div>
            `;
        }).join('');
    }

    /**
     * 获取类别标签页HTML
     */
    getCategoryTabsHTML() {
        const categories = window.difficultyLeaderboardManager ? 
            Object.values(difficultyLeaderboardManager.getSupportedCategories()) : 
            Object.keys(this.categoryConfig);

        return categories.map(category => {
            const config = this.categoryConfig[category];
            const isActive = category === this.currentCategory;
            
            return `
                <div class="category-tab ${isActive ? 'active' : ''}" 
                     data-category="${category}">
                    <div class="tab-icon">${config.icon}</div>
                    <div class="tab-info">
                        <div class="tab-name">${config.name}</div>
                        <div class="tab-desc">${config.description}</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 关闭按钮
        this.containerElement.addEventListener('click', (e) => {
            if (e.target.dataset.action === 'close' || e.target === this.containerElement) {
                this.hide();
            }
        });

        // 难度标签页切换
        this.containerElement.addEventListener('click', (e) => {
            const difficultyTab = e.target.closest('.difficulty-tab');
            if (difficultyTab) {
                this.selectDifficulty(difficultyTab.dataset.difficulty);
            }
        });

        // 类别标签页切换
        this.containerElement.addEventListener('click', (e) => {
            const categoryTab = e.target.closest('.category-tab');
            if (categoryTab) {
                this.selectCategory(categoryTab.dataset.category);
            }
        });

        // 按钮事件
        this.containerElement.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action) {
                this.handleAction(action);
            }
        });
    }

    /**
     * 选择难度等级
     */
    selectDifficulty(difficulty) {
        if (this.currentDifficulty === difficulty) return;
        
        // 更新当前难度
        this.currentDifficulty = difficulty;
        
        // 更新UI状态
        this.updateDifficultyTabs();
        this.updateCurrentSelection();
        
        // 重新加载数据
        this.loadLeaderboardData();
    }

    /**
     * 选择排行榜类别
     */
    selectCategory(category) {
        if (this.currentCategory === category) return;
        
        // 更新当前类别
        this.currentCategory = category;
        
        // 更新UI状态
        this.updateCategoryTabs();
        this.updateCurrentSelection();
        
        // 重新加载数据
        this.loadLeaderboardData();
    }

    /**
     * 更新难度标签页状态
     */
    updateDifficultyTabs() {
        const tabs = this.containerElement.querySelectorAll('.difficulty-tab');
        tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.difficulty === this.currentDifficulty);
        });
    }

    /**
     * 更新类别标签页状态
     */
    updateCategoryTabs() {
        const tabs = this.containerElement.querySelectorAll('.category-tab');
        tabs.forEach(tab => {
            tab.classList.toggle('active', tab.dataset.category === this.currentCategory);
        });
    }

    /**
     * 更新当前选择显示
     */
    updateCurrentSelection() {
        const difficultyBadge = this.containerElement.querySelector('.difficulty-badge');
        const categoryName = this.containerElement.querySelector('.category-name');
        
        const difficultyVisual = this.difficultyVisuals[this.currentDifficulty];
        const categoryConfig = this.categoryConfig[this.currentCategory];
        
        if (difficultyBadge) {
            difficultyBadge.innerHTML = `
                <span class="badge-icon">${difficultyVisual.icon}</span>
                <span class="badge-text">${difficultyVisual.name}</span>
            `;
            difficultyBadge.style.backgroundColor = difficultyVisual.color;
        }
        
        if (categoryName) {
            categoryName.innerHTML = `
                <span class="category-icon">${categoryConfig.icon}</span>
                <span class="category-text">${categoryConfig.name}</span>
            `;
        }
    }

    /**
     * 加载排行榜数据
     */
    async loadLeaderboardData() {
        try {
            this.showLoadingState();
            
            if (!window.difficultyLeaderboardManager || !window.difficultyLeaderboardManager.initialized) {
                throw new Error('难度排行榜管理器未初始化');
            }
            
            // 获取排行榜数据
            const leaderboard = difficultyLeaderboardManager.getLeaderboard(
                this.currentDifficulty, 
                this.currentCategory, 
                20
            );
            
            if (!leaderboard || leaderboard.entries.length === 0) {
                this.showEmptyState();
                return;
            }
            
            // 显示排行榜数据
            this.displayLeaderboard(leaderboard);
            
            // 加载玩家排名信息
            await this.loadPlayerRankInfo();
            
        } catch (error) {
            console.error('❌ 加载排行榜数据失败:', error);
            this.showErrorState(error.message);
        }
    }

    /**
     * 显示排行榜数据
     */
    displayLeaderboard(leaderboard) {
        const listContainer = this.containerElement.querySelector('.leaderboard-list');
        
        let entriesHTML = '';
        
        leaderboard.entries.forEach((entry, index) => {
            const rank = index + 1;
            const isTopThree = rank <= 3;
            const difficultyVisual = this.difficultyVisuals[entry.difficulty];
            
            entriesHTML += `
                <div class="leaderboard-entry ${isTopThree ? 'top-three' : ''}" data-rank="${rank}">
                    <div class="entry-rank">
                        <span class="rank-number">${rank}</span>
                        ${isTopThree ? `<span class="rank-medal">${this.getRankMedal(rank)}</span>` : ''}
                    </div>
                    
                    <div class="entry-player">
                        <div class="player-name">${entry.playerName}</div>
                        <div class="player-details">
                            <span class="difficulty-indicator" style="color: ${difficultyVisual.color}">
                                ${difficultyVisual.icon} ${difficultyVisual.name}
                            </span>
                            ${entry.level ? `<span class="level">关卡 ${entry.level}</span>` : ''}
                        </div>
                    </div>
                    
                    <div class="entry-score">
                        <div class="score-value">${this.formatScore(entry.score)}</div>
                        ${entry.weightedScore ? `<div class="weighted-score">加权: ${this.formatScore(entry.weightedScore)}</div>` : ''}
                    </div>
                    
                    <div class="entry-stats">
                        ${entry.combo ? `<div class="stat">连击: ${entry.combo}</div>` : ''}
                        ${entry.perfectHits ? `<div class="stat">完美: ${entry.perfectHits}</div>` : ''}
                        ${entry.accuracy ? `<div class="stat">准确率: ${entry.accuracy.toFixed(1)}%</div>` : ''}
                    </div>
                    
                    <div class="entry-time">
                        ${this.formatTime(entry.timestamp)}
                    </div>
                </div>
            `;
        });
        
        listContainer.innerHTML = entriesHTML;
    }

    /**
     * 加载玩家排名信息
     */
    async loadPlayerRankInfo() {
        try {
            if (!window.playerManager || !window.playerManager.getCurrentPlayer) {
                return;
            }
            
            const currentPlayer = playerManager.getCurrentPlayer();
            if (!currentPlayer) {
                return;
            }
            
            const playerRank = difficultyLeaderboardManager.getPlayerRank(
                this.currentDifficulty,
                this.currentCategory,
                currentPlayer.id
            );
            
            const rankContainer = this.containerElement.querySelector('.rank-details');
            
            if (playerRank && playerRank.entry) {
                const difficultyVisual = this.difficultyVisuals[this.currentDifficulty];
                
                rankContainer.innerHTML = `
                    <div class="player-rank-card">
                        <div class="rank-info">
                            <div class="rank-position">
                                <span class="rank-number">#${playerRank.rank}</span>
                                <span class="rank-total">/ ${playerRank.totalEntries}</span>
                            </div>
                            <div class="rank-difficulty" style="color: ${difficultyVisual.color}">
                                ${difficultyVisual.icon} ${difficultyVisual.name}
                            </div>
                        </div>
                        
                        <div class="player-stats">
                            <div class="stat-item">
                                <span class="stat-label">分数</span>
                                <span class="stat-value">${this.formatScore(playerRank.entry.score)}</span>
                            </div>
                            ${playerRank.entry.combo ? `
                                <div class="stat-item">
                                    <span class="stat-label">连击</span>
                                    <span class="stat-value">${playerRank.entry.combo}</span>
                                </div>
                            ` : ''}
                            ${playerRank.entry.perfectHits ? `
                                <div class="stat-item">
                                    <span class="stat-label">完美击中</span>
                                    <span class="stat-value">${playerRank.entry.perfectHits}</span>
                                </div>
                            ` : ''}
                        </div>
                        
                        <div class="rank-actions">
                            <button class="btn btn-small" data-action="viewAllRanks">查看所有难度排名</button>
                        </div>
                    </div>
                `;
            } else {
                rankContainer.innerHTML = `
                    <div class="no-rank-card">
                        <div class="no-rank-icon">🎯</div>
                        <div class="no-rank-text">您还未在此难度下上榜</div>
                        <div class="no-rank-hint">快去游戏获得分数吧！</div>
                    </div>
                `;
            }
        } catch (error) {
            console.error('❌ 加载玩家排名信息失败:', error);
        }
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        const listContainer = this.containerElement.querySelector('.leaderboard-list');
        listContainer.innerHTML = `
            <div class="loading-state">
                <div class="spinner"></div>
                <p>加载排行榜数据...</p>
            </div>
        `;
    }

    /**
     * 显示空状态
     */
    showEmptyState() {
        const listContainer = this.containerElement.querySelector('.leaderboard-list');
        const difficultyVisual = this.difficultyVisuals[this.currentDifficulty];
        const categoryConfig = this.categoryConfig[this.currentCategory];
        
        listContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">${categoryConfig.icon}</div>
                <h3>暂无排行榜数据</h3>
                <p>在 <span style="color: ${difficultyVisual.color}">${difficultyVisual.name}</span> 难度下还没有 ${categoryConfig.name} 记录</p>
                <p>成为第一个上榜的玩家吧！</p>
            </div>
        `;
    }

    /**
     * 显示错误状态
     */
    showErrorState(message) {
        const listContainer = this.containerElement.querySelector('.leaderboard-list');
        listContainer.innerHTML = `
            <div class="error-state">
                <div class="error-icon">❌</div>
                <h3>加载失败</h3>
                <p>${message}</p>
                <button class="btn btn-primary" data-action="refresh">重试</button>
            </div>
        `;
    }

    /**
     * 处理用户操作
     */
    async handleAction(action) {
        switch (action) {
            case 'close':
                this.hide();
                break;
                
            case 'refresh':
                await this.loadLeaderboardData();
                break;
                
            case 'crossDifficulty':
                this.showCrossDifficultyComparison();
                break;
                
            case 'stats':
                this.showStatistics();
                break;
                
            case 'export':
                this.exportLeaderboardData();
                break;
                
            case 'viewAllRanks':
                this.showAllDifficultyRanks();
                break;
        }
    }

    /**
     * 显示跨难度比较
     */
    showCrossDifficultyComparison() {
        // 这里可以实现跨难度比较的弹窗
        console.log('🌟 显示跨难度比较');
    }

    /**
     * 显示统计信息
     */
    showStatistics() {
        // 这里可以实现统计信息的弹窗
        console.log('📊 显示统计信息');
    }

    /**
     * 导出排行榜数据
     */
    exportLeaderboardData() {
        try {
            if (!window.difficultyLeaderboardManager) return;
            
            const exportData = difficultyLeaderboardManager.exportLeaderboardData(
                this.currentDifficulty, 
                this.currentCategory
            );
            
            if (exportData) {
                const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
                    type: 'application/json' 
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `leaderboard_${this.currentDifficulty}_${this.currentCategory}.json`;
                a.click();
                URL.revokeObjectURL(url);
                
                console.log('✅ 排行榜数据已导出');
            }
        } catch (error) {
            console.error('❌ 导出排行榜数据失败:', error);
        }
    }

    /**
     * 显示所有难度排名
     */
    showAllDifficultyRanks() {
        // 这里可以实现显示玩家在所有难度下排名的弹窗
        console.log('🏆 显示所有难度排名');
    }

    /**
     * 获取排名奖牌
     */
    getRankMedal(rank) {
        const medals = ['🥇', '🥈', '🥉'];
        return medals[rank - 1] || '';
    }

    /**
     * 格式化分数
     */
    formatScore(score) {
        if (score >= 1000000) {
            return (score / 1000000).toFixed(1) + 'M';
        } else if (score >= 1000) {
            return (score / 1000).toFixed(1) + 'K';
        }
        return score.toString();
    }

    /**
     * 格式化时间
     */
    formatTime(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        
        if (diff < 60000) { // 1分钟内
            return '刚刚';
        } else if (diff < 3600000) { // 1小时内
            return Math.floor(diff / 60000) + '分钟前';
        } else if (diff < 86400000) { // 1天内
            return Math.floor(diff / 3600000) + '小时前';
        } else if (diff < 2592000000) { // 30天内
            return Math.floor(diff / 86400000) + '天前';
        } else {
            return new Date(timestamp).toLocaleDateString();
        }
    }
}

// 创建全局难度排行榜UI实例
const difficultyLeaderboardUI = new DifficultyLeaderboardUI();

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.DifficultyLeaderboardUI = DifficultyLeaderboardUI;
    window.difficultyLeaderboardUI = difficultyLeaderboardUI;
}

// 支持CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        DifficultyLeaderboardUI,
        difficultyLeaderboardUI
    };
}
