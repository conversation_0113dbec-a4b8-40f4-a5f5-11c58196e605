/**
 * 瞬光捕手 - 输入处理器
 * 统一处理键盘、鼠标和触摸输入
 */

class InputHandler {
    constructor() {
        this.keys = new Set();
        this.mouseState = {
            x: 0,
            y: 0,
            isDown: false,
            button: -1
        };
        this.touchState = {
            active: false,
            touches: [],
            startTime: 0
        };
        this.initialized = false;
        
        // 输入配置
        this.config = {
            doubleTapThreshold: 300, // 双击时间阈值（毫秒）
            longPressThreshold: 500, // 长按时间阈值（毫秒）
            swipeThreshold: 50,      // 滑动距离阈值（像素）
            tapThreshold: 10         // 点击移动阈值（像素）
        };
        
        // 手势状态
        this.gestureState = {
            lastTapTime: 0,
            lastTapPosition: { x: 0, y: 0 },
            longPressTimer: null,
            swipeStart: null
        };
    }

    /**
     * 初始化输入处理器
     */
    init() {
        try {
            this.bindKeyboardEvents();
            this.bindMouseEvents();
            this.bindTouchEvents();
            this.bindGamepadEvents();
            
            this.initialized = true;
            console.log('输入处理器初始化完成');
            
        } catch (error) {
            console.error('输入处理器初始化失败:', error);
        }
    }

    /**
     * 绑定键盘事件
     */
    bindKeyboardEvents() {
        document.addEventListener('keydown', (e) => this.handleKeyDown(e));
        document.addEventListener('keyup', (e) => this.handleKeyUp(e));
        
        // 防止某些按键的默认行为
        document.addEventListener('keydown', (e) => {
            if (['Space', 'ArrowUp', 'ArrowDown', 'ArrowLeft', 'ArrowRight'].includes(e.code)) {
                if (window.screenManager && screenManager.getCurrentScreen() === 'game-screen') {
                    e.preventDefault();
                }
            }
        });
    }

    /**
     * 绑定鼠标事件
     */
    bindMouseEvents() {
        document.addEventListener('mousedown', (e) => this.handleMouseDown(e));
        document.addEventListener('mouseup', (e) => this.handleMouseUp(e));
        document.addEventListener('mousemove', (e) => this.handleMouseMove(e));
        document.addEventListener('wheel', (e) => this.handleMouseWheel(e));
        
        // 防止右键菜单
        document.addEventListener('contextmenu', (e) => {
            if (window.screenManager && screenManager.getCurrentScreen() === 'game-screen') {
                e.preventDefault();
            }
        });
    }

    /**
     * 绑定触摸事件
     */
    bindTouchEvents() {
        document.addEventListener('touchstart', (e) => this.handleTouchStart(e), { passive: false });
        document.addEventListener('touchmove', (e) => this.handleTouchMove(e), { passive: false });
        document.addEventListener('touchend', (e) => this.handleTouchEnd(e), { passive: false });
        document.addEventListener('touchcancel', (e) => this.handleTouchCancel(e), { passive: false });
    }

    /**
     * 绑定手柄事件
     */
    bindGamepadEvents() {
        window.addEventListener('gamepadconnected', (e) => {
            console.log('手柄已连接:', e.gamepad.id);
        });
        
        window.addEventListener('gamepaddisconnected', (e) => {
            console.log('手柄已断开:', e.gamepad.id);
        });
    }

    /**
     * 处理键盘按下
     */
    handleKeyDown(event) {
        this.keys.add(event.code);
        
        // 游戏中的键盘输入
        if (window.screenManager && screenManager.getCurrentScreen() === 'game-screen') {
            this.handleGameKeyDown(event);
        }
        
        // 全局键盘快捷键
        this.handleGlobalKeyDown(event);
    }

    /**
     * 处理键盘释放
     */
    handleKeyUp(event) {
        this.keys.delete(event.code);
    }

    /**
     * 处理游戏中的键盘输入
     */
    handleGameKeyDown(event) {
        if (!window.gameEngine || gameEngine.gameState !== 'playing') return;

        switch (event.code) {
            case 'Space':
            case 'Enter':
                // 空格键或回车键作为点击输入
                const centerX = gameEngine.canvas.width / 2;
                const centerY = gameEngine.canvas.height / 2;
                gameEngine.handleClick(centerX, centerY);
                break;

            case 'KeyP':
                // P键暂停游戏
                if (window.screenManager) {
                    screenManager.pauseGame();
                }
                break;
        }
    }

    /**
     * 处理全局键盘快捷键
     */
    handleGlobalKeyDown(event) {
        switch (event.code) {
            case 'F11':
                // F11切换全屏
                this.toggleFullscreen();
                event.preventDefault();
                break;
                
            case 'KeyM':
                // M键静音
                if (event.ctrlKey) {
                    this.toggleMute();
                    event.preventDefault();
                }
                break;
        }
    }

    /**
     * 处理鼠标按下
     */
    handleMouseDown(event) {
        this.mouseState.isDown = true;
        this.mouseState.button = event.button;
        this.updateMousePosition(event);
        
        // 游戏中的鼠标输入
        if (window.screenManager && screenManager.getCurrentScreen() === 'game-screen' && event.button === 0) {
            this.handleGameClick(event);
        }
    }

    /**
     * 处理鼠标释放
     */
    handleMouseUp(event) {
        this.mouseState.isDown = false;
        this.mouseState.button = -1;
    }

    /**
     * 处理鼠标移动
     */
    handleMouseMove(event) {
        this.updateMousePosition(event);
    }

    /**
     * 处理鼠标滚轮
     */
    handleMouseWheel(event) {
        // 可以用于缩放或其他功能
        if (window.screenManager && screenManager.getCurrentScreen() === 'game-screen') {
            event.preventDefault();
        }
    }

    /**
     * 更新鼠标位置
     */
    updateMousePosition(event) {
        this.mouseState.x = event.clientX;
        this.mouseState.y = event.clientY;
    }

    /**
     * 处理游戏点击
     */
    handleGameClick(event) {
        if (!window.gameEngine || gameEngine.gameState !== 'playing') return;

        const canvas = gameEngine.canvas;
        if (!canvas) return;

        // 检查点击是否在canvas区域内，避免重复处理
        const rect = canvas.getBoundingClientRect();
        const x = event.clientX;
        const y = event.clientY;

        // 只有点击在canvas区域内时才处理游戏逻辑
        if (x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom) {
            console.log('🎯 点击在canvas区域内，由InputHandler处理');
            const canvasX = x - rect.left;
            const canvasY = y - rect.top;
            gameEngine.handleClick(canvasX, canvasY);
        } else {
            console.log('🚫 点击在canvas区域外，忽略游戏逻辑处理');
            // 点击在canvas区域外（UI元素），不处理游戏逻辑，避免错误扣除生命值
        }
    }

    /**
     * 处理触摸开始
     */
    handleTouchStart(event) {
        event.preventDefault();
        
        this.touchState.active = true;
        this.touchState.touches = Array.from(event.touches);
        this.touchState.startTime = Date.now();
        
        if (event.touches.length === 1) {
            const touch = event.touches[0];
            this.handleSingleTouchStart(touch);
        }
    }

    /**
     * 处理单点触摸开始
     */
    handleSingleTouchStart(touch) {
        const now = Date.now();
        const position = { x: touch.clientX, y: touch.clientY };
        
        // 检查双击
        const timeDiff = now - this.gestureState.lastTapTime;
        const distance = this.calculateDistance(position, this.gestureState.lastTapPosition);
        
        if (timeDiff < this.config.doubleTapThreshold && distance < this.config.tapThreshold) {
            this.handleDoubleTap(touch);
            return;
        }
        
        // 记录单击信息
        this.gestureState.lastTapTime = now;
        this.gestureState.lastTapPosition = position;
        this.gestureState.swipeStart = position;
        
        // 设置长按定时器
        this.gestureState.longPressTimer = setTimeout(() => {
            this.handleLongPress(touch);
        }, this.config.longPressThreshold);
    }

    /**
     * 处理触摸移动
     */
    handleTouchMove(event) {
        event.preventDefault();
        
        if (this.gestureState.longPressTimer) {
            clearTimeout(this.gestureState.longPressTimer);
            this.gestureState.longPressTimer = null;
        }
        
        this.touchState.touches = Array.from(event.touches);
    }

    /**
     * 处理触摸结束
     */
    handleTouchEnd(event) {
        event.preventDefault();
        
        if (this.gestureState.longPressTimer) {
            clearTimeout(this.gestureState.longPressTimer);
            this.gestureState.longPressTimer = null;
        }
        
        if (event.changedTouches.length === 1 && this.touchState.touches.length === 1) {
            const touch = event.changedTouches[0];
            this.handleSingleTouchEnd(touch);
        }
        
        this.touchState.active = false;
        this.touchState.touches = [];
    }

    /**
     * 处理单点触摸结束
     */
    handleSingleTouchEnd(touch) {
        const endPosition = { x: touch.clientX, y: touch.clientY };
        const distance = this.calculateDistance(this.gestureState.swipeStart, endPosition);
        
        if (distance < this.config.tapThreshold) {
            // 这是一个点击
            this.handleTap(touch);
        } else if (distance > this.config.swipeThreshold) {
            // 这是一个滑动
            this.handleSwipe(this.gestureState.swipeStart, endPosition);
        }
    }

    /**
     * 处理触摸取消
     */
    handleTouchCancel(event) {
        event.preventDefault();
        
        if (this.gestureState.longPressTimer) {
            clearTimeout(this.gestureState.longPressTimer);
            this.gestureState.longPressTimer = null;
        }
        
        this.touchState.active = false;
        this.touchState.touches = [];
    }

    /**
     * 处理点击
     */
    handleTap(touch) {
        // 获取当前屏幕
        const currentScreen = window.screenManager ? screenManager.getCurrentScreen() : null;

        if (currentScreen === 'game-screen') {
            // 游戏界面的触摸处理
            this.handleGameTouch(touch);
        } else {
            // 其他界面的触摸处理 - 模拟鼠标点击事件
            this.simulateMouseClickFromTouch(touch);
        }
    }

    /**
     * 从触摸事件模拟鼠标点击事件
     * 用于处理非游戏界面的触摸交互
     */
    simulateMouseClickFromTouch(touch) {
        const target = document.elementFromPoint(touch.clientX, touch.clientY);
        if (target) {
            console.log('🎯 触摸转换为点击事件:', target.tagName, target.className);

            // 创建并派发鼠标事件
            const mouseEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                clientX: touch.clientX,
                clientY: touch.clientY,
                button: 0
            });

            target.dispatchEvent(mouseEvent);
        }
    }

    /**
     * 处理双击
     */
    handleDoubleTap(touch) {
        console.log('🎯 双击检测');

        // 获取当前屏幕
        const currentScreen = window.screenManager ? screenManager.getCurrentScreen() : null;

        if (currentScreen === 'game-screen') {
            // 游戏界面的双击处理（可以用于特殊功能，如暂停等）
            console.log('🎮 游戏界面双击');
        } else {
            // 其他界面的双击处理 - 模拟双击事件
            const target = document.elementFromPoint(touch.clientX, touch.clientY);
            if (target) {
                console.log('🎯 触摸双击转换为双击事件:', target.tagName, target.className);

                // 创建并派发双击事件
                const dblClickEvent = new MouseEvent('dblclick', {
                    bubbles: true,
                    cancelable: true,
                    clientX: touch.clientX,
                    clientY: touch.clientY,
                    button: 0
                });

                target.dispatchEvent(dblClickEvent);
            }
        }
    }

    /**
     * 处理长按
     */
    handleLongPress(touch) {
        console.log('🎯 长按检测');

        // 获取当前屏幕
        const currentScreen = window.screenManager ? screenManager.getCurrentScreen() : null;

        if (currentScreen === 'game-screen') {
            // 游戏界面的长按处理（可以用于显示菜单、暂停等）
            console.log('🎮 游戏界面长按');
        } else {
            // 其他界面的长按处理 - 模拟右键菜单或长按事件
            const target = document.elementFromPoint(touch.clientX, touch.clientY);
            if (target) {
                console.log('🎯 触摸长按转换为上下文菜单事件:', target.tagName, target.className);

                // 创建并派发上下文菜单事件（右键菜单）
                const contextMenuEvent = new MouseEvent('contextmenu', {
                    bubbles: true,
                    cancelable: true,
                    clientX: touch.clientX,
                    clientY: touch.clientY,
                    button: 2
                });

                target.dispatchEvent(contextMenuEvent);
            }
        }
    }

    /**
     * 处理滑动
     */
    handleSwipe(start, end) {
        const dx = end.x - start.x;
        const dy = end.y - start.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        // 确定滑动方向
        let direction = '';
        if (Math.abs(dx) > Math.abs(dy)) {
            direction = dx > 0 ? 'right' : 'left';
        } else {
            direction = dy > 0 ? 'down' : 'up';
        }

        console.log('🎯 滑动检测:', { direction, distance, dx, dy });

        // 获取当前屏幕
        const currentScreen = window.screenManager ? screenManager.getCurrentScreen() : null;

        if (currentScreen === 'game-screen') {
            // 游戏界面的滑动处理
            this.handleGameSwipe(direction, distance, start, end);
        } else {
            // 其他界面的滑动处理
            this.handleUISwipe(direction, distance, start, end);
        }
    }

    /**
     * 处理游戏界面的滑动
     */
    handleGameSwipe(direction, distance, start, end) {
        console.log('🎮 游戏界面滑动:', direction);
        // 可以用于游戏内的特殊操作
    }

    /**
     * 处理UI界面的滑动
     */
    handleUISwipe(direction, distance, start, end) {
        console.log('🎯 UI界面滑动:', direction);

        // 可以用于界面导航
        if (direction === 'left' && distance > 100) {
            // 向左滑动可能表示"返回"或"下一页"
            console.log('🎯 向左滑动 - 可用于导航');
        } else if (direction === 'right' && distance > 100) {
            // 向右滑动可能表示"前进"或"上一页"
            console.log('🎯 向右滑动 - 可用于导航');
        }
    }

    /**
     * 处理游戏触摸
     */
    handleGameTouch(touch) {
        if (!window.gameEngine || gameEngine.gameState !== 'playing') return;

        const canvas = gameEngine.canvas;
        if (!canvas) return;

        // 检查触摸是否在canvas区域内，避免重复处理
        const rect = canvas.getBoundingClientRect();
        const x = touch.clientX;
        const y = touch.clientY;

        // 只有触摸在canvas区域内时才处理游戏逻辑
        if (x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom) {
            console.log('🎯 触摸在canvas区域内，由InputHandler处理');
            const canvasX = x - rect.left;
            const canvasY = y - rect.top;
            gameEngine.handleClick(canvasX, canvasY);
        } else {
            console.log('🚫 触摸在canvas区域外，忽略游戏逻辑处理');
            // 触摸在canvas区域外（UI元素），不处理游戏逻辑，避免错误扣除生命值
        }
    }

    /**
     * 计算两点间距离
     */
    calculateDistance(point1, point2) {
        const dx = point2.x - point1.x;
        const dy = point2.y - point1.y;
        return Math.sqrt(dx * dx + dy * dy);
    }

    /**
     * 切换全屏
     */
    toggleFullscreen() {
        if (!document.fullscreenElement) {
            document.documentElement.requestFullscreen().catch(err => {
                console.log('无法进入全屏模式:', err);
            });
        } else {
            document.exitFullscreen();
        }
    }

    /**
     * 切换静音
     */
    toggleMute() {
        // TODO: 实现音频静音功能
        console.log('切换静音');
    }

    /**
     * 检查按键是否按下
     */
    isKeyPressed(keyCode) {
        return this.keys.has(keyCode);
    }

    /**
     * 获取鼠标状态
     */
    getMouseState() {
        return { ...this.mouseState };
    }

    /**
     * 获取触摸状态
     */
    getTouchState() {
        return { ...this.touchState };
    }

    /**
     * 更新手柄输入
     */
    updateGamepad() {
        const gamepads = navigator.getGamepads();
        
        for (let i = 0; i < gamepads.length; i++) {
            const gamepad = gamepads[i];
            if (gamepad) {
                this.handleGamepadInput(gamepad);
            }
        }
    }

    /**
     * 处理手柄输入
     */
    handleGamepadInput(gamepad) {
        // 检查按钮
        if (gamepad.buttons[0].pressed) { // A按钮
            if (window.screenManager && window.gameEngine &&
                screenManager.getCurrentScreen() === 'game-screen' && gameEngine.gameState === 'playing') {
                const centerX = gameEngine.canvas.width / 2;
                const centerY = gameEngine.canvas.height / 2;
                gameEngine.handleClick(centerX, centerY);
            }
        }
        
        // 检查摇杆
        const leftStickX = gamepad.axes[0];
        const leftStickY = gamepad.axes[1];
        
        // 可以用于移动光标或其他功能
        if (Math.abs(leftStickX) > 0.1 || Math.abs(leftStickY) > 0.1) {
            // 处理摇杆输入
        }
    }

    /**
     * 清理资源
     */
    cleanup() {
        if (this.gestureState.longPressTimer) {
            clearTimeout(this.gestureState.longPressTimer);
            this.gestureState.longPressTimer = null;
        }
        
        this.keys.clear();
        this.touchState.active = false;
        this.touchState.touches = [];
    }
}

// 创建全局输入处理器实例
window.inputHandler = new InputHandler();
