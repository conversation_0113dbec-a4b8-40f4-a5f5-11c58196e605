/**
 * 瞬光捕手 - 用户凭证管理UI组件
 * 提供用户友好的凭证创建和管理界面
 */

class CredentialManagerUI {
    constructor() {
        this.isVisible = false;
        this.currentStep = 'select'; // select, create, verify
        this.selectedCredentialType = null;
        
        console.log('🎨 凭证管理UI组件已创建');
    }

    /**
     * 显示凭证管理界面
     */
    show() {
        this.createUI();
        this.isVisible = true;
        
        // 添加到页面
        document.body.appendChild(this.containerElement);
        
        // 添加动画效果
        requestAnimationFrame(() => {
            this.containerElement.classList.add('show');
        });
    }

    /**
     * 隐藏凭证管理界面
     */
    hide() {
        if (this.containerElement) {
            this.containerElement.classList.remove('show');
            
            setTimeout(() => {
                if (this.containerElement && this.containerElement.parentNode) {
                    this.containerElement.parentNode.removeChild(this.containerElement);
                }
                this.isVisible = false;
            }, 300);
        }
    }

    /**
     * 创建UI元素
     */
    createUI() {
        // 创建容器
        this.containerElement = document.createElement('div');
        this.containerElement.className = 'credential-manager-overlay';
        this.containerElement.innerHTML = this.getOverlayHTML();
        
        // 绑定事件
        this.bindEvents();
        
        // 显示当前步骤
        this.showStep(this.currentStep);
    }

    /**
     * 获取覆盖层HTML
     */
    getOverlayHTML() {
        return `
            <div class="credential-manager-modal">
                <div class="modal-header">
                    <h2>🔐 用户登录</h2>
                    <button class="close-btn" data-action="close">×</button>
                </div>
                
                <div class="modal-content">
                    <!-- 选择凭证类型步骤 -->
                    <div class="step-content" data-step="select">
                        <h3>选择登录方式</h3>
                        <p class="step-description">选择一种方式来创建您的游戏身份，以便在多设备间同步游戏数据。</p>
                        
                        <div class="credential-types">
                            ${this.getCredentialTypesHTML()}
                        </div>
                        
                        <div class="step-actions">
                            <button class="btn btn-secondary" data-action="playAnonymous">匿名游戏</button>
                        </div>
                    </div>
                    
                    <!-- 创建凭证步骤 -->
                    <div class="step-content" data-step="create">
                        <div class="create-form-container">
                            <!-- 动态生成的表单内容 -->
                        </div>
                        
                        <div class="step-actions">
                            <button class="btn btn-secondary" data-action="back">返回</button>
                            <button class="btn btn-primary" data-action="createCredential">创建</button>
                        </div>
                    </div>
                    
                    <!-- 验证步骤 -->
                    <div class="step-content" data-step="verify">
                        <div class="verify-container">
                            <!-- 动态生成的验证内容 -->
                        </div>
                        
                        <div class="step-actions">
                            <button class="btn btn-secondary" data-action="back">返回</button>
                            <button class="btn btn-primary" data-action="verify">验证</button>
                        </div>
                    </div>
                    
                    <!-- 成功步骤 -->
                    <div class="step-content" data-step="success">
                        <div class="success-message">
                            <div class="success-icon">✅</div>
                            <h3>登录成功！</h3>
                            <p class="credential-info"></p>
                            <p class="success-description">您的游戏数据现在可以在多设备间同步了。</p>
                        </div>
                        
                        <div class="step-actions">
                            <button class="btn btn-primary" data-action="startGame">开始游戏</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    /**
     * 获取凭证类型HTML
     */
    getCredentialTypesHTML() {
        const types = userCredentialSystem.getSupportedCredentialTypes();
        
        return types.map(type => `
            <div class="credential-type ${type.recommended ? 'recommended' : ''}" 
                 data-type="${type.type}">
                <div class="type-icon">${type.icon}</div>
                <div class="type-info">
                    <h4>${type.name}</h4>
                    <p>${type.description}</p>
                    ${type.recommended ? '<span class="recommended-badge">推荐</span>' : ''}
                </div>
                <div class="type-arrow">→</div>
            </div>
        `).join('');
    }

    /**
     * 绑定事件
     */
    bindEvents() {
        // 关闭按钮
        this.containerElement.addEventListener('click', (e) => {
            if (e.target.dataset.action === 'close' || e.target === this.containerElement) {
                this.hide();
            }
        });

        // 凭证类型选择
        this.containerElement.addEventListener('click', (e) => {
            const credentialType = e.target.closest('.credential-type');
            if (credentialType) {
                this.selectedCredentialType = credentialType.dataset.type;
                this.showStep('create');
            }
        });

        // 按钮事件
        this.containerElement.addEventListener('click', (e) => {
            const action = e.target.dataset.action;
            if (action) {
                this.handleAction(action, e);
            }
        });

        // 表单提交
        this.containerElement.addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleAction('createCredential', e);
        });
    }

    /**
     * 处理用户操作
     */
    async handleAction(action, event) {
        switch (action) {
            case 'close':
                this.hide();
                break;
                
            case 'playAnonymous':
                await this.createAnonymousCredential();
                break;
                
            case 'back':
                this.goBack();
                break;
                
            case 'createCredential':
                await this.createCredential();
                break;
                
            case 'verify':
                await this.verifyCredential();
                break;
                
            case 'startGame':
                this.hide();
                this.startGame();
                break;
        }
    }

    /**
     * 显示指定步骤
     */
    showStep(step) {
        this.currentStep = step;
        
        // 隐藏所有步骤
        const steps = this.containerElement.querySelectorAll('.step-content');
        steps.forEach(s => s.classList.remove('active'));
        
        // 显示当前步骤
        const currentStepElement = this.containerElement.querySelector(`[data-step="${step}"]`);
        if (currentStepElement) {
            currentStepElement.classList.add('active');
        }
        
        // 根据步骤生成内容
        switch (step) {
            case 'create':
                this.generateCreateForm();
                break;
            case 'verify':
                this.generateVerifyForm();
                break;
        }
    }

    /**
     * 生成创建表单
     */
    generateCreateForm() {
        const container = this.containerElement.querySelector('.create-form-container');
        const types = userCredentialSystem.credentialTypes;
        
        let formHTML = '';
        
        switch (this.selectedCredentialType) {
            case types.USERNAME_SUFFIX:
                formHTML = `
                    <div class="form-header">
                        <h3>👤 创建用户名</h3>
                        <p>输入一个您喜欢的用户名，系统会自动添加随机后缀以确保唯一性。</p>
                    </div>
                    
                    <form class="credential-form">
                        <div class="form-group">
                            <label for="username">用户名</label>
                            <input type="text" id="username" name="username" 
                                   placeholder="输入用户名 (3-16个字符)" 
                                   minlength="3" maxlength="16" required>
                            <div class="form-hint">
                                支持中文、英文、数字和下划线，最终格式为：用户名#随机码
                            </div>
                            <div class="form-error"></div>
                        </div>
                        
                        <div class="preview-section">
                            <label>预览效果：</label>
                            <div class="username-preview">
                                <span class="preview-username">您的用户名</span>
                                <span class="preview-separator">#</span>
                                <span class="preview-suffix">A1B2</span>
                            </div>
                        </div>
                    </form>
                `;
                break;
                
            case types.EMAIL_BASED:
                formHTML = `
                    <div class="form-header">
                        <h3>📧 邮箱登录</h3>
                        <p>使用邮箱地址创建账号，我们会发送验证码到您的邮箱。</p>
                    </div>
                    
                    <form class="credential-form">
                        <div class="form-group">
                            <label for="email">邮箱地址</label>
                            <input type="email" id="email" name="email" 
                                   placeholder="输入您的邮箱地址" required>
                            <div class="form-hint">
                                请确保邮箱地址正确，我们将发送验证码到此邮箱
                            </div>
                            <div class="form-error"></div>
                        </div>
                    </form>
                `;
                break;
                
            case types.DEVICE_FINGERPRINT:
                formHTML = `
                    <div class="form-header">
                        <h3>📱 设备登录</h3>
                        <p>基于您的设备特征自动创建唯一身份，无需记忆任何信息。</p>
                    </div>
                    
                    <div class="device-info">
                        <div class="info-item">
                            <span class="info-label">设备类型：</span>
                            <span class="info-value">${this.getDeviceType()}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">浏览器：</span>
                            <span class="info-value">${this.getBrowserInfo()}</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">屏幕分辨率：</span>
                            <span class="info-value">${screen.width}×${screen.height}</span>
                        </div>
                    </div>
                    
                    <div class="warning-box">
                        <strong>⚠️ 注意：</strong>
                        设备登录方式绑定当前设备，如果清除浏览器数据或更换设备，可能无法找回账号。
                    </div>
                `;
                break;
        }
        
        container.innerHTML = formHTML;
        
        // 绑定实时预览事件
        if (this.selectedCredentialType === types.USERNAME_SUFFIX) {
            this.bindUsernamePreview();
        }
    }

    /**
     * 生成验证表单
     */
    generateVerifyForm() {
        const container = this.containerElement.querySelector('.verify-container');
        
        if (this.selectedCredentialType === userCredentialSystem.credentialTypes.EMAIL_BASED) {
            container.innerHTML = `
                <div class="verify-header">
                    <h3>📧 邮箱验证</h3>
                    <p>我们已向您的邮箱发送了6位数字验证码，请查收并输入。</p>
                </div>
                
                <form class="verify-form">
                    <div class="form-group">
                        <label for="verifyCode">验证码</label>
                        <input type="text" id="verifyCode" name="verifyCode" 
                               placeholder="输入6位验证码" 
                               maxlength="6" pattern="[0-9]{6}" required>
                        <div class="form-hint">
                            没有收到验证码？请检查垃圾邮件文件夹
                        </div>
                        <div class="form-error"></div>
                    </div>
                    
                    <div class="resend-section">
                        <button type="button" class="btn-link" data-action="resendCode">
                            重新发送验证码
                        </button>
                    </div>
                </form>
            `;
        }
    }

    /**
     * 绑定用户名预览事件
     */
    bindUsernamePreview() {
        const usernameInput = this.containerElement.querySelector('#username');
        const previewUsername = this.containerElement.querySelector('.preview-username');
        
        if (usernameInput && previewUsername) {
            usernameInput.addEventListener('input', (e) => {
                const value = e.target.value || '您的用户名';
                previewUsername.textContent = value;
                
                // 实时验证
                this.validateUsernameInput(e.target);
            });
        }
    }

    /**
     * 验证用户名输入
     */
    validateUsernameInput(input) {
        const validation = userCredentialSystem.validateUsername(input.value);
        const errorElement = input.parentNode.querySelector('.form-error');
        
        if (validation.valid) {
            input.classList.remove('error');
            errorElement.textContent = '';
        } else {
            input.classList.add('error');
            errorElement.textContent = validation.reason;
        }
        
        return validation.valid;
    }

    /**
     * 创建凭证
     */
    async createCredential() {
        try {
            const types = userCredentialSystem.credentialTypes;
            let credential = null;
            
            switch (this.selectedCredentialType) {
                case types.USERNAME_SUFFIX:
                    const username = this.containerElement.querySelector('#username').value;
                    if (!this.validateUsernameInput(this.containerElement.querySelector('#username'))) {
                        return;
                    }
                    credential = await userCredentialSystem.createUsernameSuffixCredential(username);
                    this.showSuccessStep(credential);
                    break;
                    
                case types.EMAIL_BASED:
                    const email = this.containerElement.querySelector('#email').value;
                    credential = await userCredentialSystem.createEmailBasedCredential(email);
                    this.showStep('verify');
                    break;
                    
                case types.DEVICE_FINGERPRINT:
                    credential = await userCredentialSystem.createDeviceFingerprintCredential();
                    this.showSuccessStep(credential);
                    break;
            }
            
        } catch (error) {
            this.showError(error.message);
        }
    }

    /**
     * 验证凭证
     */
    async verifyCredential() {
        try {
            const verifyCode = this.containerElement.querySelector('#verifyCode').value;
            const success = await userCredentialSystem.verifyEmailCode(verifyCode);
            
            if (success) {
                const credential = userCredentialSystem.getCurrentCredential();
                this.showSuccessStep(credential);
            } else {
                this.showError('验证码错误，请重新输入');
            }
        } catch (error) {
            this.showError(error.message);
        }
    }

    /**
     * 创建匿名凭证
     */
    async createAnonymousCredential() {
        try {
            const credential = await userCredentialSystem.createAnonymousCredential();
            this.hide();
            this.startGame();
        } catch (error) {
            this.showError(error.message);
        }
    }

    /**
     * 显示成功步骤
     */
    showSuccessStep(credential) {
        this.showStep('success');
        
        const credentialInfo = this.containerElement.querySelector('.credential-info');
        credentialInfo.textContent = `欢迎，${credential.displayName}！`;
    }

    /**
     * 显示错误信息
     */
    showError(message) {
        // 创建错误提示
        const errorElement = document.createElement('div');
        errorElement.className = 'error-toast';
        errorElement.textContent = message;
        
        document.body.appendChild(errorElement);
        
        // 自动移除
        setTimeout(() => {
            if (errorElement.parentNode) {
                errorElement.parentNode.removeChild(errorElement);
            }
        }, 3000);
    }

    /**
     * 返回上一步
     */
    goBack() {
        switch (this.currentStep) {
            case 'create':
                this.showStep('select');
                break;
            case 'verify':
                this.showStep('create');
                break;
        }
    }

    /**
     * 开始游戏
     */
    startGame() {
        // 触发游戏开始事件
        if (typeof window !== 'undefined' && window.dispatchEvent) {
            const event = new CustomEvent('gameStart', {
                detail: {
                    credential: userCredentialSystem.getCurrentCredential()
                }
            });
            window.dispatchEvent(event);
        }
        
        console.log('🎮 开始游戏');
    }

    /**
     * 获取设备类型
     */
    getDeviceType() {
        const userAgent = navigator.userAgent;
        if (/Mobile|Android|iPhone|iPad/.test(userAgent)) {
            return '移动设备';
        } else if (/Tablet/.test(userAgent)) {
            return '平板设备';
        } else {
            return '桌面设备';
        }
    }

    /**
     * 获取浏览器信息
     */
    getBrowserInfo() {
        const userAgent = navigator.userAgent;
        if (userAgent.includes('Chrome')) return 'Chrome';
        if (userAgent.includes('Firefox')) return 'Firefox';
        if (userAgent.includes('Safari')) return 'Safari';
        if (userAgent.includes('Edge')) return 'Edge';
        return '其他浏览器';
    }
}

// 创建全局凭证管理UI实例
const credentialManagerUI = new CredentialManagerUI();

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.CredentialManagerUI = CredentialManagerUI;
    window.credentialManagerUI = credentialManagerUI;
}

// 支持CommonJS
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        CredentialManagerUI,
        credentialManagerUI
    };
}
