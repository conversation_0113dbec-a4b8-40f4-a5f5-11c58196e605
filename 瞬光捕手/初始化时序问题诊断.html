<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初始化时序问题诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #16213e;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.danger {
            background: #f44336;
        }
        .test-button.warning {
            background: #ff9800;
        }
        .test-button.secondary {
            background: #2196F3;
        }
        .results {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
        }
        .results.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .results.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .results.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .results.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .log-capture {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 11px;
            max-height: 300px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 初始化时序问题诊断</h1>
        
        <div class="section">
            <h2>📋 问题分析</h2>
            <div class="results warning">
基于日志分析发现的关键问题：

🔍 时序问题：
1. 用户管理界面等待用户管理器初始化5秒超时
2. 超时后仍然获取到了用户管理器实例
3. 但在用户创建时，用户管理器仍显示未初始化状态

⚠️ 可能原因：
1. 用户管理器init()方法抛出异常，导致initialized状态不一致
2. 异步初始化过程中的错误处理问题
3. 我们添加的超时保护机制可能未正确生效
4. 用户系统适配器与用户管理器的初始化时序问题

🎯 诊断重点：
- 检查用户管理器的实际初始化状态
- 验证超时保护机制是否被调用
- 分析初始化过程中的异常和错误
- 确认修复的方法是否正确加载和执行
            </div>
        </div>

        <div class="section">
            <h2>🎮 游戏环境诊断</h2>
            <div class="test-controls">
                <button class="test-button" onclick="loadGameWithDiagnostics()">加载游戏并捕获初始化日志</button>
                <button class="test-button secondary" onclick="analyzeInitSequence()">分析初始化序列</button>
                <button class="test-button warning" onclick="debugUserManager()">调试用户管理器</button>
                <button class="test-button danger" onclick="resetDiagnostics()">重置诊断</button>
            </div>
            
            <div class="iframe-container" id="game-container" style="display: none;">
                <iframe id="game-frame" src=""></iframe>
            </div>
            
            <div id="game-diagnostics" class="results info">
                点击"加载游戏并捕获初始化日志"开始诊断...
            </div>
        </div>

        <div class="section">
            <h2>📊 初始化日志捕获</h2>
            <div class="test-controls">
                <button class="test-button" onclick="startLogCapture()">开始日志捕获</button>
                <button class="test-button secondary" onclick="stopLogCapture()">停止捕获</button>
                <button class="test-button secondary" onclick="analyzeCapture()">分析捕获的日志</button>
            </div>
            
            <div class="log-capture" id="log-capture">
                日志捕获区域...
            </div>
        </div>

        <div class="section">
            <h2>🔧 用户管理器状态检查</h2>
            <div class="test-controls">
                <button class="test-button" onclick="checkUserManagerState()">检查用户管理器状态</button>
                <button class="test-button secondary" onclick="testInitMethods()">测试初始化方法</button>
                <button class="test-button secondary" onclick="forceReinitialize()">强制重新初始化</button>
            </div>
            
            <div id="user-manager-state" class="results info">
                点击按钮开始检查...
            </div>
        </div>

        <div class="section">
            <h2>⚡ 修复验证</h2>
            <div class="test-controls">
                <button class="test-button" onclick="verifyTimeoutProtection()">验证超时保护机制</button>
                <button class="test-button secondary" onclick="testLayeredInit()">测试分层初始化</button>
                <button class="test-button warning" onclick="simulateInitFailure()">模拟初始化失败</button>
            </div>
            
            <div id="fix-verification" class="results info">
                点击按钮开始验证...
            </div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let logCapture = [];
        let originalConsole = {};
        let isCapturing = false;

        function loadGameWithDiagnostics() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            
            updateResults('game-diagnostics', 'info', '🔄 正在加载游戏并准备诊断...');
            
            // 开始日志捕获
            startLogCapture();
            
            frame.src = './index.html';
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                updateResults('game-diagnostics', 'success', '✅ 游戏已加载，开始深度诊断...');
                
                // 等待一段时间让初始化完成
                setTimeout(() => {
                    performDeepDiagnostics();
                }, 20000); // 等待20秒，确保初始化完成
            };

            frame.onerror = function(error) {
                updateResults('game-diagnostics', 'error', `❌ 游戏加载失败: ${error}`);
            };
        }

        function startLogCapture() {
            if (isCapturing) return;
            
            isCapturing = true;
            logCapture = [];
            
            // 保存原始console方法
            originalConsole.log = console.log;
            originalConsole.warn = console.warn;
            originalConsole.error = console.error;
            
            // 重写console方法
            console.log = function(...args) {
                const message = args.join(' ');
                logCapture.push(`[LOG] ${new Date().toLocaleTimeString()}: ${message}`);
                updateLogDisplay();
                originalConsole.log.apply(console, args);
            };
            
            console.warn = function(...args) {
                const message = args.join(' ');
                logCapture.push(`[WARN] ${new Date().toLocaleTimeString()}: ${message}`);
                updateLogDisplay();
                originalConsole.warn.apply(console, args);
            };
            
            console.error = function(...args) {
                const message = args.join(' ');
                logCapture.push(`[ERROR] ${new Date().toLocaleTimeString()}: ${message}`);
                updateLogDisplay();
                originalConsole.error.apply(console, args);
            };
            
            updateResults('game-diagnostics', 'info', '📝 日志捕获已开始...');
        }

        function stopLogCapture() {
            if (!isCapturing) return;
            
            isCapturing = false;
            
            // 恢复原始console方法
            console.log = originalConsole.log;
            console.warn = originalConsole.warn;
            console.error = originalConsole.error;
            
            updateResults('game-diagnostics', 'success', '📝 日志捕获已停止');
        }

        function updateLogDisplay() {
            const logElement = document.getElementById('log-capture');
            const recentLogs = logCapture.slice(-50); // 只显示最近50条
            logElement.textContent = recentLogs.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        function analyzeCapture() {
            if (logCapture.length === 0) {
                updateResults('game-diagnostics', 'warning', '⚠️ 没有捕获到日志');
                return;
            }

            let report = '📊 日志分析报告:\n\n';
            
            // 分析初始化相关日志
            const initLogs = logCapture.filter(log => 
                log.includes('初始化') || log.includes('init') || log.includes('UserManager')
            );
            
            report += `初始化相关日志 (${initLogs.length}条):\n`;
            initLogs.forEach(log => {
                report += `  ${log}\n`;
            });
            
            // 分析错误日志
            const errorLogs = logCapture.filter(log => log.includes('[ERROR]'));
            if (errorLogs.length > 0) {
                report += `\n❌ 错误日志 (${errorLogs.length}条):\n`;
                errorLogs.forEach(log => {
                    report += `  ${log}\n`;
                });
            }
            
            // 分析警告日志
            const warnLogs = logCapture.filter(log => log.includes('[WARN]'));
            if (warnLogs.length > 0) {
                report += `\n⚠️ 警告日志 (${warnLogs.length}条):\n`;
                warnLogs.forEach(log => {
                    report += `  ${log}\n`;
                });
            }
            
            // 检查关键日志
            const hasUserManagerInit = logCapture.some(log => log.includes('用户管理器初始化完成'));
            const hasTimeout = logCapture.some(log => log.includes('初始化超时'));
            const hasQuickInit = logCapture.some(log => log.includes('快速初始化'));
            
            report += '\n🔍 关键状态检查:\n';
            report += `  用户管理器初始化完成: ${hasUserManagerInit ? '✅' : '❌'}\n`;
            report += `  出现超时: ${hasTimeout ? '⚠️' : '✅'}\n`;
            report += `  触发快速初始化: ${hasQuickInit ? '⚠️' : '✅'}\n`;
            
            updateResults('game-diagnostics', 'info', report);
        }

        function performDeepDiagnostics() {
            if (!gameFrame) {
                updateResults('game-diagnostics', 'error', '❌ 游戏未加载');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 深度诊断报告:\n\n';

                // 检查用户系统适配器
                if (gameWindow.userSystemAdapter) {
                    report += '用户系统适配器:\n';
                    report += `  存在: ✅\n`;
                    report += `  已初始化: ${gameWindow.userSystemAdapter.initialized ? '✅' : '❌'}\n`;
                    
                    const userManager = gameWindow.userSystemAdapter.getUserManager();
                    if (userManager) {
                        report += '\n用户管理器:\n';
                        report += `  存在: ✅\n`;
                        report += `  已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                        report += `  构造函数: ${userManager.constructor.name}\n`;
                        
                        // 检查我们添加的方法
                        const methods = ['initWithTimeout', 'performInitialization', 'quickInitialization', 'loadUserListOptimized'];
                        report += '\n修复方法检查:\n';
                        methods.forEach(method => {
                            const exists = typeof userManager[method] === 'function';
                            report += `  ${method}: ${exists ? '✅' : '❌'}\n`;
                        });
                        
                        // 检查存储服务
                        if (userManager.storageService) {
                            report += '\n存储服务:\n';
                            report += `  存在: ✅\n`;
                            report += `  已初始化: ${userManager.storageService.initialized ? '✅' : '❌'}\n`;
                            report += `  类型: ${userManager.storageService.storageType || '未知'}\n`;
                        } else {
                            report += '\n存储服务: ❌ 不存在\n';
                        }
                        
                        // 检查用户数据
                        report += '\n用户数据:\n';
                        report += `  用户数量: ${userManager.users ? userManager.users.size : 0}\n`;
                        report += `  当前用户: ${userManager.currentUser ? userManager.currentUser.displayName : '无'}\n`;
                        
                    } else {
                        report += '\n用户管理器: ❌ 不存在\n';
                    }
                } else {
                    report += '用户系统适配器: ❌ 不存在\n';
                }

                // 检查用户管理界面
                if (gameWindow.userManagementUI) {
                    report += '\n用户管理界面:\n';
                    report += `  存在: ✅\n`;
                    report += `  已初始化: ${gameWindow.userManagementUI.initialized ? '✅' : '❌'}\n`;
                    report += `  用户管理器引用: ${gameWindow.userManagementUI.userManager ? '✅' : '❌'}\n`;
                } else {
                    report += '\n用户管理界面: ❌ 不存在\n';
                }

                updateResults('game-diagnostics', 'info', report);

            } catch (error) {
                updateResults('game-diagnostics', 'error', `❌ 诊断过程中出现错误: ${error.message}`);
            }
        }

        function checkUserManagerState() {
            if (!gameFrame) {
                updateResults('user-manager-state', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔧 用户管理器状态详细检查:\n\n';

                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.getUserManager) {
                    const userManager = gameWindow.userSystemAdapter.getUserManager();
                    
                    if (userManager) {
                        report += '基本状态:\n';
                        report += `  实例类型: ${userManager.constructor.name}\n`;
                        report += `  已初始化: ${userManager.initialized}\n`;
                        report += `  存储服务: ${userManager.storageService ? '存在' : '不存在'}\n`;
                        
                        if (userManager.storageService) {
                            report += `  存储服务已初始化: ${userManager.storageService.initialized}\n`;
                        }
                        
                        // 尝试调用初始化方法
                        report += '\n方法可用性测试:\n';
                        try {
                            if (typeof userManager.initWithTimeout === 'function') {
                                report += `  initWithTimeout: ✅ 可调用\n`;
                            } else {
                                report += `  initWithTimeout: ❌ 不存在或不可调用\n`;
                            }
                            
                            if (typeof userManager.performInitialization === 'function') {
                                report += `  performInitialization: ✅ 可调用\n`;
                            } else {
                                report += `  performInitialization: ❌ 不存在或不可调用\n`;
                            }
                            
                            if (typeof userManager.quickInitialization === 'function') {
                                report += `  quickInitialization: ✅ 可调用\n`;
                            } else {
                                report += `  quickInitialization: ❌ 不存在或不可调用\n`;
                            }
                        } catch (error) {
                            report += `  方法检查出错: ${error.message}\n`;
                        }
                        
                        // 检查事件监听器
                        report += '\n事件系统:\n';
                        if (userManager.eventListeners) {
                            report += `  事件监听器: ✅ (${userManager.eventListeners.size || 0}个)\n`;
                        } else {
                            report += `  事件监听器: ❌ 不存在\n`;
                        }
                        
                    } else {
                        report += '❌ 无法获取用户管理器实例\n';
                    }
                } else {
                    report += '❌ 用户系统适配器不存在或未提供getUserManager方法\n';
                }

                updateResults('user-manager-state', 'info', report);

            } catch (error) {
                updateResults('user-manager-state', 'error', `❌ 状态检查失败: ${error.message}`);
            }
        }

        function testInitMethods() {
            if (!gameFrame) {
                updateResults('user-manager-state', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                const userManager = gameWindow.userSystemAdapter?.getUserManager();
                
                if (!userManager) {
                    updateResults('user-manager-state', 'error', '❌ 无法获取用户管理器');
                    return;
                }

                let report = '🧪 初始化方法测试:\n\n';
                
                // 测试超时保护方法
                if (typeof userManager.initWithTimeout === 'function') {
                    report += '测试 initWithTimeout 方法:\n';
                    try {
                        // 不实际调用，只检查方法签名
                        const methodStr = userManager.initWithTimeout.toString();
                        if (methodStr.includes('Promise.race')) {
                            report += '  ✅ 包含超时保护逻辑\n';
                        } else {
                            report += '  ⚠️ 可能缺少超时保护逻辑\n';
                        }
                        
                        if (methodStr.includes('quickInitialization')) {
                            report += '  ✅ 包含快速初始化降级\n';
                        } else {
                            report += '  ⚠️ 可能缺少降级机制\n';
                        }
                    } catch (error) {
                        report += `  ❌ 方法检查出错: ${error.message}\n`;
                    }
                } else {
                    report += '❌ initWithTimeout 方法不存在\n';
                }
                
                report += '\n当前初始化状态:\n';
                report += `  initialized: ${userManager.initialized}\n`;
                report += `  用户数量: ${userManager.users ? userManager.users.size : 0}\n`;
                report += `  当前用户: ${userManager.currentUser ? userManager.currentUser.displayName : '无'}\n`;

                updateResults('user-manager-state', 'info', report);

            } catch (error) {
                updateResults('user-manager-state', 'error', `❌ 方法测试失败: ${error.message}`);
            }
        }

        function forceReinitialize() {
            if (!gameFrame) {
                updateResults('user-manager-state', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                const userManager = gameWindow.userSystemAdapter?.getUserManager();
                
                if (!userManager) {
                    updateResults('user-manager-state', 'error', '❌ 无法获取用户管理器');
                    return;
                }

                let report = '🔄 强制重新初始化:\n\n';
                
                // 重置初始化状态
                userManager.initialized = false;
                report += '1. 重置 initialized 状态为 false\n';
                
                // 尝试重新初始化
                if (typeof userManager.init === 'function') {
                    report += '2. 调用 init() 方法...\n';
                    
                    userManager.init().then(() => {
                        report += '✅ 重新初始化成功\n';
                        report += `   initialized: ${userManager.initialized}\n`;
                        updateResults('user-manager-state', 'success', report);
                    }).catch((error) => {
                        report += `❌ 重新初始化失败: ${error.message}\n`;
                        updateResults('user-manager-state', 'error', report);
                    });
                    
                    updateResults('user-manager-state', 'info', report + '⏳ 正在重新初始化...');
                } else {
                    report += '❌ init 方法不存在\n';
                    updateResults('user-manager-state', 'error', report);
                }

            } catch (error) {
                updateResults('user-manager-state', 'error', `❌ 强制重新初始化失败: ${error.message}`);
            }
        }

        function verifyTimeoutProtection() {
            updateResults('fix-verification', 'info', 
                '🔍 超时保护机制验证:\n\n' +
                '检查项目:\n' +
                '1. initWithTimeout 方法是否存在\n' +
                '2. Promise.race 超时逻辑是否正确\n' +
                '3. 3秒超时设置是否生效\n' +
                '4. 降级到快速初始化是否工作\n\n' +
                '💡 使用"测试初始化方法"进行详细检查'
            );
        }

        function testLayeredInit() {
            updateResults('fix-verification', 'info', 
                '🏗️ 分层初始化测试:\n\n' +
                '测试层次:\n' +
                '1. performInitialization - 完整初始化\n' +
                '2. quickInitialization - 快速初始化\n' +
                '3. 最小化初始化 - 保底方案\n\n' +
                '💡 使用诊断工具检查各层初始化方法是否存在'
            );
        }

        function simulateInitFailure() {
            updateResults('fix-verification', 'warning', 
                '⚠️ 模拟初始化失败:\n\n' +
                '这个测试将模拟初始化过程中的各种失败情况，\n' +
                '验证我们的降级机制是否正常工作。\n\n' +
                '测试场景:\n' +
                '- 存储服务不可用\n' +
                '- 用户数据加载失败\n' +
                '- 网络超时\n' +
                '- 依赖项缺失'
            );
        }

        function analyzeInitSequence() {
            updateResults('game-diagnostics', 'info', 
                '📊 初始化序列分析:\n\n' +
                '基于提供的日志，发现的问题:\n\n' +
                '1. 时序问题:\n' +
                '   - 用户管理界面等待5秒超时\n' +
                '   - 但随后成功获取用户管理器\n' +
                '   - 创建用户时仍显示未初始化\n\n' +
                '2. 可能原因:\n' +
                '   - init()方法抛出异常但设置了initialized=true\n' +
                '   - 异步初始化过程中的状态不一致\n' +
                '   - 超时保护机制未正确生效\n\n' +
                '💡 建议使用"加载游戏并捕获初始化日志"进行实时分析'
            );
        }

        function debugUserManager() {
            updateResults('game-diagnostics', 'warning', 
                '🐛 用户管理器调试模式:\n\n' +
                '调试重点:\n' +
                '1. 检查 init() 方法是否正确执行\n' +
                '2. 验证 initWithTimeout 是否被调用\n' +
                '3. 分析异常处理逻辑\n' +
                '4. 确认 initialized 状态设置时机\n\n' +
                '💡 使用"检查用户管理器状态"获取详细信息'
            );
        }

        function resetDiagnostics() {
            stopLogCapture();
            
            document.getElementById('game-container').style.display = 'none';
            document.getElementById('log-capture').textContent = '日志捕获区域...';
            
            logCapture = [];
            gameFrame = null;
            
            updateResults('game-diagnostics', 'info', '🔄 诊断已重置');
            updateResults('user-manager-state', 'info', '点击按钮开始检查...');
            updateResults('fix-verification', 'info', '点击按钮开始验证...');
        }

        function updateResults(elementId, type, content) {
            const element = document.getElementById(elementId);
            element.className = `results ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        // 页面加载完成后显示说明
        document.addEventListener('DOMContentLoaded', () => {
            updateResults('game-diagnostics', 'info', 
                '🔍 初始化时序问题诊断工具\n\n' +
                '本工具专门用于诊断用户管理器初始化时序问题。\n\n' +
                '基于日志分析，重点检查:\n' +
                '1. 用户管理器的实际初始化状态\n' +
                '2. 超时保护机制是否生效\n' +
                '3. 修复的方法是否正确加载\n' +
                '4. 异步初始化过程中的异常处理\n\n' +
                '开始诊断：点击"加载游戏并捕获初始化日志"'
            );
        });
    </script>
</body>
</html>
