<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理器初始化诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #16213e;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.danger {
            background: #f44336;
        }
        .test-button.warning {
            background: #ff9800;
        }
        .results {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .results.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .results.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .results.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .results.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: none;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.success {
            background: #4CAF50;
        }
        .status-indicator.error {
            background: #f44336;
        }
        .status-indicator.warning {
            background: #ff9800;
        }
        .status-indicator.info {
            background: #2196F3;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 用户管理器初始化诊断工具</h1>
        
        <div class="section">
            <h2>📋 修复说明</h2>
            <div class="results info">
本次修复针对"用户管理器正在初始化中，请稍后重试"错误：

🔧 修复内容：
1. 用户管理界面增加智能初始化检查机制
2. 实现等待用户管理器和存储服务初始化完成的机制
3. 优化初始化状态检查，避免过于严格的检查
4. 提供更好的用户反馈和错误提示

⏰ 等待策略：
- 用户管理器初始化：最多等待3秒
- 存储服务初始化：最多等待3秒
- 用户界面初始化：最多等待5秒
- 检查间隔：100毫秒

✅ 预期效果：
- 消除"用户管理器正在初始化中"错误
- 用户创建功能在系统就绪后立即可用
- 提供更准确的系统状态反馈
            </div>
        </div>

        <div class="section">
            <h2>🎮 游戏环境测试</h2>
            <div class="test-controls">
                <button class="test-button" onclick="loadGame()">加载游戏</button>
                <button class="test-button" onclick="monitorInitialization()">监控初始化</button>
                <button class="test-button warning" onclick="simulateSlowInit()">模拟慢初始化</button>
                <button class="test-button danger" onclick="clearData()">清除数据</button>
            </div>
            
            <div class="progress-bar" id="init-progress" style="display: none;">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            
            <div class="iframe-container" id="game-container" style="display: none;">
                <iframe id="game-frame" src=""></iframe>
            </div>
            
            <div id="game-status" class="results info">
                点击"加载游戏"开始测试...
            </div>
        </div>

        <div class="section">
            <h2>🔍 初始化状态实时监控</h2>
            <div class="test-controls">
                <button class="test-button" onclick="startRealTimeMonitoring()">开始实时监控</button>
                <button class="test-button" onclick="stopRealTimeMonitoring()">停止监控</button>
                <button class="test-button" onclick="checkCurrentStatus()">检查当前状态</button>
            </div>
            
            <div id="realtime-status" class="results info">
                点击"开始实时监控"开始...
            </div>
        </div>

        <div class="section">
            <h2>👤 用户管理器详细检查</h2>
            <div class="test-controls">
                <button class="test-button" onclick="checkUserManagerDetails()">详细检查用户管理器</button>
                <button class="test-button" onclick="checkInitializationSequence()">检查初始化序列</button>
                <button class="test-button" onclick="testWaitMechanism()">测试等待机制</button>
            </div>
            
            <div id="user-manager-details" class="results info">
                点击按钮开始检查...
            </div>
        </div>

        <div class="section">
            <h2>🧪 用户创建功能测试</h2>
            <div class="form-group">
                <label for="test-identifier">用户标识符:</label>
                <input type="text" id="test-identifier" placeholder="输入测试用户标识符" value="testuser789">
            </div>
            <div class="form-group">
                <label for="test-display-name">显示名称:</label>
                <input type="text" id="test-display-name" placeholder="输入测试显示名称" value="测试用户789">
            </div>
            <div class="test-controls">
                <button class="test-button" onclick="testUserCreationWithWait()">测试用户创建(带等待)</button>
                <button class="test-button" onclick="testUserCreationInGame()">在游戏中测试</button>
                <button class="test-button" onclick="stressTestUserCreation()">压力测试</button>
            </div>
            
            <div id="creation-test-results" class="results info">
                填写用户信息后点击测试按钮...
            </div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let monitoringInterval = null;
        let initializationLog = [];

        function loadGame() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            const progressBar = document.getElementById('init-progress');
            const progressFill = document.getElementById('progress-fill');
            
            updateResults('game-status', 'info', '🔄 正在加载游戏...');
            progressBar.style.display = 'block';
            progressFill.style.width = '10%';
            
            frame.src = './index.html';
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                progressFill.style.width = '50%';
                updateResults('game-status', 'success', '✅ 游戏已加载，等待初始化完成...');
                
                // 监控初始化过程
                let checkCount = 0;
                const checkInterval = setInterval(() => {
                    try {
                        const gameWindow = gameFrame.contentWindow;
                        checkCount++;
                        progressFill.style.width = `${50 + (checkCount * 5)}%`;
                        
                        if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                            const userManager = gameWindow.userSystemAdapter.getUserManager();
                            if (userManager && userManager.initialized) {
                                progressFill.style.width = '100%';
                                updateResults('game-status', 'success', '✅ 游戏和用户管理器初始化完成');
                                clearInterval(checkInterval);
                                setTimeout(() => {
                                    progressBar.style.display = 'none';
                                }, 2000);
                                return;
                            }
                        }
                        
                        if (checkCount > 100) { // 10秒超时
                            progressFill.style.width = '100%';
                            updateResults('game-status', 'warning', '⚠️ 初始化检查超时，但游戏已加载');
                            clearInterval(checkInterval);
                            setTimeout(() => {
                                progressBar.style.display = 'none';
                            }, 2000);
                        }
                    } catch (error) {
                        console.warn('初始化检查错误:', error);
                    }
                }, 100);
            };

            frame.onerror = function(error) {
                progressBar.style.display = 'none';
                updateResults('game-status', 'error', `❌ 游戏加载失败: ${error}`);
            };
        }

        function monitorInitialization() {
            if (!gameFrame) {
                updateResults('game-status', 'error', '❌ 请先加载游戏');
                return;
            }

            updateResults('game-status', 'info', '🔍 开始监控初始化过程...\n请查看浏览器控制台获取详细日志');
            
            // 开始实时监控
            startRealTimeMonitoring();
        }

        function simulateSlowInit() {
            updateResults('game-status', 'warning', 
                '⚠️ 模拟慢初始化测试:\n\n' +
                '这个测试将模拟系统初始化较慢的情况，\n' +
                '用于验证等待机制是否正常工作。\n\n' +
                '💡 在实际使用中，如果遇到初始化慢的问题，\n' +
                '   系统会自动等待初始化完成后再允许用户操作。'
            );
        }

        function startRealTimeMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
            }

            if (!gameFrame) {
                updateResults('realtime-status', 'error', '❌ 请先加载游戏');
                return;
            }

            let report = '🔍 实时监控开始...\n\n';
            updateResults('realtime-status', 'info', report);

            monitoringInterval = setInterval(() => {
                try {
                    const gameWindow = gameFrame.contentWindow;
                    const timestamp = new Date().toLocaleTimeString();
                    
                    let status = `[${timestamp}] 系统状态:\n`;
                    
                    // 检查用户系统适配器
                    if (gameWindow.userSystemAdapter) {
                        status += `  用户系统适配器: ${gameWindow.userSystemAdapter.initialized ? '✅ 已初始化' : '⏳ 初始化中'}\n`;
                        
                        if (gameWindow.userSystemAdapter.initialized) {
                            const userManager = gameWindow.userSystemAdapter.getUserManager();
                            if (userManager) {
                                status += `  用户管理器: ${userManager.initialized ? '✅ 已初始化' : '⏳ 初始化中'}\n`;
                                
                                if (userManager.storageService) {
                                    status += `  存储服务: ${userManager.storageService.initialized ? '✅ 已初始化' : '⏳ 初始化中'}\n`;
                                }
                            } else {
                                status += `  用户管理器: ❌ 未找到\n`;
                            }
                        }
                    } else {
                        status += `  用户系统适配器: ❌ 未找到\n`;
                    }
                    
                    // 检查用户管理界面
                    if (gameWindow.userManagementUI) {
                        status += `  用户管理界面: ${gameWindow.userManagementUI.initialized ? '✅ 已初始化' : '⏳ 初始化中'}\n`;
                    } else {
                        status += `  用户管理界面: ❌ 未找到\n`;
                    }
                    
                    status += '\n';
                    
                    // 保持最近10条记录
                    initializationLog.push(status);
                    if (initializationLog.length > 10) {
                        initializationLog.shift();
                    }
                    
                    updateResults('realtime-status', 'info', initializationLog.join(''));
                    
                } catch (error) {
                    updateResults('realtime-status', 'error', `❌ 监控过程中出现错误: ${error.message}`);
                    clearInterval(monitoringInterval);
                }
            }, 1000);
        }

        function stopRealTimeMonitoring() {
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
                updateResults('realtime-status', 'success', '✅ 实时监控已停止');
            }
        }

        function checkCurrentStatus() {
            if (!gameFrame) {
                updateResults('realtime-status', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '📊 当前系统状态检查:\n\n';
                
                // 详细状态检查
                report += '=== 核心组件状态 ===\n';
                
                if (gameWindow.userSystemAdapter) {
                    report += `用户系统适配器: ✅ 存在\n`;
                    report += `  已初始化: ${gameWindow.userSystemAdapter.initialized ? '✅' : '❌'}\n`;
                    
                    if (gameWindow.userSystemAdapter.initialized) {
                        const userManager = gameWindow.userSystemAdapter.getUserManager();
                        if (userManager) {
                            report += `\n用户管理器: ✅ 存在\n`;
                            report += `  已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                            report += `  存储服务: ${userManager.storageService ? '✅' : '❌'}\n`;
                            
                            if (userManager.storageService) {
                                report += `  存储服务已初始化: ${userManager.storageService.initialized ? '✅' : '❌'}\n`;
                            }
                            
                            report += `  用户数量: ${userManager.users ? userManager.users.size : 0}\n`;
                            report += `  当前用户: ${userManager.currentUser ? userManager.currentUser.displayName : '无'}\n`;
                        } else {
                            report += `\n用户管理器: ❌ 未找到\n`;
                        }
                    }
                } else {
                    report += `用户系统适配器: ❌ 未找到\n`;
                }
                
                if (gameWindow.userManagementUI) {
                    report += `\n用户管理界面: ✅ 存在\n`;
                    report += `  已初始化: ${gameWindow.userManagementUI.initialized ? '✅' : '❌'}\n`;
                } else {
                    report += `\n用户管理界面: ❌ 未找到\n`;
                }
                
                // 判断整体状态
                const allReady = gameWindow.userSystemAdapter && 
                                gameWindow.userSystemAdapter.initialized &&
                                gameWindow.userSystemAdapter.getUserManager() &&
                                gameWindow.userSystemAdapter.getUserManager().initialized &&
                                gameWindow.userSystemAdapter.getUserManager().storageService &&
                                gameWindow.userSystemAdapter.getUserManager().storageService.initialized;
                
                report += `\n=== 整体状态 ===\n`;
                report += `系统就绪状态: ${allReady ? '✅ 完全就绪' : '⚠️ 未完全就绪'}\n`;
                report += `用户创建可用: ${allReady ? '✅ 可用' : '❌ 不可用'}\n`;
                
                updateResults('realtime-status', allReady ? 'success' : 'warning', report);
                
            } catch (error) {
                updateResults('realtime-status', 'error', `❌ 状态检查失败: ${error.message}`);
            }
        }

        function checkUserManagerDetails() {
            if (!gameFrame) {
                updateResults('user-manager-details', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 用户管理器详细检查:\n\n';

                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                    const userManager = gameWindow.userSystemAdapter.getUserManager();
                    
                    if (userManager) {
                        report += '用户管理器详细信息:\n';
                        report += `  构造函数: ${userManager.constructor.name}\n`;
                        report += `  已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                        report += `  存储服务: ${userManager.storageService ? '✅' : '❌'}\n`;
                        
                        if (userManager.storageService) {
                            report += `  存储服务类型: ${userManager.storageService.constructor.name}\n`;
                            report += `  存储服务已初始化: ${userManager.storageService.initialized ? '✅' : '❌'}\n`;
                        }
                        
                        // 检查方法
                        report += '\n方法检查:\n';
                        const methods = ['createUser', 'userExists', 'loadUserCore', 'init'];
                        methods.forEach(method => {
                            const exists = typeof userManager[method] === 'function';
                            report += `  ${method}: ${exists ? '✅' : '❌'}\n`;
                        });
                        
                        // 检查事件监听器
                        report += '\n事件系统:\n';
                        report += `  事件监听器: ${userManager.eventListeners ? '✅' : '❌'}\n`;
                        if (userManager.eventListeners) {
                            report += `  监听器数量: ${userManager.eventListeners.size || 0}\n`;
                        }
                        
                    } else {
                        report += '❌ 用户管理器不存在\n';
                    }
                } else {
                    report += '❌ 用户系统适配器未初始化\n';
                }

                updateResults('user-manager-details', 'info', report);

            } catch (error) {
                updateResults('user-manager-details', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        function checkInitializationSequence() {
            updateResults('user-manager-details', 'info', 
                '🔄 初始化序列检查:\n\n' +
                '正常的初始化序列应该是:\n' +
                '1. 存储服务初始化\n' +
                '2. 用户系统适配器初始化\n' +
                '3. 用户管理器初始化\n' +
                '4. 用户管理界面初始化\n\n' +
                '💡 如果某个步骤失败，后续步骤可能无法正常进行'
            );
        }

        function testWaitMechanism() {
            updateResults('user-manager-details', 'info', 
                '⏰ 等待机制测试:\n\n' +
                '新的等待机制包括:\n' +
                '- 用户管理界面初始化时等待用户管理器\n' +
                '- 用户创建时智能检查并等待初始化完成\n' +
                '- 最多等待3秒，避免无限等待\n' +
                '- 提供详细的等待状态日志\n\n' +
                '💡 这应该解决"用户管理器正在初始化中"的问题'
            );
        }

        async function testUserCreationWithWait() {
            const identifier = document.getElementById('test-identifier').value.trim();
            const displayName = document.getElementById('test-display-name').value.trim();

            if (!identifier || !displayName) {
                updateResults('creation-test-results', 'error', '❌ 请填写用户标识符和显示名称');
                return;
            }

            if (!gameFrame) {
                updateResults('creation-test-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = `🧪 用户创建测试(带等待机制):\n标识符: ${identifier}\n显示名称: ${displayName}\n\n`;

                // 模拟用户管理界面的智能检查
                report += '开始智能初始化检查...\n';
                
                if (!gameWindow.userSystemAdapter) {
                    report += '❌ 用户系统适配器不存在\n';
                    updateResults('creation-test-results', 'error', report);
                    return;
                }

                const userManager = gameWindow.userSystemAdapter.getUserManager();
                if (!userManager) {
                    report += '❌ 用户管理器不存在\n';
                    updateResults('creation-test-results', 'error', report);
                    return;
                }

                // 等待用户管理器初始化
                if (!userManager.initialized) {
                    report += '⏳ 用户管理器正在初始化，等待完成...\n';
                    updateResults('creation-test-results', 'info', report);
                    
                    let retryCount = 0;
                    const maxRetries = 30;
                    
                    while (!userManager.initialized && retryCount < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                        retryCount++;
                        
                        if (retryCount % 10 === 0) {
                            report += `⏳ 等待用户管理器初始化... (${retryCount / 10}秒)\n`;
                            updateResults('creation-test-results', 'info', report);
                        }
                    }
                    
                    if (!userManager.initialized) {
                        report += '❌ 用户管理器初始化超时\n';
                        updateResults('creation-test-results', 'error', report);
                        return;
                    }
                    
                    report += '✅ 用户管理器初始化完成\n';
                }

                // 等待存储服务初始化
                if (!userManager.storageService || !userManager.storageService.initialized) {
                    report += '⏳ 存储服务正在初始化，等待完成...\n';
                    updateResults('creation-test-results', 'info', report);
                    
                    let retryCount = 0;
                    const maxRetries = 30;
                    
                    while ((!userManager.storageService || !userManager.storageService.initialized) && retryCount < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                        retryCount++;
                        
                        if (retryCount % 10 === 0) {
                            report += `⏳ 等待存储服务初始化... (${retryCount / 10}秒)\n`;
                            updateResults('creation-test-results', 'info', report);
                        }
                    }
                    
                    if (!userManager.storageService || !userManager.storageService.initialized) {
                        report += '❌ 存储服务初始化超时\n';
                        updateResults('creation-test-results', 'error', report);
                        return;
                    }
                    
                    report += '✅ 存储服务初始化完成\n';
                }

                report += '\n开始创建用户...\n';
                updateResults('creation-test-results', 'info', report);

                // 执行用户创建
                const newUser = await userManager.createUser(identifier, displayName);
                
                report += `✅ 用户创建成功!\n`;
                report += `用户ID: ${newUser.identifier}\n`;
                report += `显示名称: ${newUser.displayName}\n`;
                report += `用户类型: ${newUser.type}\n`;
                report += `创建时间: ${new Date(newUser.createdAt).toLocaleString()}\n`;

                updateResults('creation-test-results', 'success', report);

            } catch (error) {
                let report = `❌ 用户创建失败:\n`;
                report += `错误信息: ${error.message}\n`;
                if (error.stack) {
                    report += `错误堆栈:\n${error.stack}\n`;
                }
                updateResults('creation-test-results', 'error', report);
            }
        }

        function testUserCreationInGame() {
            if (!gameFrame) {
                updateResults('creation-test-results', 'error', '❌ 请先加载游戏');
                return;
            }

            updateResults('creation-test-results', 'info', 
                '🎮 在游戏中测试用户创建:\n\n' +
                '步骤:\n' +
                '1. 在游戏界面中点击"用户管理"按钮\n' +
                '2. 在弹出的对话框中填写用户信息\n' +
                '3. 点击"创建用户"按钮\n' +
                '4. 观察是否还出现"用户管理器正在初始化中"错误\n\n' +
                '💡 如果修复成功，应该不再出现初始化错误提示\n' +
                '   系统会自动等待初始化完成后允许用户创建'
            );
        }

        function stressTestUserCreation() {
            updateResults('creation-test-results', 'info', 
                '🔥 压力测试说明:\n\n' +
                '压力测试将:\n' +
                '1. 快速连续尝试创建多个用户\n' +
                '2. 测试等待机制在高负载下的表现\n' +
                '3. 验证初始化状态检查的稳定性\n\n' +
                '💡 这有助于发现并发访问时的潜在问题'
            );
        }

        function clearData() {
            if (confirm('确定要清除所有数据吗？这将删除所有用户和游戏数据。')) {
                try {
                    localStorage.clear();
                    if (gameFrame) {
                        gameFrame.contentWindow.localStorage.clear();
                    }
                    updateResults('game-status', 'success', '✅ 数据已清除，请重新加载游戏进行测试');
                } catch (error) {
                    updateResults('game-status', 'error', `❌ 清除数据失败: ${error.message}`);
                }
            }
        }

        function updateResults(elementId, type, content) {
            const element = document.getElementById(elementId);
            element.className = `results ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        // 页面加载完成后显示说明
        document.addEventListener('DOMContentLoaded', () => {
            updateResults('game-status', 'info', 
                '🔍 用户管理器初始化诊断工具\n\n' +
                '本工具专门用于诊断和修复"用户管理器正在初始化中"错误。\n\n' +
                '使用步骤:\n' +
                '1. 点击"加载游戏"加载游戏环境\n' +
                '2. 使用"开始实时监控"观察初始化过程\n' +
                '3. 使用"详细检查用户管理器"验证状态\n' +
                '4. 执行"测试用户创建(带等待)"验证修复效果\n\n' +
                '如果修复成功，应该不再出现初始化错误。'
            );
        });
    </script>
</body>
</html>
