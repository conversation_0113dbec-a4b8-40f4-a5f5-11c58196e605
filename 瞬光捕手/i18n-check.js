/**
 * 瞬光捕手 - 国际化检查脚本
 * 自动检查HTML中的data-i18n属性与i18n配置文件的匹配情况
 */

// 从i18n.js文件中提取的翻译键（需要手动更新）
const zhTranslations = {
    // 游戏基本信息
    'game.title': '瞬光捕手',
    'game.subtitle': '捕捉决定性瞬间，引燃无限可能',
    'loading.text': '正在加载...',
    
    // 主菜单
    'menu.start': '开始游戏',
    'menu.levelEditor': '关卡编辑器',
    'menu.customLevels': '自定义关卡',
    'menu.leaderboard': '排行榜',
    'menu.settings': '设置',
    
    // 游戏界面
    'game.score': '得分',
    'game.level': '关卡',
    'game.lives': '生命',
    'game.pause': '暂停',
    'game.hint.ready': '准备好了吗？',
    'game.hint.start': '点击开始！',
    'game.hint.perfect': '完美时机！',
    'game.hint.good': '不错！',
    'game.hint.miss': '错过了！',
    'game.hint.gameOver': '游戏结束',
    
    // 暂停菜单
    'pause.title': '游戏暂停',
    'pause.resume': '继续游戏',
    'pause.restart': '重新开始',
    'pause.mainMenu': '主菜单',
    
    // 游戏结束
    'gameOver.title': '游戏结束',
    'gameOver.finalScore': '最终得分',
    'gameOver.newRecord': '新纪录！',
    'gameOver.playAgain': '再玩一次',
    'gameOver.backToMenu': '返回主菜单',
    
    // 设置
    'settings.title': '设置',
    'settings.language': '语言',
    'settings.sound': '音效',
    'settings.music': '音乐',
    'settings.difficulty': '游戏难度',
    'settings.save': '保存设置',
    'settings.cancel': '取消',
    
    // 玩家管理
    'player.current': '当前玩家',
    'player.switch': '切换',
    'player.management': '玩家管理',
    'player.existing': '现有玩家',
    'player.createNew': '创建新玩家',
    'player.namePlaceholder': '输入玩家名称',
    'player.create': '创建',
    'player.select': '选择',
    'player.delete': '删除',
    'player.guest': '游客',
    
    // 用户管理 (新增)
    'user.current': '当前用户',
    'user.manage': '用户管理',
    'user.switch': '切换用户',
    'user.create': '创建用户',
    'user.delete': '删除用户',
    'user.edit': '编辑用户',
    'user.guest': '游客',
    
    // 游戏界面 (新增)
    'game.backToMenu': '返回主菜单',
    
    // 排行榜
    'leaderboard.title': '排行榜',
    'leaderboard.global': '全球排行',
    'leaderboard.daily': '今日排行',
    'leaderboard.weekly': '本周排行',
    'leaderboard.perfectHits': '完美击中',
    'leaderboard.rank': '排名',
    'leaderboard.player': '玩家',
    'leaderboard.score': '分数',
    'leaderboard.time': '时间',
    'leaderboard.refresh': '刷新',
    
    // 自定义关卡
    'customLevels.title': '自定义关卡',
    'customLevels.subtitle': '探索玩家创造的精彩关卡',
    'customLevels.filter': '过滤',
    'customLevels.filter.published': '已发布',
    'customLevels.filter.my': '我的关卡',
    'customLevels.filter.all': '全部',
    'customLevels.sort': '排序',
    'customLevels.sort.rating': '评分',
    'customLevels.sort.playCount': '游玩次数',
    'customLevels.sort.date': '创建时间',
    'customLevels.sort.difficulty': '难度',
    'customLevels.search': '搜索关卡...',
    'customLevels.searchBtn': '搜索',
    'customLevels.totalLevels': '总关卡数',
    'customLevels.loading': '加载关卡中...',
    'customLevels.empty.title': '暂无关卡',
    'customLevels.empty.message': '还没有找到符合条件的关卡',
    'customLevels.createFirst': '创建第一个关卡',
    'customLevels.author': '作者',
    'customLevels.difficulty': '难度',
    'customLevels.playCount': '游玩次数',
    'customLevels.created': '创建时间',
    'customLevels.description': '关卡描述',
    'customLevels.rating': '评分',
    'customLevels.like': '点赞',
    'customLevels.dislike': '踩',
    'customLevels.preview': '关卡预览',
    'customLevels.play': '开始游戏',
    'customLevels.edit': '编辑关卡',
    'customLevels.delete': '删除关卡',
    
    // 关卡编辑器
    'editor.tools': '工具',
    'editor.select': '选择',
    'editor.spark': '光点',
    'editor.obstacle': '障碍',
    'editor.powerup': '道具',
    'editor.trigger': '触发',
    'editor.eraser': '橡皮',
    'editor.actions': '操作',
    'editor.new': '新建',
    'editor.load': '加载',
    'editor.save': '保存',
    'editor.test': '测试',
    'editor.publish': '发布',
    'editor.view': '视图',
    'editor.showGrid': '显示网格',
    'editor.snapToGrid': '对齐网格',
    'editor.levelSettings': '关卡设置',
    'editor.levelName': '关卡名称',
    'editor.levelDescription': '关卡描述',
    'editor.difficulty': '难度',
    'editor.difficulty.easy': '简单',
    'editor.difficulty.normal': '普通',
    'editor.difficulty.hard': '困难',
    'editor.difficulty.expert': '专家',
    'editor.timeLimit': '时间限制(秒)',
    'editor.targetScore': '目标分数',
    'editor.objectProperties': '对象属性',
    'editor.selectTool': '请选择工具',
    'editor.statistics': '统计信息',
    'editor.totalObjects': '总对象数',
    'editor.sparkCount': '光点数',
    'editor.obstacleCount': '障碍数',
    'editor.powerupCount': '道具数',
    'editor.loadLevel': '加载关卡',
    'editor.loadSelected': '加载选中',
    
    // 难度设置
    'difficulty.beginner': '新手',
    'difficulty.easy': '简单',
    'difficulty.normal': '普通',
    'difficulty.hard': '困难',
    'difficulty.expert': '专家',
    'difficulty.master': '大师',
    'difficulty.normal.desc': '标准的游戏难度',
    
    // 通用
    'common.ok': '确定',
    'common.cancel': '取消',
    'common.close': '关闭',
    'common.back': '返回',
    'common.next': '下一步',
    'common.previous': '上一步',
    'common.loading': '加载中...',
    'common.error': '错误',
    'common.success': '成功',
    'common.warning': '警告',
    'common.confirm': '确认'
};

const enTranslations = {
    // Game basic info
    'game.title': 'Split-Second Spark',
    'game.subtitle': 'Capture decisive moments, ignite infinite possibilities',
    'loading.text': 'Loading...',
    
    // Main menu
    'menu.start': 'Start Game',
    'menu.levelEditor': 'Level Editor',
    'menu.customLevels': 'Custom Levels',
    'menu.leaderboard': 'Leaderboard',
    'menu.settings': 'Settings',
    
    // Game interface
    'game.score': 'Score',
    'game.level': 'Level',
    'game.lives': 'Lives',
    'game.pause': 'Pause',
    'game.hint.ready': 'Ready?',
    'game.hint.start': 'Click to start!',
    'game.hint.perfect': 'Perfect timing!',
    'game.hint.good': 'Good!',
    'game.hint.miss': 'Missed!',
    'game.hint.gameOver': 'Game Over',
    
    // Pause menu
    'pause.title': 'Game Paused',
    'pause.resume': 'Resume',
    'pause.restart': 'Restart',
    'pause.mainMenu': 'Main Menu',
    
    // Game over
    'gameOver.title': 'Game Over',
    'gameOver.finalScore': 'Final Score',
    'gameOver.newRecord': 'New Record!',
    'gameOver.playAgain': 'Play Again',
    'gameOver.backToMenu': 'Back to Menu',
    
    // Settings
    'settings.title': 'Settings',
    'settings.language': 'Language',
    'settings.sound': 'Sound',
    'settings.music': 'Music',
    'settings.difficulty': 'Game Difficulty',
    'settings.save': 'Save Settings',
    'settings.cancel': 'Cancel',
    
    // Player management
    'player.current': 'Current Player',
    'player.switch': 'Switch',
    'player.management': 'Player Management',
    'player.existing': 'Existing Players',
    'player.createNew': 'Create New Player',
    'player.namePlaceholder': 'Enter player name',
    'player.create': 'Create',
    'player.select': 'Select',
    'player.delete': 'Delete',
    'player.guest': 'Guest',
    
    // User management (新增)
    'user.current': 'Current User',
    'user.manage': 'User Management',
    'user.switch': 'Switch User',
    'user.create': 'Create User',
    'user.delete': 'Delete User',
    'user.edit': 'Edit User',
    'user.guest': 'Guest',
    
    // Game interface (新增)
    'game.backToMenu': 'Back to Menu',
    
    // 其他翻译键...（为了节省空间，这里省略了部分内容）
};

// 从HTML中提取的data-i18n属性
const htmlI18nKeys = [
    'game.title', 'loading.text', 'game.subtitle', 'menu.start', 'menu.levelEditor',
    'menu.customLevels', 'menu.leaderboard', 'menu.settings', 'user.current', 'user.manage',
    'game.backToMenu', 'game.score', 'game.level', 'game.lives', 'game.pause',
    'game.hint.ready', 'pause.title', 'pause.resume', 'pause.restart', 'pause.mainMenu',
    'gameOver.title', 'gameOver.finalScore', 'gameOver.playAgain', 'gameOver.backToMenu',
    'leaderboard.title', 'leaderboard.global', 'leaderboard.daily', 'leaderboard.weekly',
    'leaderboard.perfectHits', 'leaderboard.rank', 'leaderboard.player', 'leaderboard.score',
    'leaderboard.time', 'leaderboard.refresh', 'common.back', 'customLevels.title',
    'customLevels.subtitle', 'customLevels.filter', 'customLevels.filter.published',
    'customLevels.filter.my', 'customLevels.filter.all', 'customLevels.sort',
    'customLevels.sort.rating', 'customLevels.sort.playCount', 'customLevels.sort.date',
    'customLevels.sort.difficulty', 'customLevels.totalLevels', 'customLevels.loading',
    'customLevels.empty.title', 'customLevels.empty.message', 'customLevels.createFirst',
    'customLevels.author', 'customLevels.difficulty', 'customLevels.playCount',
    'customLevels.created', 'customLevels.description', 'customLevels.rating',
    'customLevels.like', 'customLevels.dislike', 'customLevels.preview',
    'customLevels.play', 'customLevels.edit', 'customLevels.delete', 'editor.tools',
    'editor.select', 'editor.spark', 'editor.obstacle', 'editor.powerup', 'editor.trigger',
    'editor.eraser', 'editor.actions', 'editor.new', 'editor.load', 'editor.save',
    'editor.test', 'editor.publish', 'editor.view', 'editor.showGrid', 'editor.snapToGrid',
    'editor.levelSettings', 'editor.levelName', 'editor.levelDescription', 'editor.difficulty',
    'editor.difficulty.easy', 'editor.difficulty.normal', 'editor.difficulty.hard',
    'editor.difficulty.expert', 'editor.timeLimit', 'editor.targetScore',
    'editor.objectProperties', 'editor.selectTool', 'editor.statistics', 'editor.totalObjects',
    'editor.sparkCount', 'editor.obstacleCount', 'editor.powerupCount', 'editor.loadLevel',
    'editor.loadSelected', 'common.cancel', 'settings.title', 'settings.language',
    'settings.difficulty', 'difficulty.beginner', 'difficulty.easy', 'difficulty.normal',
    'difficulty.hard', 'difficulty.expert', 'difficulty.master', 'difficulty.normal.desc',
    'settings.sound', 'settings.music', 'settings.save', 'settings.cancel'
];

// 检查函数
function checkI18nCompleteness() {
    console.log('🌐 开始国际化完整性检查...\n');
    
    const results = {
        missingInZh: [],
        missingInEn: [],
        missingInHtml: [],
        newKeysAdded: [],
        totalKeys: htmlI18nKeys.length
    };
    
    // 检查HTML中的键是否在翻译文件中存在
    htmlI18nKeys.forEach(key => {
        if (!zhTranslations[key]) {
            results.missingInZh.push(key);
        }
        if (!enTranslations[key]) {
            results.missingInEn.push(key);
        }
    });
    
    // 检查新增的用户管理相关键
    const newUserKeys = [
        'user.current', 'user.manage', 'user.switch', 'user.create', 
        'user.delete', 'user.edit', 'user.guest', 'game.backToMenu'
    ];
    
    newUserKeys.forEach(key => {
        if (zhTranslations[key] && enTranslations[key]) {
            results.newKeysAdded.push({
                key: key,
                zh: zhTranslations[key],
                en: enTranslations[key]
            });
        }
    });
    
    return results;
}

// 运行检查
const checkResults = checkI18nCompleteness();

console.log('📊 国际化检查结果:');
console.log('==================');
console.log(`总计检查键数: ${checkResults.totalKeys}`);
console.log(`中文缺失键数: ${checkResults.missingInZh.length}`);
console.log(`英文缺失键数: ${checkResults.missingInEn.length}`);
console.log(`新增键数: ${checkResults.newKeysAdded.length}`);

if (checkResults.missingInZh.length > 0) {
    console.log('\n❌ 中文翻译缺失的键:');
    checkResults.missingInZh.forEach(key => console.log(`  - ${key}`));
}

if (checkResults.missingInEn.length > 0) {
    console.log('\n❌ 英文翻译缺失的键:');
    checkResults.missingInEn.forEach(key => console.log(`  - ${key}`));
}

if (checkResults.newKeysAdded.length > 0) {
    console.log('\n✅ 新增的翻译键:');
    checkResults.newKeysAdded.forEach(item => {
        console.log(`  - ${item.key}:`);
        console.log(`    中文: ${item.zh}`);
        console.log(`    英文: ${item.en}`);
    });
}

console.log('\n🎯 检查完成!');
