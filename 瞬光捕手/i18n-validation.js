/**
 * 瞬光捕手 - i18n验证脚本
 * 验证新增翻译键的完整性
 */

const fs = require('fs');
const path = require('path');

// 读取i18n.js文件
const i18nPath = path.join(__dirname, 'js/utils/i18n.js');
const i18nContent = fs.readFileSync(i18nPath, 'utf8');

// 提取翻译数据
function extractTranslations(content) {
    const zhMatch = content.match(/'zh-CN':\s*{([\s\S]*?)},\s*'en-US'/);
    const enMatch = content.match(/'en-US':\s*{([\s\S]*?)}\s*};/);
    
    if (!zhMatch || !enMatch) {
        throw new Error('无法解析翻译数据');
    }
    
    const zhContent = zhMatch[1];
    const enContent = enMatch[1];
    
    // 简单的键值对提取
    const extractKeys = (content) => {
        const keys = {};
        const lines = content.split('\n');
        
        for (const line of lines) {
            const match = line.match(/'([^']+)':\s*'([^']+)'/);
            if (match) {
                keys[match[1]] = match[2];
            }
        }
        
        return keys;
    };
    
    return {
        zh: extractKeys(zhContent),
        en: extractKeys(enContent)
    };
}

console.log('🌐 瞬光捕手 i18n 验证报告');
console.log('='.repeat(50));

try {
    const translations = extractTranslations(i18nContent);
    
    // 新增的翻译键
    const newKeys = [
        'user.current',
        'user.manage',
        'user.switch', 
        'user.create',
        'user.delete',
        'user.edit',
        'user.guest',
        'game.backToMenu'
    ];
    
    console.log('\n📋 新增翻译键验证:');
    console.log('-'.repeat(30));
    
    let completeCount = 0;
    let missingZh = [];
    let missingEn = [];
    
    newKeys.forEach(key => {
        const hasZh = translations.zh[key];
        const hasEn = translations.en[key];
        
        console.log(`\n🔍 ${key}:`);
        
        if (hasZh) {
            console.log(`  ✅ 中文: "${translations.zh[key]}"`);
        } else {
            console.log(`  ❌ 中文: 缺失`);
            missingZh.push(key);
        }
        
        if (hasEn) {
            console.log(`  ✅ 英文: "${translations.en[key]}"`);
        } else {
            console.log(`  ❌ 英文: 缺失`);
            missingEn.push(key);
        }
        
        if (hasZh && hasEn) {
            completeCount++;
            console.log(`  ✅ 状态: 完整`);
        } else {
            console.log(`  ⚠️ 状态: 不完整`);
        }
    });
    
    // 统计结果
    console.log('\n📊 验证统计:');
    console.log('-'.repeat(30));
    console.log(`总计新增键: ${newKeys.length}`);
    console.log(`完整翻译: ${completeCount}`);
    console.log(`中文缺失: ${missingZh.length}`);
    console.log(`英文缺失: ${missingEn.length}`);
    console.log(`完成率: ${Math.round((completeCount / newKeys.length) * 100)}%`);
    
    // 详细问题报告
    if (missingZh.length > 0) {
        console.log('\n❌ 中文翻译缺失的键:');
        missingZh.forEach(key => console.log(`  - ${key}`));
    }
    
    if (missingEn.length > 0) {
        console.log('\n❌ 英文翻译缺失的键:');
        missingEn.forEach(key => console.log(`  - ${key}`));
    }
    
    // 最终评估
    console.log('\n🎯 最终评估:');
    console.log('-'.repeat(30));
    
    if (completeCount === newKeys.length) {
        console.log('🎉 优秀！所有新增翻译键都已完整添加！');
        console.log('✅ 中英文翻译完整');
        console.log('✅ 支持语言切换');
        console.log('✅ 国际化标准符合要求');
    } else {
        console.log('⚠️ 需要改进:');
        if (missingZh.length > 0) {
            console.log(`- 补充 ${missingZh.length} 个中文翻译`);
        }
        if (missingEn.length > 0) {
            console.log(`- 补充 ${missingEn.length} 个英文翻译`);
        }
    }
    
    // 检查HTML使用情况
    console.log('\n🔍 HTML使用情况检查:');
    console.log('-'.repeat(30));
    
    const htmlPath = path.join(__dirname, 'index.html');
    const htmlContent = fs.readFileSync(htmlPath, 'utf8');
    
    const usedKeys = [];
    const unusedKeys = [];
    
    newKeys.forEach(key => {
        if (htmlContent.includes(`data-i18n="${key}"`)) {
            usedKeys.push(key);
        } else {
            unusedKeys.push(key);
        }
    });
    
    console.log(`HTML中已使用: ${usedKeys.length}/${newKeys.length}`);
    
    if (usedKeys.length > 0) {
        console.log('\n✅ 已在HTML中使用的键:');
        usedKeys.forEach(key => console.log(`  - ${key}`));
    }
    
    if (unusedKeys.length > 0) {
        console.log('\n⚠️ 未在HTML中使用的键:');
        unusedKeys.forEach(key => console.log(`  - ${key}`));
    }
    
    // 生成建议
    console.log('\n💡 改进建议:');
    console.log('-'.repeat(30));
    
    if (completeCount === newKeys.length && usedKeys.length === newKeys.length) {
        console.log('✅ 国际化实现完美，无需改进');
    } else {
        if (missingZh.length > 0 || missingEn.length > 0) {
            console.log('1. 补充缺失的翻译键');
        }
        if (unusedKeys.length > 0) {
            console.log('2. 在HTML中使用所有定义的翻译键');
        }
        console.log('3. 建立自动化的i18n检查流程');
        console.log('4. 考虑添加更多语言支持');
    }
    
} catch (error) {
    console.error('❌ 验证失败:', error.message);
    process.exit(1);
}

console.log('\n🔚 验证完成');
