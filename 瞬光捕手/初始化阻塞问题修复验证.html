<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>初始化阻塞问题修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #16213e;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.danger {
            background: #f44336;
        }
        .test-button.warning {
            background: #ff9800;
        }
        .test-button.secondary {
            background: #2196F3;
        }
        .results {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .results.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .results.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .results.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .results.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: none;
        }
        .timer {
            font-size: 18px;
            font-weight: bold;
            color: #4CAF50;
            text-align: center;
            margin: 10px 0;
        }
        .timer.warning {
            color: #ff9800;
        }
        .timer.danger {
            color: #f44336;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #45a049);
            width: 0%;
            transition: width 0.1s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 初始化阻塞问题修复验证</h1>
        
        <div class="section">
            <h2>📋 修复说明</h2>
            <div class="results info">
本次修复针对用户管理器初始化卡住的问题：

🔍 问题根因：
1. 用户管理器初始化过程中同步加载所有用户数据
2. 缺乏超时保护机制，可能无限等待
3. 存储服务响应慢时累积延迟导致初始化卡住

🔧 修复方案：
1. 添加超时保护机制（3秒超时）
2. 实现分层初始化策略：
   - 完整初始化：正常情况下的完整加载
   - 快速初始化：超时时的降级方案
   - 最小化初始化：极端情况下的保底方案
3. 优化用户列表加载：批量加载，限制并发数

⏰ 初始化策略：
- 完整初始化：3秒内完成所有用户数据加载
- 快速初始化：只加载用户标识符和当前用户
- 最小化初始化：创建临时游客用户，确保系统可用

✅ 预期效果：
- 用户管理器在1-2秒内完成初始化
- 即使存储服务慢也不会卡住
- 提供多层次的降级保护
- 用户创建功能快速可用
            </div>
        </div>

        <div class="section">
            <h2>🎮 游戏环境测试</h2>
            <div class="test-controls">
                <button class="test-button" onclick="loadGameAndMonitor()">加载游戏并监控初始化</button>
                <button class="test-button secondary" onclick="loadGameNormal()">加载正常游戏</button>
                <button class="test-button danger" onclick="resetTest()">重置测试</button>
            </div>
            
            <div class="timer" id="init-timer" style="display: none;">初始化时间: 0.0秒</div>
            <div class="progress-bar" id="init-progress" style="display: none;">
                <div class="progress-fill" id="progress-fill"></div>
            </div>
            
            <div class="iframe-container" id="game-container" style="display: none;">
                <iframe id="game-frame" src=""></iframe>
            </div>
            
            <div id="game-test-results" class="results info">
                点击"加载游戏并监控初始化"开始测试...
            </div>
        </div>

        <div class="section">
            <h2>⏱️ 初始化性能测试</h2>
            <div class="test-controls">
                <button class="test-button" onclick="performanceTest()">执行性能测试</button>
                <button class="test-button secondary" onclick="stressTest()">压力测试</button>
                <button class="test-button warning" onclick="slowStorageTest()">慢存储测试</button>
            </div>
            
            <div id="performance-results" class="results info">
                点击按钮开始性能测试...
            </div>
        </div>

        <div class="section">
            <h2>🔍 初始化过程详细分析</h2>
            <div class="test-controls">
                <button class="test-button" onclick="analyzeInitProcess()">分析初始化过程</button>
                <button class="test-button secondary" onclick="checkInitMethods()">检查初始化方法</button>
                <button class="test-button secondary" onclick="monitorRealTime()">实时监控</button>
            </div>
            
            <div id="analysis-results" class="results info">
                点击按钮开始分析...
            </div>
        </div>

        <div class="section">
            <h2>👤 用户创建功能验证</h2>
            <div class="form-group">
                <label for="test-identifier">用户标识符:</label>
                <input type="text" id="test-identifier" placeholder="输入测试用户标识符" value="fix_test_user">
            </div>
            <div class="form-group">
                <label for="test-display-name">显示名称:</label>
                <input type="text" id="test-display-name" placeholder="输入测试显示名称" value="修复测试用户">
            </div>
            <div class="test-controls">
                <button class="test-button" onclick="testUserCreationAfterFix()">测试用户创建(修复后)</button>
                <button class="test-button secondary" onclick="testUserCreationInGame()">在游戏中测试</button>
                <button class="test-button warning" onclick="testUserCreationStress()">压力测试用户创建</button>
            </div>
            
            <div id="user-creation-results" class="results info">
                填写用户信息后点击测试按钮...
            </div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let initStartTime = null;
        let initTimer = null;
        let monitoringInterval = null;

        function loadGameAndMonitor() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            const timer = document.getElementById('init-timer');
            const progress = document.getElementById('init-progress');
            const progressFill = document.getElementById('progress-fill');
            
            updateResults('game-test-results', 'info', '🔄 正在加载游戏并监控初始化过程...');
            
            // 显示计时器和进度条
            timer.style.display = 'block';
            progress.style.display = 'block';
            
            // 开始计时
            initStartTime = Date.now();
            startInitTimer();
            
            frame.src = './index.html';
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                updateResults('game-test-results', 'success', '✅ 游戏页面已加载，开始监控初始化...');
                
                // 开始详细监控初始化过程
                monitorInitializationDetailed();
            };

            frame.onerror = function(error) {
                stopInitTimer();
                updateResults('game-test-results', 'error', `❌ 游戏加载失败: ${error}`);
            };
        }

        function startInitTimer() {
            initTimer = setInterval(() => {
                if (initStartTime) {
                    const elapsed = (Date.now() - initStartTime) / 1000;
                    const timerElement = document.getElementById('init-timer');
                    const progressFill = document.getElementById('progress-fill');
                    
                    timerElement.textContent = `初始化时间: ${elapsed.toFixed(1)}秒`;
                    
                    // 根据时间改变颜色
                    if (elapsed < 2) {
                        timerElement.className = 'timer';
                        progressFill.style.background = 'linear-gradient(90deg, #4CAF50, #45a049)';
                    } else if (elapsed < 3) {
                        timerElement.className = 'timer warning';
                        progressFill.style.background = 'linear-gradient(90deg, #ff9800, #f57c00)';
                    } else {
                        timerElement.className = 'timer danger';
                        progressFill.style.background = 'linear-gradient(90deg, #f44336, #d32f2f)';
                    }
                    
                    // 更新进度条（假设5秒为100%）
                    const progress = Math.min((elapsed / 5) * 100, 100);
                    progressFill.style.width = `${progress}%`;
                }
            }, 100);
        }

        function stopInitTimer() {
            if (initTimer) {
                clearInterval(initTimer);
                initTimer = null;
            }
            if (monitoringInterval) {
                clearInterval(monitoringInterval);
                monitoringInterval = null;
            }
        }

        function monitorInitializationDetailed() {
            if (!gameFrame) return;
            
            let report = '🔍 详细初始化监控:\n\n';
            let checkCount = 0;
            const maxChecks = 100; // 10秒最大监控时间
            
            updateResults('game-test-results', 'info', report);

            monitoringInterval = setInterval(() => {
                try {
                    const gameWindow = gameFrame.contentWindow;
                    const elapsed = initStartTime ? (Date.now() - initStartTime) / 1000 : 0;
                    checkCount++;
                    
                    let status = `[${elapsed.toFixed(1)}s] 检查 #${checkCount}:\n`;
                    
                    // 检查存储服务
                    if (gameWindow.storageService) {
                        status += `  存储服务: ${gameWindow.storageService.initialized ? '✅ 已初始化' : '⏳ 初始化中'}\n`;
                    } else {
                        status += `  存储服务: ❌ 未找到\n`;
                    }
                    
                    // 检查用户系统适配器
                    if (gameWindow.userSystemAdapter) {
                        status += `  用户系统适配器: ${gameWindow.userSystemAdapter.initialized ? '✅ 已初始化' : '⏳ 初始化中'}\n`;
                        
                        if (gameWindow.userSystemAdapter.initialized) {
                            const userManager = gameWindow.userSystemAdapter.getUserManager();
                            if (userManager) {
                                status += `  用户管理器: ${userManager.initialized ? '✅ 已初始化' : '⏳ 初始化中'}\n`;
                                
                                if (userManager.initialized) {
                                    // 初始化完成！
                                    const totalTime = elapsed.toFixed(1);
                                    status += `\n🎉 初始化完成！总耗时: ${totalTime}秒\n`;
                                    
                                    // 检查初始化结果
                                    status += `\n初始化结果:\n`;
                                    status += `  用户数量: ${userManager.users ? userManager.users.size : 0}\n`;
                                    status += `  当前用户: ${userManager.currentUser ? userManager.currentUser.displayName : '无'}\n`;
                                    status += `  存储类型: ${userManager.storageService ? userManager.storageService.storageType : '未知'}\n`;
                                    
                                    report += status;
                                    updateResults('game-test-results', 
                                        totalTime < 2 ? 'success' : (totalTime < 3 ? 'warning' : 'error'), 
                                        report
                                    );
                                    
                                    stopInitTimer();
                                    return;
                                }
                            } else {
                                status += `  用户管理器: ❌ 未找到\n`;
                            }
                        }
                    } else {
                        status += `  用户系统适配器: ❌ 未找到\n`;
                    }
                    
                    status += '\n';
                    report += status;
                    
                    // 保持最近20条记录
                    const lines = report.split('\n');
                    if (lines.length > 100) {
                        report = '🔍 详细初始化监控:\n\n' + lines.slice(-80).join('\n');
                    }
                    
                    updateResults('game-test-results', 'info', report);
                    
                    // 超时检查
                    if (checkCount >= maxChecks) {
                        status = `❌ 监控超时 (${elapsed.toFixed(1)}秒)\n`;
                        status += `初始化可能卡住或失败\n`;
                        report += status;
                        updateResults('game-test-results', 'error', report);
                        stopInitTimer();
                    }
                    
                } catch (error) {
                    console.error('监控过程中出现错误:', error);
                    updateResults('game-test-results', 'error', `❌ 监控过程中出现错误: ${error.message}`);
                    stopInitTimer();
                }
            }, 100);
        }

        function loadGameNormal() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            
            updateResults('game-test-results', 'info', '🔄 正在加载正常游戏...');
            
            frame.src = './index.html';
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                updateResults('game-test-results', 'success', '✅ 正常游戏已加载');
            };
        }

        function performanceTest() {
            updateResults('performance-results', 'info', 
                '⏱️ 初始化性能测试:\n\n' +
                '测试项目:\n' +
                '1. 初始化时间测量\n' +
                '2. 内存使用情况\n' +
                '3. 存储服务调用次数\n' +
                '4. 用户数据加载效率\n\n' +
                '💡 使用"加载游戏并监控初始化"进行实际测试'
            );
        }

        function stressTest() {
            updateResults('performance-results', 'warning', 
                '🔥 压力测试说明:\n\n' +
                '压力测试将模拟:\n' +
                '1. 大量用户数据的加载\n' +
                '2. 存储服务高延迟情况\n' +
                '3. 并发初始化请求\n' +
                '4. 内存和CPU压力\n\n' +
                '⚠️ 这可能会影响浏览器性能'
            );
        }

        function slowStorageTest() {
            updateResults('performance-results', 'warning', 
                '🐌 慢存储测试:\n\n' +
                '此测试将模拟存储服务响应缓慢的情况，\n' +
                '验证超时保护和降级机制是否正常工作。\n\n' +
                '预期结果:\n' +
                '- 3秒后触发快速初始化模式\n' +
                '- 系统仍然可用\n' +
                '- 用户创建功能正常'
            );
        }

        function analyzeInitProcess() {
            if (!gameFrame) {
                updateResults('analysis-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 初始化过程分析:\n\n';

                // 检查初始化方法
                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                    const userManager = gameWindow.userSystemAdapter.getUserManager();
                    
                    if (userManager) {
                        report += '用户管理器方法检查:\n';
                        const methods = [
                            'init', 'initWithTimeout', 'performInitialization', 
                            'quickInitialization', 'loadUserListOptimized'
                        ];
                        
                        methods.forEach(method => {
                            const exists = typeof userManager[method] === 'function';
                            report += `  ${method}: ${exists ? '✅' : '❌'}\n`;
                        });
                        
                        report += '\n初始化状态:\n';
                        report += `  已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                        report += `  用户数量: ${userManager.users ? userManager.users.size : 0}\n`;
                        report += `  当前用户: ${userManager.currentUser ? userManager.currentUser.displayName : '无'}\n`;
                        
                        if (userManager.storageService) {
                            report += `  存储服务: ✅\n`;
                            report += `  存储服务已初始化: ${userManager.storageService.initialized ? '✅' : '❌'}\n`;
                        } else {
                            report += `  存储服务: ❌\n`;
                        }
                    } else {
                        report += '❌ 用户管理器不存在\n';
                    }
                } else {
                    report += '❌ 用户系统适配器未初始化\n';
                }

                updateResults('analysis-results', 'info', report);

            } catch (error) {
                updateResults('analysis-results', 'error', `❌ 分析失败: ${error.message}`);
            }
        }

        function checkInitMethods() {
            updateResults('analysis-results', 'info', 
                '🔧 初始化方法检查:\n\n' +
                '修复后的初始化方法:\n' +
                '1. init() - 主初始化方法\n' +
                '2. initWithTimeout() - 带超时保护的初始化\n' +
                '3. performInitialization() - 完整初始化过程\n' +
                '4. quickInitialization() - 快速初始化模式\n' +
                '5. loadUserListOptimized() - 优化的用户列表加载\n\n' +
                '💡 使用"分析初始化过程"检查这些方法是否存在'
            );
        }

        function monitorRealTime() {
            updateResults('analysis-results', 'info', 
                '👁️ 实时监控说明:\n\n' +
                '实时监控功能已集成在"加载游戏并监控初始化"中。\n\n' +
                '监控内容:\n' +
                '- 初始化时间计算\n' +
                '- 各组件状态变化\n' +
                '- 性能指标收集\n' +
                '- 错误和警告捕获\n\n' +
                '💡 点击"加载游戏并监控初始化"开始实时监控'
            );
        }

        async function testUserCreationAfterFix() {
            const identifier = document.getElementById('test-identifier').value.trim();
            const displayName = document.getElementById('test-display-name').value.trim();

            if (!identifier || !displayName) {
                updateResults('user-creation-results', 'error', '❌ 请填写用户标识符和显示名称');
                return;
            }

            if (!gameFrame) {
                updateResults('user-creation-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = `🧪 用户创建测试(修复后):\n标识符: ${identifier}\n显示名称: ${displayName}\n\n`;

                // 检查系统状态
                if (!gameWindow.userSystemAdapter || !gameWindow.userSystemAdapter.initialized) {
                    report += '❌ 用户系统适配器未初始化\n';
                    updateResults('user-creation-results', 'error', report);
                    return;
                }

                const userManager = gameWindow.userSystemAdapter.getUserManager();
                if (!userManager) {
                    report += '❌ 用户管理器不存在\n';
                    updateResults('user-creation-results', 'error', report);
                    return;
                }

                report += '前置条件检查:\n';
                report += `  用户管理器: ✅\n`;
                report += `  已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                report += `  存储服务: ${userManager.storageService ? '✅' : '❌'}\n`;
                
                if (userManager.storageService) {
                    report += `  存储服务已初始化: ${userManager.storageService.initialized ? '✅' : '❌'}\n`;
                }

                if (!userManager.initialized) {
                    report += '\n❌ 用户管理器未初始化，这表明修复可能未生效\n';
                    updateResults('user-creation-results', 'error', report);
                    return;
                }

                report += '\n开始创建用户...\n';
                updateResults('user-creation-results', 'info', report);

                const startTime = Date.now();
                const newUser = await userManager.createUser(identifier, displayName);
                const createTime = (Date.now() - startTime) / 1000;
                
                report += `✅ 用户创建成功！(耗时: ${createTime.toFixed(2)}秒)\n`;
                report += `用户ID: ${newUser.identifier}\n`;
                report += `显示名称: ${newUser.displayName}\n`;
                report += `用户类型: ${newUser.type}\n`;
                report += `创建时间: ${new Date(newUser.createdAt).toLocaleString()}\n`;

                updateResults('user-creation-results', 'success', report);

            } catch (error) {
                let report = `❌ 用户创建失败:\n`;
                report += `错误信息: ${error.message}\n`;
                if (error.stack) {
                    report += `错误堆栈:\n${error.stack}\n`;
                }
                updateResults('user-creation-results', 'error', report);
            }
        }

        function testUserCreationInGame() {
            if (!gameFrame) {
                updateResults('user-creation-results', 'error', '❌ 请先加载游戏');
                return;
            }

            updateResults('user-creation-results', 'info', 
                '🎮 在游戏中测试用户创建:\n\n' +
                '步骤:\n' +
                '1. 在游戏界面中点击"用户管理"按钮\n' +
                '2. 在弹出的对话框中填写用户信息\n' +
                '3. 点击"创建用户"按钮\n' +
                '4. 观察是否快速完成，不再出现长时间等待\n\n' +
                '💡 如果修复成功，用户创建应该在1-2秒内完成'
            );
        }

        function testUserCreationStress() {
            updateResults('user-creation-results', 'warning', 
                '🔥 用户创建压力测试:\n\n' +
                '压力测试将:\n' +
                '1. 快速连续创建多个用户\n' +
                '2. 测试初始化修复在高负载下的表现\n' +
                '3. 验证系统稳定性\n\n' +
                '💡 这有助于验证修复的健壮性'
            );
        }

        function resetTest() {
            stopInitTimer();
            
            document.getElementById('init-timer').style.display = 'none';
            document.getElementById('init-progress').style.display = 'none';
            document.getElementById('game-container').style.display = 'none';
            
            initStartTime = null;
            gameFrame = null;
            
            updateResults('game-test-results', 'info', '🔄 测试已重置，可以开始新的测试');
            updateResults('performance-results', 'info', '点击按钮开始性能测试...');
            updateResults('analysis-results', 'info', '点击按钮开始分析...');
            updateResults('user-creation-results', 'info', '填写用户信息后点击测试按钮...');
        }

        function updateResults(elementId, type, content) {
            const element = document.getElementById(elementId);
            element.className = `results ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        // 页面加载完成后显示说明
        document.addEventListener('DOMContentLoaded', () => {
            updateResults('game-test-results', 'info', 
                '🔧 初始化阻塞问题修复验证工具\n\n' +
                '本工具专门用于验证用户管理器初始化卡住问题的修复效果。\n\n' +
                '使用步骤:\n' +
                '1. 点击"加载游戏并监控初始化"开始测试\n' +
                '2. 观察初始化时间是否在1-2秒内完成\n' +
                '3. 使用"分析初始化过程"检查修复方法\n' +
                '4. 测试用户创建功能是否快速响应\n\n' +
                '如果修复成功，初始化应该快速完成，不再卡住。'
            );
        });
    </script>
</body>
</html>
