<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储服务初始化诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #16213e;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.danger {
            background: #f44336;
        }
        .results {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .results.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .results.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .results.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: none;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.success {
            background: #4CAF50;
        }
        .status-indicator.error {
            background: #f44336;
        }
        .status-indicator.warning {
            background: #ff9800;
        }
        .status-indicator.info {
            background: #2196F3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 存储服务初始化诊断工具</h1>
        
        <div class="section">
            <h2>📋 修复说明</h2>
            <div class="results info">
本次修复针对"存储服务未初始化，无法创建用户"错误：

🔧 修复内容：
1. 用户管理器createUser方法增加存储服务初始化状态检查
2. 用户系统适配器ensureDependencies方法增强，等待存储服务完成初始化
3. 用户管理器init方法增加存储服务初始化等待机制

⏰ 等待机制：
- 用户管理器初始化：最多等待5秒存储服务完成初始化
- 用户创建过程：最多等待5秒存储服务完成初始化
- 用户系统适配器：最多等待10秒存储服务完成初始化

✅ 预期效果：
- 消除"存储服务未初始化"错误
- 用户创建功能正常工作
- 提供详细的初始化状态日志
            </div>
        </div>

        <div class="section">
            <h2>🎮 游戏环境测试</h2>
            <div class="test-controls">
                <button class="test-button" onclick="loadGame()">加载游戏</button>
                <button class="test-button" onclick="monitorInitialization()">监控初始化过程</button>
                <button class="test-button danger" onclick="clearData()">清除数据</button>
            </div>
            
            <div class="iframe-container" id="game-container" style="display: none;">
                <iframe id="game-frame" src=""></iframe>
            </div>
            
            <div id="game-status" class="results info">
                点击"加载游戏"开始测试...
            </div>
        </div>

        <div class="section">
            <h2>🔍 存储服务状态检查</h2>
            <div class="test-controls">
                <button class="test-button" onclick="checkStorageServiceStatus()">检查存储服务状态</button>
                <button class="test-button" onclick="testStorageServiceInit()">测试存储服务初始化</button>
                <button class="test-button" onclick="watchInitializationProcess()">观察初始化过程</button>
            </div>
            
            <div id="storage-status" class="results info">
                点击按钮开始检查...
            </div>
        </div>

        <div class="section">
            <h2>👤 用户管理器状态检查</h2>
            <div class="test-controls">
                <button class="test-button" onclick="checkUserManagerStatus()">检查用户管理器状态</button>
                <button class="test-button" onclick="testUserCreationFlow()">测试用户创建流程</button>
                <button class="test-button" onclick="simulateUserCreation()">模拟用户创建</button>
            </div>
            
            <div id="user-manager-status" class="results info">
                点击按钮开始检查...
            </div>
        </div>

        <div class="section">
            <h2>🧪 实际用户创建测试</h2>
            <div class="form-group">
                <label for="test-identifier">用户标识符:</label>
                <input type="text" id="test-identifier" placeholder="输入测试用户标识符" value="testuser456">
            </div>
            <div class="form-group">
                <label for="test-display-name">显示名称:</label>
                <input type="text" id="test-display-name" placeholder="输入测试显示名称" value="测试用户456">
            </div>
            <div class="test-controls">
                <button class="test-button" onclick="performUserCreationTest()">执行用户创建测试</button>
                <button class="test-button" onclick="testInGameUserCreation()">在游戏中测试</button>
            </div>
            
            <div id="creation-test-results" class="results info">
                填写用户信息后点击测试按钮...
            </div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let initializationMonitor = null;

        function loadGame() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            
            updateResults('game-status', 'info', '🔄 正在加载游戏...');
            
            frame.src = './index.html';
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                updateResults('game-status', 'success', '✅ 游戏已加载，等待初始化完成...');
                
                // 等待游戏完全初始化
                setTimeout(() => {
                    checkStorageServiceStatus();
                }, 3000);
            };

            frame.onerror = function(error) {
                updateResults('game-status', 'error', `❌ 游戏加载失败: ${error}`);
            };
        }

        function monitorInitialization() {
            if (!gameFrame) {
                updateResults('game-status', 'error', '❌ 请先加载游戏');
                return;
            }

            updateResults('game-status', 'info', '🔍 开始监控初始化过程...\n请查看浏览器控制台获取详细日志');
            
            // 监控游戏窗口的控制台输出
            try {
                const gameWindow = gameFrame.contentWindow;
                if (gameWindow.console) {
                    // 重写console.log来捕获日志
                    const originalLog = gameWindow.console.log;
                    gameWindow.console.log = function(...args) {
                        originalLog.apply(gameWindow.console, args);
                        // 这里可以添加日志捕获逻辑
                    };
                }
            } catch (error) {
                console.warn('无法监控游戏窗口控制台:', error);
            }
        }

        function checkStorageServiceStatus() {
            if (!gameFrame) {
                updateResults('storage-status', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 存储服务状态检查:\n\n';

                if (gameWindow.storageService) {
                    const storage = gameWindow.storageService;
                    report += '存储服务基本信息:\n';
                    report += `  存在: ✅\n`;
                    report += `  已初始化: ${storage.initialized ? '✅' : '❌'}\n`;
                    report += `  存储类型: ${storage.storageType || '未知'}\n`;
                    report += `  适配器: ${storage.adapter ? storage.adapter.constructor.name : '无'}\n`;
                    
                    if (storage.adapter) {
                        report += `  适配器已初始化: ${storage.adapter.initialized !== false ? '✅' : '❌'}\n`;
                    }

                    // 检查存储服务方法
                    report += '\n存储服务方法检查:\n';
                    const methods = ['get', 'put', 'delete', 'init'];
                    methods.forEach(method => {
                        const exists = typeof storage[method] === 'function';
                        report += `  ${method}: ${exists ? '✅' : '❌'}\n`;
                    });

                    // 如果未初始化，尝试获取更多信息
                    if (!storage.initialized) {
                        report += '\n⚠️ 存储服务未完成初始化\n';
                        report += '可能原因:\n';
                        report += '- 异步初始化仍在进行中\n';
                        report += '- 初始化过程中出现错误\n';
                        report += '- 依赖的存储适配器初始化失败\n';
                    }

                } else {
                    report += '❌ 存储服务不存在\n';
                }

                updateResults('storage-status', gameWindow.storageService && gameWindow.storageService.initialized ? 'success' : 'error', report);

            } catch (error) {
                updateResults('storage-status', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        function testStorageServiceInit() {
            if (!gameFrame) {
                updateResults('storage-status', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🧪 存储服务初始化测试:\n\n';

                if (gameWindow.storageService) {
                    const storage = gameWindow.storageService;
                    
                    // 测试基础操作
                    report += '基础操作测试:\n';
                    
                    if (storage.initialized) {
                        // 测试写入和读取
                        const testKey = 'init_test_' + Date.now();
                        const testValue = { test: true, timestamp: Date.now() };
                        
                        storage.put(testKey, testValue).then(() => {
                            return storage.get(testKey);
                        }).then((result) => {
                            if (result && result.test === true) {
                                report += '  读写测试: ✅\n';
                            } else {
                                report += '  读写测试: ❌ 数据不匹配\n';
                            }
                            // 清理测试数据
                            storage.delete(testKey);
                            updateResults('storage-status', 'success', report);
                        }).catch((error) => {
                            report += `  读写测试: ❌ ${error.message}\n`;
                            updateResults('storage-status', 'error', report);
                        });
                        
                        report += '  读写测试: 进行中...\n';
                    } else {
                        report += '  ❌ 存储服务未初始化，无法进行测试\n';
                        updateResults('storage-status', 'error', report);
                    }
                } else {
                    report += '❌ 存储服务不存在\n';
                    updateResults('storage-status', 'error', report);
                }

                updateResults('storage-status', 'info', report);

            } catch (error) {
                updateResults('storage-status', 'error', `❌ 测试失败: ${error.message}`);
            }
        }

        function watchInitializationProcess() {
            if (!gameFrame) {
                updateResults('storage-status', 'error', '❌ 请先加载游戏');
                return;
            }

            let report = '👁️ 观察初始化过程:\n\n';
            report += '开始监控存储服务初始化状态...\n';
            
            updateResults('storage-status', 'info', report);

            // 定期检查初始化状态
            let checkCount = 0;
            const maxChecks = 50; // 最多检查5秒
            
            const checkInterval = setInterval(() => {
                try {
                    const gameWindow = gameFrame.contentWindow;
                    if (gameWindow.storageService) {
                        const storage = gameWindow.storageService;
                        
                        if (storage.initialized) {
                            report += `✅ 存储服务初始化完成 (检查次数: ${checkCount + 1})\n`;
                            report += `存储类型: ${storage.storageType}\n`;
                            report += `适配器: ${storage.adapter ? storage.adapter.constructor.name : '无'}\n`;
                            
                            updateResults('storage-status', 'success', report);
                            clearInterval(checkInterval);
                            return;
                        } else {
                            report += `⏳ 第${checkCount + 1}次检查: 存储服务仍在初始化中...\n`;
                        }
                    } else {
                        report += `⏳ 第${checkCount + 1}次检查: 存储服务尚未创建...\n`;
                    }
                    
                    checkCount++;
                    
                    if (checkCount >= maxChecks) {
                        report += `❌ 监控超时，存储服务可能初始化失败\n`;
                        updateResults('storage-status', 'error', report);
                        clearInterval(checkInterval);
                    } else {
                        updateResults('storage-status', 'info', report);
                    }
                    
                } catch (error) {
                    report += `❌ 监控过程中出现错误: ${error.message}\n`;
                    updateResults('storage-status', 'error', report);
                    clearInterval(checkInterval);
                }
            }, 100);
        }

        function checkUserManagerStatus() {
            if (!gameFrame) {
                updateResults('user-manager-status', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 用户管理器状态检查:\n\n';

                // 检查用户系统适配器
                if (gameWindow.userSystemAdapter) {
                    const adapter = gameWindow.userSystemAdapter;
                    report += '用户系统适配器:\n';
                    report += `  存在: ✅\n`;
                    report += `  已初始化: ${adapter.initialized ? '✅' : '❌'}\n`;
                    
                    if (adapter.initialized) {
                        const userManager = adapter.getUserManager();
                        if (userManager) {
                            report += '\n用户管理器:\n';
                            report += `  存在: ✅\n`;
                            report += `  已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                            report += `  存储服务: ${userManager.storageService ? '✅' : '❌'}\n`;
                            
                            if (userManager.storageService) {
                                report += `  存储服务已初始化: ${userManager.storageService.initialized ? '✅' : '❌'}\n`;
                                report += `  存储类型: ${userManager.storageService.storageType || '未知'}\n`;
                            }
                            
                            report += `  用户数量: ${userManager.users ? userManager.users.size : 0}\n`;
                            report += `  当前用户: ${userManager.currentUser ? userManager.currentUser.displayName : '无'}\n`;
                        } else {
                            report += '\n❌ 用户管理器不存在\n';
                        }
                    }
                } else {
                    report += '❌ 用户系统适配器不存在\n';
                }

                updateResults('user-manager-status', 'info', report);

            } catch (error) {
                updateResults('user-manager-status', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        function testUserCreationFlow() {
            if (!gameFrame) {
                updateResults('user-manager-status', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🧪 用户创建流程测试:\n\n';

                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                    const userManager = gameWindow.userSystemAdapter.getUserManager();
                    
                    if (userManager) {
                        report += '用户管理器检查:\n';
                        report += `  存在: ✅\n`;
                        report += `  已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                        report += `  存储服务: ${userManager.storageService ? '✅' : '❌'}\n`;
                        
                        if (userManager.storageService) {
                            report += `  存储服务已初始化: ${userManager.storageService.initialized ? '✅' : '❌'}\n`;
                            
                            if (userManager.storageService.initialized) {
                                report += '\n✅ 所有前置条件满足，用户创建应该可以正常工作\n';
                                
                                // 检查必要的依赖
                                const deps = ['StorageKeySchema', 'UserDataValidator', 'UserDataStructure'];
                                report += '\n依赖项检查:\n';
                                deps.forEach(dep => {
                                    const exists = gameWindow[dep] !== undefined;
                                    report += `  ${dep}: ${exists ? '✅' : '❌'}\n`;
                                });
                                
                            } else {
                                report += '\n❌ 存储服务未完成初始化，用户创建会失败\n';
                            }
                        } else {
                            report += '\n❌ 用户管理器没有存储服务引用\n';
                        }
                    } else {
                        report += '❌ 用户管理器不存在\n';
                    }
                } else {
                    report += '❌ 用户系统适配器未初始化\n';
                }

                updateResults('user-manager-status', 'info', report);

            } catch (error) {
                updateResults('user-manager-status', 'error', `❌ 测试失败: ${error.message}`);
            }
        }

        function simulateUserCreation() {
            updateResults('user-manager-status', 'info', 
                '🎯 模拟用户创建测试:\n\n' +
                '这个测试将模拟用户创建的完整流程，包括:\n' +
                '1. 检查所有前置条件\n' +
                '2. 验证存储服务状态\n' +
                '3. 检查依赖项完整性\n' +
                '4. 模拟创建过程中的各个步骤\n\n' +
                '💡 请使用"执行用户创建测试"进行实际测试'
            );
        }

        async function performUserCreationTest() {
            const identifier = document.getElementById('test-identifier').value.trim();
            const displayName = document.getElementById('test-display-name').value.trim();

            if (!identifier || !displayName) {
                updateResults('creation-test-results', 'error', '❌ 请填写用户标识符和显示名称');
                return;
            }

            if (!gameFrame) {
                updateResults('creation-test-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = `🧪 用户创建测试:\n标识符: ${identifier}\n显示名称: ${displayName}\n\n`;

                // 获取用户管理器
                if (!gameWindow.userSystemAdapter || !gameWindow.userSystemAdapter.initialized) {
                    report += '❌ 用户系统适配器未初始化\n';
                    updateResults('creation-test-results', 'error', report);
                    return;
                }

                const userManager = gameWindow.userSystemAdapter.getUserManager();
                if (!userManager) {
                    report += '❌ 无法获取用户管理器\n';
                    updateResults('creation-test-results', 'error', report);
                    return;
                }

                report += '前置条件检查:\n';
                report += `  用户管理器: ✅\n`;
                report += `  用户管理器已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                report += `  存储服务: ${userManager.storageService ? '✅' : '❌'}\n`;
                
                if (userManager.storageService) {
                    report += `  存储服务已初始化: ${userManager.storageService.initialized ? '✅' : '❌'}\n`;
                    
                    if (!userManager.storageService.initialized) {
                        report += '\n⚠️ 存储服务未初始化，这可能导致创建失败\n';
                    }
                }

                report += '\n开始创建用户...\n';
                updateResults('creation-test-results', 'info', report);

                // 执行用户创建
                const newUser = await userManager.createUser(identifier, displayName);
                
                report += `✅ 用户创建成功!\n`;
                report += `用户ID: ${newUser.identifier}\n`;
                report += `显示名称: ${newUser.displayName}\n`;
                report += `用户类型: ${newUser.type}\n`;
                report += `创建时间: ${new Date(newUser.createdAt).toLocaleString()}\n`;

                updateResults('creation-test-results', 'success', report);

            } catch (error) {
                let report = `❌ 用户创建失败:\n`;
                report += `错误信息: ${error.message}\n`;
                if (error.stack) {
                    report += `错误堆栈:\n${error.stack}\n`;
                }
                updateResults('creation-test-results', 'error', report);
            }
        }

        function testInGameUserCreation() {
            if (!gameFrame) {
                updateResults('creation-test-results', 'error', '❌ 请先加载游戏');
                return;
            }

            updateResults('creation-test-results', 'info', 
                '🎮 在游戏中测试用户创建:\n\n' +
                '步骤:\n' +
                '1. 在游戏界面中点击"用户管理"按钮\n' +
                '2. 在弹出的对话框中填写用户信息\n' +
                '3. 点击"创建用户"按钮\n' +
                '4. 观察是否成功创建用户\n\n' +
                '💡 同时观察浏览器控制台的详细日志\n' +
                '如果仍然出现"存储服务未初始化"错误，\n' +
                '请检查存储服务的初始化状态'
            );
        }

        function clearData() {
            if (confirm('确定要清除所有数据吗？这将删除所有用户和游戏数据。')) {
                try {
                    localStorage.clear();
                    if (gameFrame) {
                        gameFrame.contentWindow.localStorage.clear();
                    }
                    updateResults('game-status', 'success', '✅ 数据已清除，请重新加载游戏进行测试');
                } catch (error) {
                    updateResults('game-status', 'error', `❌ 清除数据失败: ${error.message}`);
                }
            }
        }

        function updateResults(elementId, type, content) {
            const element = document.getElementById(elementId);
            element.className = `results ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        // 页面加载完成后显示说明
        document.addEventListener('DOMContentLoaded', () => {
            updateResults('game-status', 'info', 
                '🔍 存储服务初始化诊断工具\n\n' +
                '本工具专门用于诊断和修复"存储服务未初始化"错误。\n\n' +
                '使用步骤:\n' +
                '1. 点击"加载游戏"加载游戏环境\n' +
                '2. 使用"检查存储服务状态"验证存储服务\n' +
                '3. 使用"检查用户管理器状态"验证用户管理器\n' +
                '4. 执行实际的用户创建测试\n\n' +
                '如果修复成功，应该不再出现初始化错误。'
            );
        });
    </script>
</body>
</html>
