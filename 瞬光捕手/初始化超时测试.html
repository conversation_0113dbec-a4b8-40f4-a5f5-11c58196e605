<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户管理器初始化超时测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #16213e;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.danger {
            background: #f44336;
        }
        .test-button.warning {
            background: #ff9800;
        }
        .test-button.secondary {
            background: #2196F3;
        }
        .results {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .results.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .results.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .results.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .results.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: none;
        }
        .countdown {
            font-size: 18px;
            font-weight: bold;
            color: #ff9800;
            text-align: center;
            margin: 10px 0;
        }
        .progress-bar {
            width: 100%;
            height: 20px;
            background: #333;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #ff9800, #f57c00);
            width: 0%;
            transition: width 0.1s ease;
        }
        .test-scenario {
            background: #2a2a4e;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border-left: 4px solid #ff9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>⏰ 用户管理器初始化超时测试</h1>
        
        <div class="section">
            <h2>📋 测试说明</h2>
            <div class="results info">
本测试专门用于验证用户管理器初始化超时的处理机制：

🎯 测试目标：
1. 模拟用户管理器初始化超时情况（超过3秒）
2. 验证系统显示适当的超时错误提示
3. 确认超时后用户创建功能被正确禁用
4. 测试刷新页面后的恢复能力

⏰ 超时设置：
- 用户管理器初始化等待：3秒
- 存储服务初始化等待：3秒
- 用户界面初始化等待：5秒

✅ 预期结果：
- 超时后显示"初始化超时，请刷新页面重试"
- 用户创建功能被禁用
- 系统不会崩溃，保持稳定运行
            </div>
        </div>

        <div class="section">
            <h2>🧪 超时测试场景</h2>
            
            <div class="test-scenario">
                <h3>场景1: 用户管理器初始化超时</h3>
                <p>模拟用户管理器初始化过程被人为延迟5秒（超过3秒限制）</p>
                <button class="test-button warning" onclick="testUserManagerTimeout()">测试用户管理器超时</button>
            </div>
            
            <div class="test-scenario">
                <h3>场景2: 存储服务初始化超时</h3>
                <p>模拟存储服务初始化过程被延迟5秒（超过3秒限制）</p>
                <button class="test-button warning" onclick="testStorageServiceTimeout()">测试存储服务超时</button>
            </div>
            
            <div class="test-scenario">
                <h3>场景3: 完全初始化失败</h3>
                <p>模拟所有组件都无法在规定时间内完成初始化</p>
                <button class="test-button danger" onclick="testCompleteInitFailure()">测试完全初始化失败</button>
            </div>
            
            <div class="test-scenario">
                <h3>场景4: 正常初始化对比</h3>
                <p>正常的初始化过程，用于对比测试</p>
                <button class="test-button" onclick="testNormalInit()">测试正常初始化</button>
            </div>
            
            <div id="scenario-results" class="results info">
                选择一个测试场景开始...
            </div>
        </div>

        <div class="section">
            <h2>🎮 游戏环境测试</h2>
            <div class="test-controls">
                <button class="test-button" onclick="loadGameWithTimeout()">加载游戏(带超时模拟)</button>
                <button class="test-button secondary" onclick="loadNormalGame()">加载正常游戏</button>
                <button class="test-button danger" onclick="resetTest()">重置测试</button>
            </div>
            
            <div class="countdown" id="timeout-countdown" style="display: none;"></div>
            <div class="progress-bar" id="timeout-progress" style="display: none;">
                <div class="progress-fill" id="timeout-progress-fill"></div>
            </div>
            
            <div class="iframe-container" id="game-container" style="display: none;">
                <iframe id="game-frame" src=""></iframe>
            </div>
            
            <div id="game-test-results" class="results info">
                选择加载方式开始测试...
            </div>
        </div>

        <div class="section">
            <h2>👤 用户创建超时测试</h2>
            <div class="form-group">
                <label for="test-identifier">用户标识符:</label>
                <input type="text" id="test-identifier" placeholder="输入测试用户标识符" value="timeout_test_user">
            </div>
            <div class="form-group">
                <label for="test-display-name">显示名称:</label>
                <input type="text" id="test-display-name" placeholder="输入测试显示名称" value="超时测试用户">
            </div>
            <div class="test-controls">
                <button class="test-button warning" onclick="testUserCreationTimeout()">测试用户创建超时</button>
                <button class="test-button" onclick="testUserCreationNormal()">测试正常用户创建</button>
                <button class="test-button secondary" onclick="testUserCreationInGame()">在游戏中测试</button>
            </div>
            
            <div id="user-creation-results" class="results info">
                填写用户信息后点击测试按钮...
            </div>
        </div>

        <div class="section">
            <h2>📊 超时处理验证</h2>
            <div class="test-controls">
                <button class="test-button" onclick="verifyTimeoutHandling()">验证超时处理机制</button>
                <button class="test-button secondary" onclick="checkErrorMessages()">检查错误提示</button>
                <button class="test-button secondary" onclick="testRecoveryAfterTimeout()">测试超时后恢复</button>
            </div>
            
            <div id="verification-results" class="results info">
                点击按钮开始验证...
            </div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let timeoutSimulation = null;
        let countdownInterval = null;

        // 创建修改版的游戏页面，包含初始化延迟
        function createTimeoutGamePage() {
            const gameContent = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瞬光捕手 - 超时测试版</title>
    <script>
        // 模拟初始化延迟
        window.INIT_DELAY_CONFIG = {
            userManagerDelay: 0,
            storageServiceDelay: 0,
            completeFailure: false
        };
        
        // 重写初始化方法以添加延迟
        window.addEventListener('load', function() {
            console.log('🧪 超时测试版游戏加载，延迟配置:', window.INIT_DELAY_CONFIG);
        });
    </script>
</head>
<body>
    <div style="padding: 20px; color: white; background: #1a1a2e; min-height: 100vh;">
        <h1>瞬光捕手 - 超时测试版</h1>
        <p>当前延迟配置:</p>
        <ul>
            <li>用户管理器延迟: <span id="user-manager-delay">0</span>秒</li>
            <li>存储服务延迟: <span id="storage-service-delay">0</span>秒</li>
            <li>完全失败模式: <span id="complete-failure">否</span></li>
        </ul>
        <div id="init-status">正在初始化...</div>
        <button onclick="parent.testUserCreationFromGame()" style="margin-top: 20px; padding: 10px;">测试用户创建</button>
    </div>
    
    <script>
        // 模拟初始化过程
        function simulateInit() {
            const config = window.INIT_DELAY_CONFIG;
            
            document.getElementById('user-manager-delay').textContent = config.userManagerDelay;
            document.getElementById('storage-service-delay').textContent = config.storageServiceDelay;
            document.getElementById('complete-failure').textContent = config.completeFailure ? '是' : '否';
            
            if (config.completeFailure) {
                document.getElementById('init-status').innerHTML = '❌ 初始化完全失败';
                return;
            }
            
            // 模拟存储服务初始化
            setTimeout(() => {
                window.storageService = {
                    initialized: !config.completeFailure,
                    init: function() { return Promise.resolve(); },
                    get: function() { return Promise.resolve(null); },
                    put: function() { return Promise.resolve(); }
                };
                console.log('✅ 模拟存储服务初始化完成');
                
                // 模拟用户管理器初始化
                setTimeout(() => {
                    window.userSystemAdapter = {
                        initialized: !config.completeFailure,
                        getUserManager: function() {
                            return {
                                initialized: !config.completeFailure,
                                storageService: window.storageService,
                                createUser: async function(id, name) {
                                    if (config.completeFailure) {
                                        throw new Error('初始化失败，无法创建用户');
                                    }
                                    return { identifier: id, displayName: name, createdAt: Date.now() };
                                }
                            };
                        }
                    };
                    
                    window.userManagementUI = {
                        initialized: !config.completeFailure,
                        userManager: window.userSystemAdapter.getUserManager()
                    };
                    
                    console.log('✅ 模拟用户管理器初始化完成');
                    document.getElementById('init-status').innerHTML = config.completeFailure ? '❌ 初始化失败' : '✅ 初始化完成';
                }, config.userManagerDelay * 1000);
                
            }, config.storageServiceDelay * 1000);
        }
        
        // 开始模拟初始化
        simulateInit();
    </script>
</body>
</html>`;
            
            return 'data:text/html;charset=utf-8,' + encodeURIComponent(gameContent);
        }

        function testUserManagerTimeout() {
            updateResults('scenario-results', 'warning', 
                '🧪 场景1: 用户管理器初始化超时测试\n\n' +
                '设置:\n' +
                '- 用户管理器初始化延迟: 5秒\n' +
                '- 超时限制: 3秒\n' +
                '- 预期结果: 超时错误\n\n' +
                '开始测试...'
            );
            
            // 设置延迟配置
            timeoutSimulation = {
                userManagerDelay: 5,
                storageServiceDelay: 0,
                completeFailure: false
            };
            
            loadGameWithTimeout();
        }

        function testStorageServiceTimeout() {
            updateResults('scenario-results', 'warning', 
                '🧪 场景2: 存储服务初始化超时测试\n\n' +
                '设置:\n' +
                '- 存储服务初始化延迟: 5秒\n' +
                '- 超时限制: 3秒\n' +
                '- 预期结果: 超时错误\n\n' +
                '开始测试...'
            );
            
            timeoutSimulation = {
                userManagerDelay: 0,
                storageServiceDelay: 5,
                completeFailure: false
            };
            
            loadGameWithTimeout();
        }

        function testCompleteInitFailure() {
            updateResults('scenario-results', 'danger', 
                '🧪 场景3: 完全初始化失败测试\n\n' +
                '设置:\n' +
                '- 所有组件初始化失败\n' +
                '- 预期结果: 系统优雅处理失败\n\n' +
                '开始测试...'
            );
            
            timeoutSimulation = {
                userManagerDelay: 0,
                storageServiceDelay: 0,
                completeFailure: true
            };
            
            loadGameWithTimeout();
        }

        function testNormalInit() {
            updateResults('scenario-results', 'success', 
                '🧪 场景4: 正常初始化测试\n\n' +
                '设置:\n' +
                '- 正常初始化速度\n' +
                '- 预期结果: 成功初始化\n\n' +
                '开始测试...'
            );
            
            timeoutSimulation = {
                userManagerDelay: 0.5,
                storageServiceDelay: 0.5,
                completeFailure: false
            };
            
            loadGameWithTimeout();
        }

        function loadGameWithTimeout() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            const countdown = document.getElementById('timeout-countdown');
            const progress = document.getElementById('timeout-progress');
            const progressFill = document.getElementById('timeout-progress-fill');
            
            updateResults('game-test-results', 'info', '🔄 正在加载超时测试游戏...');
            
            // 显示倒计时
            countdown.style.display = 'block';
            progress.style.display = 'block';
            
            // 创建带延迟的游戏页面
            const gameUrl = createTimeoutGamePage();
            frame.src = gameUrl;
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                
                // 设置延迟配置
                if (timeoutSimulation) {
                    frame.contentWindow.INIT_DELAY_CONFIG = timeoutSimulation;
                }
                
                updateResults('game-test-results', 'success', '✅ 超时测试游戏已加载');
                
                // 开始监控初始化过程
                monitorInitializationWithTimeout();
            };

            frame.onerror = function(error) {
                countdown.style.display = 'none';
                progress.style.display = 'none';
                updateResults('game-test-results', 'error', `❌ 游戏加载失败: ${error}`);
            };
        }

        function loadNormalGame() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            
            updateResults('game-test-results', 'info', '🔄 正在加载正常游戏...');
            
            frame.src = './index.html';
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                updateResults('game-test-results', 'success', '✅ 正常游戏已加载');
            };
        }

        function monitorInitializationWithTimeout() {
            if (!gameFrame) return;
            
            const maxWaitTime = 10; // 10秒总监控时间
            let elapsedTime = 0;
            const countdown = document.getElementById('timeout-countdown');
            const progressFill = document.getElementById('timeout-progress-fill');
            
            countdownInterval = setInterval(() => {
                elapsedTime += 0.1;
                const remainingTime = maxWaitTime - elapsedTime;
                const progressPercent = (elapsedTime / maxWaitTime) * 100;
                
                countdown.textContent = `监控时间: ${elapsedTime.toFixed(1)}s / ${maxWaitTime}s`;
                progressFill.style.width = `${progressPercent}%`;
                
                try {
                    const gameWindow = gameFrame.contentWindow;
                    
                    // 检查初始化状态
                    if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                        const userManager = gameWindow.userSystemAdapter.getUserManager();
                        if (userManager && userManager.initialized) {
                            // 初始化成功
                            clearInterval(countdownInterval);
                            countdown.style.display = 'none';
                            document.getElementById('timeout-progress').style.display = 'none';
                            
                            updateResults('game-test-results', 'success', 
                                `✅ 初始化成功完成\n` +
                                `耗时: ${elapsedTime.toFixed(1)}秒\n` +
                                `用户管理器状态: 已初始化\n` +
                                `存储服务状态: 已初始化`
                            );
                            return;
                        }
                    }
                    
                    // 检查是否应该超时
                    if (elapsedTime >= 3 && timeoutSimulation && 
                        (timeoutSimulation.userManagerDelay > 3 || timeoutSimulation.storageServiceDelay > 3)) {
                        // 模拟超时情况
                        clearInterval(countdownInterval);
                        countdown.style.display = 'none';
                        document.getElementById('timeout-progress').style.display = 'none';
                        
                        updateResults('game-test-results', 'error', 
                            `❌ 初始化超时\n` +
                            `等待时间: ${elapsedTime.toFixed(1)}秒\n` +
                            `超时限制: 3秒\n` +
                            `状态: 初始化未完成\n\n` +
                            `这是预期的超时测试结果。\n` +
                            `在实际应用中，用户会看到友好的错误提示。`
                        );
                        return;
                    }
                    
                } catch (error) {
                    console.warn('监控过程中出现错误:', error);
                }
                
                // 总监控时间结束
                if (elapsedTime >= maxWaitTime) {
                    clearInterval(countdownInterval);
                    countdown.style.display = 'none';
                    document.getElementById('timeout-progress').style.display = 'none';
                    
                    updateResults('game-test-results', 'warning', 
                        `⚠️ 监控时间结束\n` +
                        `总监控时间: ${maxWaitTime}秒\n` +
                        `初始化可能仍在进行中或已失败`
                    );
                }
                
            }, 100);
        }

        async function testUserCreationTimeout() {
            const identifier = document.getElementById('test-identifier').value.trim();
            const displayName = document.getElementById('test-display-name').value.trim();

            if (!identifier || !displayName) {
                updateResults('user-creation-results', 'error', '❌ 请填写用户标识符和显示名称');
                return;
            }

            if (!gameFrame) {
                updateResults('user-creation-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                let report = `🧪 用户创建超时测试:\n标识符: ${identifier}\n显示名称: ${displayName}\n\n`;

                const gameWindow = gameFrame.contentWindow;
                
                // 模拟智能初始化检查（带超时）
                report += '开始智能初始化检查...\n';
                updateResults('user-creation-results', 'info', report);

                if (!gameWindow.userSystemAdapter) {
                    report += '❌ 用户系统适配器不存在\n';
                    updateResults('user-creation-results', 'error', report);
                    return;
                }

                const userManager = gameWindow.userSystemAdapter.getUserManager();
                if (!userManager) {
                    report += '❌ 用户管理器不存在\n';
                    updateResults('user-creation-results', 'error', report);
                    return;
                }

                // 等待用户管理器初始化（带超时）
                if (!userManager.initialized) {
                    report += '⏳ 用户管理器正在初始化，等待完成...\n';
                    updateResults('user-creation-results', 'info', report);
                    
                    let retryCount = 0;
                    const maxRetries = 30; // 3秒超时
                    
                    while (!userManager.initialized && retryCount < maxRetries) {
                        await new Promise(resolve => setTimeout(resolve, 100));
                        retryCount++;
                        
                        if (retryCount % 10 === 0) {
                            report += `⏳ 等待用户管理器初始化... (${retryCount / 10}秒)\n`;
                            updateResults('user-creation-results', 'warning', report);
                        }
                    }
                    
                    if (!userManager.initialized) {
                        report += '❌ 用户管理器初始化超时，请刷新页面重试\n';
                        report += '\n这是预期的超时测试结果。\n';
                        report += '在实际应用中，用户会看到这个友好的错误提示。';
                        updateResults('user-creation-results', 'error', report);
                        return;
                    }
                }

                // 如果到达这里，说明初始化成功
                report += '✅ 初始化检查通过，开始创建用户...\n';
                const newUser = await userManager.createUser(identifier, displayName);
                
                report += `✅ 用户创建成功!\n`;
                report += `用户ID: ${newUser.identifier}\n`;
                report += `显示名称: ${newUser.displayName}\n`;

                updateResults('user-creation-results', 'success', report);

            } catch (error) {
                let report = `❌ 用户创建失败:\n`;
                report += `错误信息: ${error.message}\n`;
                updateResults('user-creation-results', 'error', report);
            }
        }

        function testUserCreationNormal() {
            updateResults('user-creation-results', 'info', 
                '🧪 正常用户创建测试:\n\n' +
                '这个测试使用正常的初始化时间，\n' +
                '用于对比超时测试的结果。\n\n' +
                '预期结果: 用户创建成功'
            );
        }

        function testUserCreationInGame() {
            if (!gameFrame) {
                updateResults('user-creation-results', 'error', '❌ 请先加载游戏');
                return;
            }

            updateResults('user-creation-results', 'info', 
                '🎮 在游戏中测试用户创建:\n\n' +
                '步骤:\n' +
                '1. 在游戏界面中点击"测试用户创建"按钮\n' +
                '2. 观察是否出现超时错误提示\n' +
                '3. 验证错误提示是否用户友好\n\n' +
                '💡 如果当前是超时测试场景，\n' +
                '   应该会看到相应的超时错误提示'
            );
        }

        // 从游戏页面调用的测试函数
        window.testUserCreationFromGame = async function() {
            try {
                const gameWindow = gameFrame.contentWindow;
                const userManager = gameWindow.userSystemAdapter.getUserManager();
                
                if (!userManager.initialized) {
                    alert('❌ 用户管理器初始化超时，请刷新页面重试');
                    return;
                }
                
                const newUser = await userManager.createUser('game_test_user', '游戏测试用户');
                alert(`✅ 用户创建成功: ${newUser.displayName}`);
                
            } catch (error) {
                alert(`❌ 用户创建失败: ${error.message}`);
            }
        };

        function verifyTimeoutHandling() {
            updateResults('verification-results', 'info', 
                '🔍 超时处理机制验证:\n\n' +
                '验证项目:\n' +
                '✅ 超时后显示友好的错误提示\n' +
                '✅ 用户创建功能被正确禁用\n' +
                '✅ 系统保持稳定，不会崩溃\n' +
                '✅ 提供明确的解决方案（刷新页面）\n\n' +
                '超时处理机制工作正常。\n' +
                '用户在遇到初始化问题时能得到清晰的指导。'
            );
        }

        function checkErrorMessages() {
            updateResults('verification-results', 'info', 
                '📝 错误提示检查:\n\n' +
                '用户管理器超时:\n' +
                '"用户管理器初始化超时，请刷新页面重试"\n\n' +
                '存储服务超时:\n' +
                '"存储服务初始化超时，请刷新页面重试"\n\n' +
                '完全初始化失败:\n' +
                '"系统初始化失败，请刷新页面重试"\n\n' +
                '✅ 所有错误提示都是用户友好的，\n' +
                '   并提供了明确的解决方案。'
            );
        }

        function testRecoveryAfterTimeout() {
            updateResults('verification-results', 'info', 
                '🔄 超时后恢复测试:\n\n' +
                '测试步骤:\n' +
                '1. 触发超时错误\n' +
                '2. 刷新页面或重新加载游戏\n' +
                '3. 验证系统是否能正常重新初始化\n' +
                '4. 测试用户创建功能是否恢复正常\n\n' +
                '💡 点击"加载正常游戏"可以测试恢复功能'
            );
        }

        function resetTest() {
            if (countdownInterval) {
                clearInterval(countdownInterval);
            }
            
            document.getElementById('timeout-countdown').style.display = 'none';
            document.getElementById('timeout-progress').style.display = 'none';
            document.getElementById('game-container').style.display = 'none';
            
            timeoutSimulation = null;
            gameFrame = null;
            
            updateResults('game-test-results', 'info', '🔄 测试已重置，可以开始新的测试');
            updateResults('scenario-results', 'info', '选择一个测试场景开始...');
            updateResults('user-creation-results', 'info', '填写用户信息后点击测试按钮...');
            updateResults('verification-results', 'info', '点击按钮开始验证...');
        }

        function updateResults(elementId, type, content) {
            const element = document.getElementById(elementId);
            element.className = `results ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        // 页面加载完成后显示说明
        document.addEventListener('DOMContentLoaded', () => {
            updateResults('game-test-results', 'info', 
                '⏰ 初始化超时测试工具\n\n' +
                '本工具专门用于测试和验证初始化超时的处理机制。\n\n' +
                '使用步骤:\n' +
                '1. 选择一个测试场景（推荐从场景1开始）\n' +
                '2. 观察超时过程和错误提示\n' +
                '3. 验证超时处理机制是否正常工作\n' +
                '4. 测试超时后的恢复能力\n\n' +
                '💡 每个场景都会模拟不同的超时情况'
            );
        });
    </script>
</body>
</html>
