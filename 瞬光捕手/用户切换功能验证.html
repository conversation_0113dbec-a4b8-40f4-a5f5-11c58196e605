<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户切换功能验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #16213e;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.danger {
            background: #f44336;
        }
        .test-button.warning {
            background: #ff9800;
        }
        .test-button.secondary {
            background: #2196F3;
        }
        .results {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .results.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .results.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .results.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .results.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #333;
            border-radius: 4px;
            background: #2a2a4e;
            color: white;
            font-size: 14px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-indicator.success {
            background: #4CAF50;
        }
        .status-indicator.error {
            background: #f44336;
        }
        .status-indicator.warning {
            background: #ff9800;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 用户切换功能验证工具</h1>
        
        <div class="section">
            <h2>📋 修复说明</h2>
            <div class="results info">
本次修复针对用户切换功能的错误：

🔍 问题根因：
1. window.gameEngine.updateSettings方法不存在
2. 用户切换过程中的错误处理不完善
3. 切换完成后界面显示未更新
4. 系统状态更新失败导致整个切换过程失败

🔧 修复内容：
1. 修复游戏引擎方法调用错误
   - 将updateSettings改为updateLevelConfig
   - 添加setAdaptiveDifficulty方法调用
   - 增强错误处理，不阻塞切换过程

2. 优化用户切换流程
   - 增强错误处理机制
   - 系统状态更新失败不影响切换完成
   - 添加切换结果验证和反馈

3. 完善界面更新机制
   - 切换完成后自动更新用户显示
   - 刷新用户列表显示当前用户
   - 通知其他组件用户已切换

✅ 预期效果：
- 用户切换功能正常工作
- 切换后界面正确显示新用户
- 即使部分功能失败也能完成切换
- 提供详细的状态反馈
            </div>
        </div>

        <div class="section">
            <h2>🎮 游戏环境测试</h2>
            <div class="test-controls">
                <button class="test-button" onclick="loadGame()">加载游戏</button>
                <button class="test-button secondary" onclick="checkGameEngine()">检查游戏引擎</button>
                <button class="test-button danger" onclick="resetTest()">重置测试</button>
            </div>
            
            <div class="iframe-container" id="game-container" style="display: none;">
                <iframe id="game-frame" src=""></iframe>
            </div>
            
            <div id="game-test-results" class="results info">
                点击"加载游戏"开始测试...
            </div>
        </div>

        <div class="section">
            <h2>👤 用户切换测试</h2>
            <div class="form-group">
                <label for="target-user">目标用户标识符:</label>
                <input type="text" id="target-user" placeholder="输入要切换到的用户标识符" value="123">
            </div>
            <div class="test-controls">
                <button class="test-button" onclick="testUserSwitch()">测试用户切换</button>
                <button class="test-button secondary" onclick="checkCurrentUser()">检查当前用户</button>
                <button class="test-button secondary" onclick="listAllUsers()">列出所有用户</button>
            </div>
            
            <div id="user-switch-results" class="results info">
                填写目标用户后点击测试按钮...
            </div>
        </div>

        <div class="section">
            <h2>🔧 系统状态检查</h2>
            <div class="test-controls">
                <button class="test-button" onclick="checkSystemComponents()">检查系统组件</button>
                <button class="test-button secondary" onclick="checkGameEngineStatus()">检查游戏引擎状态</button>
                <button class="test-button secondary" onclick="testGameEngineMethods()">测试游戏引擎方法</button>
            </div>
            
            <div id="system-check-results" class="results info">
                点击按钮开始检查...
            </div>
        </div>

        <div class="section">
            <h2>🧪 错误处理验证</h2>
            <div class="test-controls">
                <button class="test-button warning" onclick="simulateGameEngineError()">模拟游戏引擎错误</button>
                <button class="test-button warning" onclick="simulateUserNotFound()">模拟用户不存在</button>
                <button class="test-button warning" onclick="simulateSystemStateError()">模拟系统状态错误</button>
            </div>
            
            <div id="error-test-results" class="results info">
                点击按钮测试错误处理...
            </div>
        </div>
    </div>

    <script>
        let gameFrame = null;

        function loadGame() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            
            updateResults('game-test-results', 'info', '🔄 正在加载游戏...');
            
            frame.src = './index.html';
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                updateResults('game-test-results', 'success', '✅ 游戏已加载，等待初始化完成...');
                
                // 等待游戏初始化完成
                setTimeout(() => {
                    checkGameEngine();
                }, 5000);
            };

            frame.onerror = function(error) {
                updateResults('game-test-results', 'error', `❌ 游戏加载失败: ${error}`);
            };
        }

        function checkGameEngine() {
            if (!gameFrame) {
                updateResults('game-test-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 游戏引擎检查:\n\n';

                if (gameWindow.gameEngine) {
                    report += '游戏引擎状态:\n';
                    report += `  存在: ✅\n`;
                    report += `  构造函数: ${gameWindow.gameEngine.constructor.name}\n`;
                    
                    // 检查方法
                    const methods = [
                        'updateSettings', 'updateLevelConfig', 'setAdaptiveDifficulty',
                        'start', 'stop', 'reset', 'update'
                    ];
                    
                    report += '\n方法检查:\n';
                    methods.forEach(method => {
                        const exists = typeof gameWindow.gameEngine[method] === 'function';
                        report += `  ${method}: ${exists ? '✅' : '❌'}\n`;
                    });
                    
                    // 检查属性
                    report += '\n重要属性:\n';
                    report += `  isRunning: ${gameWindow.gameEngine.isRunning || false}\n`;
                    report += `  level: ${gameWindow.gameEngine.level || 0}\n`;
                    report += `  score: ${gameWindow.gameEngine.score || 0}\n`;
                    
                } else {
                    report += '❌ 游戏引擎不存在\n';
                }

                updateResults('game-test-results', 'info', report);

            } catch (error) {
                updateResults('game-test-results', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        async function testUserSwitch() {
            const targetUser = document.getElementById('target-user').value.trim();
            
            if (!targetUser) {
                updateResults('user-switch-results', 'error', '❌ 请输入目标用户标识符');
                return;
            }

            if (!gameFrame) {
                updateResults('user-switch-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = `🔄 用户切换测试:\n目标用户: ${targetUser}\n\n`;

                // 检查用户切换管理器
                if (!gameWindow.userSwitchManager) {
                    report += '❌ 用户切换管理器不存在\n';
                    updateResults('user-switch-results', 'error', report);
                    return;
                }

                report += '开始切换用户...\n';
                updateResults('user-switch-results', 'info', report);

                // 执行用户切换
                const switchResult = await gameWindow.userSwitchManager.switchUser(targetUser);
                
                report += `切换结果:\n`;
                report += `  成功: ${switchResult.success ? '✅' : '❌'}\n`;
                report += `  切换时间: ${switchResult.switchTime}ms\n`;
                
                if (switchResult.currentUser) {
                    report += `  当前用户: ${switchResult.currentUser.displayName} (${switchResult.currentUser.identifier})\n`;
                }
                
                if (switchResult.systemStateUpdated !== undefined) {
                    report += `  系统状态更新: ${switchResult.systemStateUpdated ? '✅' : '❌'}\n`;
                }
                
                if (switchResult.systemStateError) {
                    report += `  系统状态错误: ${switchResult.systemStateError}\n`;
                }

                const resultType = switchResult.success ? 'success' : 'error';
                updateResults('user-switch-results', resultType, report);

            } catch (error) {
                let report = `❌ 用户切换失败:\n`;
                report += `错误信息: ${error.message}\n`;
                if (error.stack) {
                    report += `错误堆栈:\n${error.stack}\n`;
                }
                updateResults('user-switch-results', 'error', report);
            }
        }

        function checkCurrentUser() {
            if (!gameFrame) {
                updateResults('user-switch-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '👤 当前用户检查:\n\n';

                // 检查用户管理器
                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.getUserManager) {
                    const userManager = gameWindow.userSystemAdapter.getUserManager();
                    const currentUser = userManager.getCurrentUser();
                    
                    if (currentUser) {
                        report += `当前用户信息:\n`;
                        report += `  标识符: ${currentUser.identifier}\n`;
                        report += `  显示名称: ${currentUser.displayName}\n`;
                        report += `  用户类型: ${currentUser.type}\n`;
                        report += `  创建时间: ${new Date(currentUser.createdAt).toLocaleString()}\n`;
                        report += `  最后活跃: ${new Date(currentUser.lastActiveAt).toLocaleString()}\n`;
                    } else {
                        report += '❌ 没有当前用户\n';
                    }
                } else {
                    report += '❌ 用户管理器不可用\n';
                }

                updateResults('user-switch-results', 'info', report);

            } catch (error) {
                updateResults('user-switch-results', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        function listAllUsers() {
            if (!gameFrame) {
                updateResults('user-switch-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '📋 所有用户列表:\n\n';

                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.getUserManager) {
                    const userManager = gameWindow.userSystemAdapter.getUserManager();
                    const users = userManager.users;
                    
                    if (users && users.size > 0) {
                        report += `用户总数: ${users.size}\n\n`;
                        
                        users.forEach((user, identifier) => {
                            const isCurrent = userManager.currentUser && userManager.currentUser.identifier === identifier;
                            report += `${isCurrent ? '👤 ' : '   '}${user.displayName} (${identifier})\n`;
                            report += `     类型: ${user.type}\n`;
                            report += `     创建: ${new Date(user.createdAt).toLocaleString()}\n\n`;
                        });
                    } else {
                        report += '❌ 没有找到用户\n';
                    }
                } else {
                    report += '❌ 用户管理器不可用\n';
                }

                updateResults('user-switch-results', 'info', report);

            } catch (error) {
                updateResults('user-switch-results', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        function checkSystemComponents() {
            if (!gameFrame) {
                updateResults('system-check-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔧 系统组件检查:\n\n';

                const components = [
                    'gameEngine', 'userSystemAdapter', 'userSwitchManager', 
                    'userManagementUI', 'screenManager', 'difficultyConfigManager'
                ];

                components.forEach(component => {
                    const exists = gameWindow[component] !== undefined;
                    report += `${component}: ${exists ? '✅' : '❌'}\n`;
                    
                    if (exists && gameWindow[component].initialized !== undefined) {
                        report += `  已初始化: ${gameWindow[component].initialized ? '✅' : '❌'}\n`;
                    }
                });

                updateResults('system-check-results', 'info', report);

            } catch (error) {
                updateResults('system-check-results', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        function checkGameEngineStatus() {
            if (!gameFrame) {
                updateResults('system-check-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🎮 游戏引擎详细状态:\n\n';

                if (gameWindow.gameEngine) {
                    const engine = gameWindow.gameEngine;
                    
                    report += '基本状态:\n';
                    report += `  运行中: ${engine.isRunning || false}\n`;
                    report += `  暂停: ${engine.isPaused || false}\n`;
                    report += `  关卡: ${engine.level || 0}\n`;
                    report += `  分数: ${engine.score || 0}\n`;
                    
                    report += '\n配置信息:\n';
                    if (engine.levelConfig) {
                        report += `  光点生成间隔: ${engine.levelConfig.sparkSpawnRate}ms\n`;
                        report += `  光点速度: ${engine.levelConfig.sparkSpeed}\n`;
                        report += `  光点数量: ${engine.levelConfig.sparkCount}\n`;
                        report += `  完美窗口: ${engine.levelConfig.perfectWindow}ms\n`;
                        report += `  良好窗口: ${engine.levelConfig.goodWindow}ms\n`;
                    }
                    
                    report += '\n动态难度:\n';
                    if (engine.adaptiveDifficulty) {
                        report += `  启用: ${engine.adaptiveDifficulty.enabled}\n`;
                        report += `  平均准确度: ${engine.adaptiveDifficulty.playerPerformance.averageAccuracy.toFixed(2)}\n`;
                    }
                    
                } else {
                    report += '❌ 游戏引擎不存在\n';
                }

                updateResults('system-check-results', 'info', report);

            } catch (error) {
                updateResults('system-check-results', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        function testGameEngineMethods() {
            if (!gameFrame) {
                updateResults('system-check-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🧪 游戏引擎方法测试:\n\n';

                if (gameWindow.gameEngine) {
                    const engine = gameWindow.gameEngine;
                    
                    // 测试updateLevelConfig方法
                    if (typeof engine.updateLevelConfig === 'function') {
                        try {
                            engine.updateLevelConfig();
                            report += 'updateLevelConfig: ✅ 调用成功\n';
                        } catch (error) {
                            report += `updateLevelConfig: ❌ 调用失败 - ${error.message}\n`;
                        }
                    } else {
                        report += 'updateLevelConfig: ❌ 方法不存在\n';
                    }
                    
                    // 测试setAdaptiveDifficulty方法
                    if (typeof engine.setAdaptiveDifficulty === 'function') {
                        try {
                            engine.setAdaptiveDifficulty(true);
                            report += 'setAdaptiveDifficulty: ✅ 调用成功\n';
                        } catch (error) {
                            report += `setAdaptiveDifficulty: ❌ 调用失败 - ${error.message}\n`;
                        }
                    } else {
                        report += 'setAdaptiveDifficulty: ❌ 方法不存在\n';
                    }
                    
                    // 测试已废弃的updateSettings方法
                    if (typeof engine.updateSettings === 'function') {
                        report += 'updateSettings: ⚠️ 方法存在（应该已废弃）\n';
                    } else {
                        report += 'updateSettings: ✅ 方法不存在（符合预期）\n';
                    }
                    
                } else {
                    report += '❌ 游戏引擎不存在\n';
                }

                updateResults('system-check-results', 'info', report);

            } catch (error) {
                updateResults('system-check-results', 'error', `❌ 测试失败: ${error.message}`);
            }
        }

        function simulateGameEngineError() {
            updateResults('error-test-results', 'warning', 
                '⚠️ 游戏引擎错误模拟:\n\n' +
                '这个测试将模拟游戏引擎方法调用失败的情况，\n' +
                '验证用户切换过程是否能正确处理这种错误。\n\n' +
                '预期结果:\n' +
                '- 用户切换仍然成功\n' +
                '- 显示系统状态更新失败的警告\n' +
                '- 不影响用户切换的核心功能'
            );
        }

        function simulateUserNotFound() {
            updateResults('error-test-results', 'warning', 
                '⚠️ 用户不存在错误模拟:\n\n' +
                '这个测试将尝试切换到不存在的用户，\n' +
                '验证错误处理是否正确。\n\n' +
                '测试步骤:\n' +
                '1. 在目标用户输入框中输入不存在的用户ID\n' +
                '2. 点击"测试用户切换"\n' +
                '3. 观察错误处理结果'
            );
        }

        function simulateSystemStateError() {
            updateResults('error-test-results', 'warning', 
                '⚠️ 系统状态错误模拟:\n\n' +
                '这个测试将模拟系统状态更新过程中的错误，\n' +
                '验证错误隔离机制是否正常工作。\n\n' +
                '预期结果:\n' +
                '- 用户切换核心功能正常\n' +
                '- 系统状态更新失败被正确处理\n' +
                '- 用户界面正确更新'
            );
        }

        function resetTest() {
            document.getElementById('game-container').style.display = 'none';
            gameFrame = null;
            
            updateResults('game-test-results', 'info', '🔄 测试已重置');
            updateResults('user-switch-results', 'info', '填写目标用户后点击测试按钮...');
            updateResults('system-check-results', 'info', '点击按钮开始检查...');
            updateResults('error-test-results', 'info', '点击按钮测试错误处理...');
        }

        function updateResults(elementId, type, content) {
            const element = document.getElementById(elementId);
            element.className = `results ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        // 页面加载完成后显示说明
        document.addEventListener('DOMContentLoaded', () => {
            updateResults('game-test-results', 'info', 
                '🔄 用户切换功能验证工具\n\n' +
                '本工具专门用于验证用户切换功能的修复效果。\n\n' +
                '使用步骤:\n' +
                '1. 点击"加载游戏"加载游戏环境\n' +
                '2. 使用"检查游戏引擎"验证修复效果\n' +
                '3. 在用户切换测试中输入目标用户ID\n' +
                '4. 执行用户切换测试验证功能\n\n' +
                '如果修复成功，用户切换应该正常工作。'
            );
        });
    </script>
</body>
</html>
