/**
 * 瞬光捕手 - 安全认证对话框样式
 */

.secure-auth-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.secure-auth-modal {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border-radius: 16px;
    padding: 32px;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.auth-header {
    text-align: center;
    margin-bottom: 32px;
}

.auth-header h2 {
    color: #ffffff;
    font-size: 24px;
    margin: 0 0 8px 0;
    font-weight: 600;
}

.auth-header p {
    color: #b0b0b0;
    font-size: 14px;
    margin: 0;
    line-height: 1.5;
}

.auth-options {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin-bottom: 24px;
}

.auth-option {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.auth-option:hover {
    background: rgba(255, 255, 255, 0.08);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.auth-option h3 {
    color: #ffffff;
    font-size: 16px;
    margin: 0 0 8px 0;
    font-weight: 500;
}

.auth-option p {
    color: #b0b0b0;
    font-size: 13px;
    margin: 0 0 16px 0;
    line-height: 1.4;
}

.auth-btn {
    width: 100%;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: none;
}

.guest-btn {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    color: white;
}

.guest-btn:hover {
    background: linear-gradient(135deg, #357abd 0%, #2968a3 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
}

.username-btn {
    background: linear-gradient(135deg, #50c878 0%, #3da35d 100%);
    color: white;
}

.username-btn:hover {
    background: linear-gradient(135deg, #3da35d 0%, #2d8a47 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(80, 200, 120, 0.3);
}

.device-btn {
    background: linear-gradient(135deg, #ff6b6b 0%, #e55555 100%);
    color: white;
}

.device-btn:hover {
    background: linear-gradient(135deg, #e55555 0%, #cc4444 100%);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
}

.cancel-btn {
    background: rgba(255, 255, 255, 0.1);
    color: #b0b0b0;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.cancel-btn:hover {
    background: rgba(255, 255, 255, 0.15);
    color: #ffffff;
    border-color: rgba(255, 255, 255, 0.3);
}

#username-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.05);
    color: #ffffff;
    font-size: 14px;
    margin-bottom: 12px;
    transition: all 0.3s ease;
}

#username-input:focus {
    outline: none;
    border-color: #4a90e2;
    background: rgba(255, 255, 255, 0.08);
    box-shadow: 0 0 0 3px rgba(74, 144, 226, 0.2);
}

#username-input::placeholder {
    color: #666;
}

.auth-footer {
    text-align: center;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

.privacy-notice {
    margin-bottom: 16px;
}

.privacy-notice p {
    color: #888;
    font-size: 12px;
    margin: 0;
    line-height: 1.4;
}

.auth-error {
    background: rgba(255, 107, 107, 0.1);
    border: 1px solid rgba(255, 107, 107, 0.3);
    border-radius: 8px;
    padding: 12px 16px;
    margin-bottom: 16px;
    color: #ff6b6b;
    font-size: 13px;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 600px) {
    .secure-auth-modal {
        padding: 24px;
        margin: 20px;
        max-width: none;
        width: calc(100% - 40px);
    }
    
    .auth-header h2 {
        font-size: 20px;
    }
    
    .auth-option {
        padding: 16px;
    }
    
    .auth-btn {
        padding: 14px 20px;
        font-size: 16px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.secure-auth-modal {
    animation: fadeIn 0.3s ease-out;
}

/* 深色主题适配 */
@media (prefers-color-scheme: dark) {
    .secure-auth-modal {
        background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .secure-auth-modal {
        border: 2px solid #ffffff;
    }
    
    .auth-option {
        border: 1px solid #ffffff;
    }
    
    .auth-btn {
        border: 1px solid currentColor;
    }
}

/* 减少动画模式 */
@media (prefers-reduced-motion: reduce) {
    .secure-auth-modal {
        animation: none;
    }
    
    .auth-option,
    .auth-btn {
        transition: none;
    }
    
    .auth-option:hover,
    .auth-btn:hover {
        transform: none;
    }
}
