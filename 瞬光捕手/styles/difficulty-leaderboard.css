/**
 * 瞬光捕手 - 难度分类排行榜UI样式
 * 现代化的排行榜界面设计
 */

/* 覆盖层样式 */
.difficulty-leaderboard-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.85);
    backdrop-filter: blur(12px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.difficulty-leaderboard-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 模态框样式 */
.difficulty-leaderboard-modal {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 20px;
    box-shadow: 0 30px 100px rgba(0, 0, 0, 0.5);
    width: 95%;
    max-width: 1400px;
    max-height: 95vh;
    overflow: hidden;
    transform: scale(0.9) translateY(30px);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.difficulty-leaderboard-overlay.show .difficulty-leaderboard-modal {
    transform: scale(1) translateY(0);
}

/* 模态框头部 */
.modal-header {
    background: rgba(255, 255, 255, 0.08);
    padding: 25px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.modal-header h2 {
    color: white;
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.header-controls {
    display: flex;
    gap: 10px;
    align-items: center;
}

.btn-icon {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1.2rem;
}

.btn-icon:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

.close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* 模态框内容 */
.modal-content {
    padding: 30px;
    color: white;
    overflow-y: auto;
    max-height: calc(95vh - 100px);
}

/* 难度选择器 */
.difficulty-selector {
    margin-bottom: 30px;
}

.difficulty-selector h3 {
    color: white;
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.difficulty-tabs {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
}

.difficulty-tab {
    background: rgba(255, 255, 255, 0.1);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 120px;
    backdrop-filter: blur(10px);
}

.difficulty-tab:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.2);
}

.difficulty-tab.active {
    background: var(--difficulty-color, #FFC107);
    border-color: var(--difficulty-color, #FFC107);
    color: white;
    box-shadow: 0 5px 25px rgba(255, 193, 7, 0.3);
}

.difficulty-tab .tab-icon {
    font-size: 1.5rem;
}

.difficulty-tab .tab-name {
    font-weight: 600;
    font-size: 1rem;
}

/* 类别选择器 */
.category-selector {
    margin-bottom: 30px;
}

.category-selector h3 {
    color: white;
    margin: 0 0 15px 0;
    font-size: 1.2rem;
    font-weight: 600;
}

.category-tabs {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.category-tab {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 12px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 15px;
    backdrop-filter: blur(10px);
}

.category-tab:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

.category-tab.active {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 5px 20px rgba(255, 255, 255, 0.1);
}

.category-tab .tab-icon {
    font-size: 1.8rem;
    min-width: 40px;
    text-align: center;
}

.category-tab .tab-info {
    flex: 1;
}

.category-tab .tab-name {
    font-weight: 600;
    font-size: 1.1rem;
    margin-bottom: 5px;
}

.category-tab .tab-desc {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
    line-height: 1.3;
}

/* 排行榜内容 */
.leaderboard-content {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.leaderboard-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 25px;
    flex-wrap: wrap;
    gap: 15px;
}

.current-selection {
    display: flex;
    align-items: center;
    gap: 15px;
}

.difficulty-badge {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 15px;
    border-radius: 20px;
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

.category-name {
    display: flex;
    align-items: center;
    gap: 8px;
    color: rgba(255, 255, 255, 0.9);
    font-size: 1.1rem;
    font-weight: 500;
}

.leaderboard-actions {
    display: flex;
    gap: 10px;
}

.btn-small {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    padding: 8px 15px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 0.9rem;
}

.btn-small:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* 排行榜列表 */
.leaderboard-list {
    margin-bottom: 30px;
}

.leaderboard-entry {
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 10px;
    display: grid;
    grid-template-columns: auto 1fr auto auto auto;
    gap: 20px;
    align-items: center;
    transition: all 0.2s ease;
}

.leaderboard-entry:hover {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateX(5px);
}

.leaderboard-entry.top-three {
    background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 193, 7, 0.05) 100%);
    border-color: rgba(255, 215, 0, 0.3);
}

.entry-rank {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    min-width: 60px;
}

.rank-number {
    font-size: 1.5rem;
    font-weight: 700;
    color: white;
}

.rank-medal {
    font-size: 1.2rem;
}

.entry-player {
    min-width: 200px;
}

.player-name {
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    margin-bottom: 5px;
}

.player-details {
    display: flex;
    gap: 15px;
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.7);
}

.difficulty-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: 500;
}

.entry-score {
    text-align: right;
    min-width: 120px;
}

.score-value {
    font-size: 1.3rem;
    font-weight: 700;
    color: #FFC107;
    margin-bottom: 3px;
}

.weighted-score {
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.6);
}

.entry-stats {
    display: flex;
    flex-direction: column;
    gap: 3px;
    min-width: 100px;
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.7);
}

.entry-time {
    font-size: 0.85rem;
    color: rgba(255, 255, 255, 0.6);
    text-align: right;
    min-width: 80px;
}

/* 状态显示 */
.loading-state,
.empty-state,
.error-state {
    text-align: center;
    padding: 60px 20px;
    color: rgba(255, 255, 255, 0.8);
}

.loading-state .spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.2);
    border-top: 4px solid #FFC107;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state .empty-icon,
.error-state .error-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.empty-state h3,
.error-state h3 {
    color: white;
    margin: 0 0 15px 0;
    font-size: 1.3rem;
}

.empty-state p,
.error-state p {
    margin: 0 0 10px 0;
    line-height: 1.5;
}

/* 玩家排名信息 */
.player-rank-info {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.player-rank-info h4 {
    color: white;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.player-rank-card {
    display: grid;
    grid-template-columns: auto 1fr auto;
    gap: 20px;
    align-items: center;
}

.rank-info {
    text-align: center;
}

.rank-position {
    display: flex;
    align-items: baseline;
    gap: 5px;
    justify-content: center;
    margin-bottom: 5px;
}

.rank-number {
    font-size: 2rem;
    font-weight: 700;
    color: #FFC107;
}

.rank-total {
    font-size: 1rem;
    color: rgba(255, 255, 255, 0.7);
}

.rank-difficulty {
    font-size: 0.9rem;
    font-weight: 500;
}

.player-stats {
    display: flex;
    gap: 20px;
}

.stat-item {
    text-align: center;
}

.stat-label {
    display: block;
    font-size: 0.8rem;
    color: rgba(255, 255, 255, 0.7);
    margin-bottom: 3px;
}

.stat-value {
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
}

.rank-actions {
    text-align: right;
}

.no-rank-card {
    text-align: center;
    padding: 30px;
}

.no-rank-icon {
    font-size: 3rem;
    margin-bottom: 15px;
}

.no-rank-text {
    font-size: 1.1rem;
    color: white;
    margin-bottom: 8px;
}

.no-rank-hint {
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9rem;
}

/* 按钮样式 */
.btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.2s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-primary {
    background: linear-gradient(135deg, #FFC107 0%, #FF9800 100%);
}

.btn-primary:hover {
    box-shadow: 0 5px 15px rgba(255, 193, 7, 0.3);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .difficulty-leaderboard-modal {
        width: 98%;
        margin: 10px;
    }
    
    .modal-content {
        padding: 20px;
    }
    
    .category-tabs {
        grid-template-columns: 1fr;
    }
    
    .leaderboard-entry {
        grid-template-columns: auto 1fr;
        gap: 15px;
    }
    
    .entry-stats,
    .entry-time {
        grid-column: 1 / -1;
        margin-top: 10px;
        display: flex;
        justify-content: space-between;
    }
}

@media (max-width: 768px) {
    .modal-header {
        padding: 20px;
        flex-direction: column;
        gap: 15px;
    }
    
    .difficulty-tabs {
        justify-content: center;
    }
    
    .difficulty-tab {
        min-width: 100px;
        padding: 12px 15px;
    }
    
    .leaderboard-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .current-selection {
        justify-content: center;
    }
    
    .leaderboard-actions {
        justify-content: center;
    }
    
    .player-rank-card {
        grid-template-columns: 1fr;
        text-align: center;
        gap: 15px;
    }
    
    .player-stats {
        justify-content: center;
    }
}

@media (max-width: 480px) {
    .modal-header h2 {
        font-size: 1.5rem;
    }
    
    .difficulty-tab {
        flex-direction: column;
        gap: 5px;
        padding: 10px;
    }
    
    .category-tab {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }
    
    .leaderboard-entry {
        padding: 15px;
        grid-template-columns: 1fr;
        text-align: center;
    }
    
    .entry-rank {
        flex-direction: row;
        justify-content: center;
    }
}
