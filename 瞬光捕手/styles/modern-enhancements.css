/**
 * 瞬光捕手 - 现代化增强样式
 * 专门用于提升游戏的现代化视觉效果和交互体验
 */

/* ===== 高级动画系统 ===== */

/* 页面加载动画 */
@keyframes pageLoad {
    0% {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
        filter: blur(10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

.screen {
    animation: pageLoad 0.8s var(--ease-out-quart) forwards;
}

/* 高级按钮悬浮效果 */
.menu-btn {
    position: relative;
    overflow: hidden;
}

.menu-btn::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    transition: all 0.6s ease;
    transform: translate(-50%, -50%);
    border-radius: 50%;
}

.menu-btn:hover::after {
    width: 300px;
    height: 300px;
}

/* 脉冲效果 */
.pulse-effect {
    animation: pulseGlow 2s ease-in-out infinite;
}

@keyframes pulseGlow {
    0%, 100% {
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
    }
    50% {
        box-shadow: 0 0 40px rgba(0, 212, 255, 0.6);
    }
}

/* ===== 游戏特效增强 ===== */

/* 光点击中特效 */
.spark-hit-effect {
    position: absolute;
    pointer-events: none;
    z-index: 1000;
}

.spark-hit-effect::before {
    content: '';
    position: absolute;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, var(--neon-blue) 0%, transparent 70%);
    border-radius: 50%;
    animation: sparkHitExplosion 0.6s ease-out forwards;
}

@keyframes sparkHitExplosion {
    0% {
        transform: scale(0) rotate(0deg);
        opacity: 1;
    }
    100% {
        transform: scale(3) rotate(180deg);
        opacity: 0;
    }
}

/* 连击效果 */
.combo-effect {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 2rem;
    font-weight: 800;
    color: var(--neon-yellow);
    text-shadow: 0 0 20px var(--neon-yellow);
    animation: comboPopup 1s ease-out forwards;
    pointer-events: none;
    z-index: 1001;
}

@keyframes comboPopup {
    0% {
        transform: translate(-50%, -50%) scale(0) rotate(-10deg);
        opacity: 0;
    }
    50% {
        transform: translate(-50%, -50%) scale(1.2) rotate(5deg);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1) rotate(0deg);
        opacity: 0;
    }
}

/* ===== 粒子系统增强 ===== */

/* 背景粒子动画 */
.particle-system {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: -1;
}

.floating-particle {
    position: absolute;
    width: 4px;
    height: 4px;
    background: var(--neon-blue);
    border-radius: 50%;
    opacity: 0.6;
    animation: floatUp 8s linear infinite;
}

.floating-particle:nth-child(2n) {
    background: var(--neon-purple);
    animation-duration: 10s;
    animation-delay: 2s;
}

.floating-particle:nth-child(3n) {
    background: var(--neon-pink);
    animation-duration: 12s;
    animation-delay: 4s;
}

@keyframes floatUp {
    0% {
        transform: translateY(100vh) scale(0);
        opacity: 0;
    }
    10% {
        opacity: 0.6;
        transform: translateY(90vh) scale(1);
    }
    90% {
        opacity: 0.6;
        transform: translateY(10vh) scale(1);
    }
    100% {
        transform: translateY(-10vh) scale(0);
        opacity: 0;
    }
}

/* ===== 高级UI组件 ===== */

/* 现代化进度条 */
.modern-progress {
    width: 100%;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.modern-progress::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple));
    border-radius: 4px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.modern-progress.animated::before {
    animation: progressShimmer 2s ease-in-out infinite;
}

@keyframes progressShimmer {
    0%, 100% {
        box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
    }
    50% {
        box-shadow: 0 0 20px rgba(0, 212, 255, 0.8);
    }
}

/* 现代化开关 */
.modern-toggle {
    position: relative;
    width: 60px;
    height: 30px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.modern-toggle::before {
    content: '';
    position: absolute;
    top: 3px;
    left: 3px;
    width: 24px;
    height: 24px;
    background: white;
    border-radius: 50%;
    transition: all 0.3s var(--ease-out-quart);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.modern-toggle.active {
    background: linear-gradient(90deg, var(--neon-blue), var(--neon-purple));
    border-color: var(--neon-blue);
}

.modern-toggle.active::before {
    transform: translateX(30px);
    box-shadow: 0 2px 8px rgba(0, 212, 255, 0.4);
}

/* ===== 响应式增强 ===== */

/* 移动端触摸反馈增强 */
@media (hover: none) and (pointer: coarse) {
    .menu-btn:active {
        transform: scale(0.95);
        transition: transform 0.1s ease;
    }
    
    .menu-btn:active::after {
        width: 200px;
        height: 200px;
        transition: all 0.2s ease;
    }
}

/* 高刷新率屏幕优化 */
@media (min-resolution: 120dpi) {
    * {
        animation-duration: calc(var(--animation-duration, 1s) * 0.8);
    }
    
    .menu-btn {
        transition-duration: 0.2s;
    }
}
