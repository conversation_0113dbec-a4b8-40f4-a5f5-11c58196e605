/**
 * 瞬光捕手 - 难度选择器UI样式
 * 现代化的难度选择界面设计
 */

/* 覆盖层样式 */
.difficulty-selector-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    backdrop-filter: blur(15px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.difficulty-selector-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 模态框样式 */
.difficulty-selector-modal {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 20px;
    box-shadow: 0 25px 80px rgba(0, 0, 0, 0.5);
    width: 95%;
    max-width: 1200px;
    max-height: 95vh;
    overflow: hidden;
    transform: scale(0.8) translateY(50px);
    transition: all 0.3s ease;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.difficulty-selector-overlay.show .difficulty-selector-modal {
    transform: scale(1) translateY(0);
}

/* 模态框头部 */
.modal-header {
    background: rgba(255, 255, 255, 0.05);
    padding: 25px 30px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.modal-header h2 {
    color: white;
    margin: 0;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
}

.close-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 8px;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: scale(1.1);
}

/* 模态框内容 */
.modal-content {
    padding: 30px;
    color: white;
    overflow-y: auto;
    max-height: calc(95vh - 100px);
}

.difficulty-description {
    text-align: center;
    margin-bottom: 30px;
}

.difficulty-description p {
    color: rgba(255, 255, 255, 0.8);
    font-size: 1.1rem;
    line-height: 1.6;
    margin: 0;
}

/* 难度网格 */
.difficulty-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 20px;
    margin-bottom: 40px;
}

/* 难度卡片 */
.difficulty-card {
    position: relative;
    border-radius: 15px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s ease;
    transform: translateY(0);
    border: 2px solid transparent;
}

.difficulty-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
}

.difficulty-card.selected {
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.4);
}

.card-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.9;
    transition: opacity 0.3s ease;
}

.difficulty-card:hover .card-background {
    opacity: 1;
}

.card-content {
    position: relative;
    padding: 25px;
    background: rgba(0, 0, 0, 0.2);
    backdrop-filter: blur(10px);
    height: 100%;
    display: flex;
    flex-direction: column;
    min-height: 180px;
}

.difficulty-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    text-align: center;
    filter: drop-shadow(0 2px 10px rgba(0, 0, 0, 0.3));
}

.difficulty-info {
    flex: 1;
}

.difficulty-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0 0 10px 0;
    color: white;
    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.5);
}

.difficulty-desc {
    color: rgba(255, 255, 255, 0.9);
    font-size: 1rem;
    line-height: 1.5;
    margin: 0 0 15px 0;
}

.difficulty-multiplier {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
}

.multiplier-value {
    font-weight: 700;
    color: white;
    font-size: 1.1rem;
}

.recommended-badge {
    position: absolute;
    top: 15px;
    right: 15px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    padding: 5px 12px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
}

.card-glow {
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border-radius: 17px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
}

.difficulty-card:hover .card-glow,
.difficulty-card.selected .card-glow {
    opacity: 0.6;
}

.selection-indicator {
    position: absolute;
    top: 15px;
    left: 15px;
    width: 20px;
    height: 20px;
    border: 2px solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.difficulty-card.selected .selection-indicator {
    border-color: white;
    background: white;
}

.indicator-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: transparent;
    transition: background 0.3s ease;
}

.difficulty-card.selected .indicator-dot {
    background: #1a1a2e;
}

/* 当前难度信息 */
.current-difficulty-info {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 15px;
    padding: 25px;
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.info-header h3 {
    color: white;
    margin: 0 0 20px 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.difficulty-stats {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.stat-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.stat-label {
    color: rgba(255, 255, 255, 0.8);
    font-weight: 500;
    flex: 1;
}

.stat-value {
    color: white;
    font-weight: 600;
    font-size: 1.1rem;
}

.stat-bar {
    position: relative;
    flex: 2;
    height: 8px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
    margin: 0 15px;
    overflow: hidden;
}

.stat-fill {
    height: 100%;
    border-radius: 4px;
    transition: width 0.5s ease;
    position: relative;
}

.stat-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.stat-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin-top: 20px;
}

.stat-grid .stat-item {
    background: rgba(255, 255, 255, 0.05);
    padding: 15px;
    border-radius: 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
}

.stat-grid .stat-label {
    font-size: 0.9rem;
}

.stat-grid .stat-value {
    font-size: 1.2rem;
    color: #FFC107;
}

/* 难度特性 */
.difficulty-features {
    margin-top: 25px;
}

.difficulty-features h4 {
    color: white;
    margin: 0 0 15px 0;
    font-size: 1.1rem;
    font-weight: 600;
}

.features-list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.feature-tag {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 0.85rem;
    font-weight: 500;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.feature-tag.positive {
    background: rgba(76, 175, 80, 0.2);
    border-color: rgba(76, 175, 80, 0.5);
    color: #4CAF50;
}

.feature-tag.negative {
    background: rgba(244, 67, 54, 0.2);
    border-color: rgba(244, 67, 54, 0.5);
    color: #F44336;
}

/* 模态框操作按钮 */
.modal-actions {
    display: flex;
    gap: 20px;
    justify-content: center;
    padding-top: 20px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.btn {
    padding: 15px 30px;
    border: none;
    border-radius: 10px;
    font-size: 1.1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.btn:hover::before {
    left: 100%;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    box-shadow: 0 5px 20px rgba(102, 126, 234, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 30px rgba(102, 126, 234, 0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
}

/* 成功消息 */
.success-message {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(76, 175, 80, 0.9);
    color: white;
    padding: 30px;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(76, 175, 80, 0.5);
    opacity: 0;
    scale: 0.8;
    transition: all 0.3s ease;
    z-index: 10;
}

.success-message.show {
    opacity: 1;
    scale: 1;
}

.success-icon {
    font-size: 3rem;
    margin-bottom: 15px;
    filter: drop-shadow(0 2px 10px rgba(0, 0, 0, 0.3));
}

.success-text {
    font-size: 1.2rem;
    font-weight: 600;
}

/* 错误提示 */
.error-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(244, 67, 54, 0.9);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(244, 67, 54, 0.3);
    z-index: 10001;
    animation: slideInRight 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(244, 67, 54, 0.5);
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 粒子效果 */
.particles-container {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    overflow: hidden;
}

.particle {
    position: absolute;
    border-radius: 50%;
    pointer-events: none;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

/* 键盘提示 */
.keyboard-hints {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 15px;
    color: rgba(255, 255, 255, 0.6);
    font-size: 0.9rem;
}

.keyboard-hint {
    display: flex;
    align-items: center;
    gap: 5px;
}

.key {
    background: rgba(255, 255, 255, 0.1);
    padding: 3px 8px;
    border-radius: 5px;
    font-family: monospace;
    font-weight: 600;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .difficulty-grid {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 15px;
    }

    .difficulty-card {
        min-height: 160px;
    }

    .card-content {
        padding: 20px;
    }

    .difficulty-icon {
        font-size: 2.5rem;
    }
}

@media (max-width: 768px) {
    .difficulty-selector-modal {
        width: 98%;
        margin: 10px;
    }

    .modal-content {
        padding: 20px;
    }

    .difficulty-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .difficulty-card {
        min-height: 140px;
    }

    .card-content {
        padding: 18px;
    }

    .difficulty-name {
        font-size: 1.3rem;
    }

    .difficulty-icon {
        font-size: 2.2rem;
    }

    .stat-grid {
        grid-template-columns: 1fr;
    }

    .modal-actions {
        flex-direction: column;
        gap: 15px;
    }

    .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .modal-header {
        padding: 20px;
    }

    .modal-header h2 {
        font-size: 1.5rem;
    }

    .modal-content {
        padding: 15px;
    }

    .difficulty-card {
        min-height: 120px;
    }

    .card-content {
        padding: 15px;
    }

    .difficulty-name {
        font-size: 1.2rem;
    }

    .difficulty-icon {
        font-size: 2rem;
        margin-bottom: 10px;
    }

    .current-difficulty-info {
        padding: 20px;
    }

    .recommended-badge {
        top: 10px;
        right: 10px;
        padding: 3px 8px;
        font-size: 0.75rem;
    }

    .selection-indicator {
        top: 10px;
        left: 10px;
        width: 16px;
        height: 16px;
    }

    .indicator-dot {
        width: 6px;
        height: 6px;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    .difficulty-selector-modal {
        border: 2px solid white;
    }

    .difficulty-card {
        border: 2px solid rgba(255, 255, 255, 0.5);
    }

    .difficulty-card.selected {
        border-color: white;
        border-width: 3px;
    }

    .feature-tag {
        border-width: 2px;
    }
}

/* 减少动画模式支持 */
@media (prefers-reduced-motion: reduce) {
    .difficulty-selector-overlay,
    .difficulty-selector-modal,
    .difficulty-card,
    .btn,
    .particle {
        transition: none;
        animation: none;
    }

    .difficulty-card:hover {
        transform: none;
    }

    .btn:hover {
        transform: none;
    }
}

/* 深色模式优化 */
@media (prefers-color-scheme: dark) {
    .difficulty-selector-modal {
        background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 50%, #2a2a2a 100%);
    }

    .modal-header {
        background: rgba(255, 255, 255, 0.03);
    }

    .current-difficulty-info {
        background: rgba(255, 255, 255, 0.03);
    }
}
