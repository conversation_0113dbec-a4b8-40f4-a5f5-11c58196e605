/**
 * 瞬光捕手 - 用户凭证管理UI样式
 * 现代化的登录界面设计
 */

/* 覆盖层样式 */
.credential-manager-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.credential-manager-overlay.show {
    opacity: 1;
    visibility: visible;
}

/* 模态框样式 */
.credential-manager-modal {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9) translateY(20px);
    transition: all 0.3s ease;
    position: relative;
}

.credential-manager-overlay.show .credential-manager-modal {
    transform: scale(1) translateY(0);
}

/* 模态框头部 */
.modal-header {
    background: rgba(255, 255, 255, 0.1);
    padding: 20px 25px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.modal-header h2 {
    color: white;
    margin: 0;
    font-size: 1.5rem;
    font-weight: 600;
}

.close-btn {
    background: none;
    border: none;
    color: white;
    font-size: 1.5rem;
    cursor: pointer;
    padding: 5px;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s ease;
}

.close-btn:hover {
    background: rgba(255, 255, 255, 0.2);
}

/* 模态框内容 */
.modal-content {
    padding: 25px;
    background: white;
    min-height: 300px;
    position: relative;
}

/* 步骤内容 */
.step-content {
    display: none;
    animation: fadeIn 0.3s ease;
}

.step-content.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.step-content h3 {
    color: #333;
    margin: 0 0 10px 0;
    font-size: 1.3rem;
    font-weight: 600;
}

.step-description {
    color: #666;
    margin-bottom: 25px;
    line-height: 1.5;
}

/* 凭证类型选择 */
.credential-types {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin-bottom: 25px;
}

.credential-type {
    display: flex;
    align-items: center;
    padding: 20px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    position: relative;
    overflow: hidden;
}

.credential-type:hover {
    border-color: #667eea;
    background: #f8f9ff;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.credential-type.recommended {
    border-color: #667eea;
    background: linear-gradient(135deg, #f8f9ff 0%, #e8ecff 100%);
}

.credential-type.recommended::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.type-icon {
    font-size: 2rem;
    margin-right: 15px;
    min-width: 50px;
    text-align: center;
}

.type-info {
    flex: 1;
}

.type-info h4 {
    margin: 0 0 5px 0;
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
}

.type-info p {
    margin: 0;
    color: #666;
    font-size: 0.9rem;
    line-height: 1.4;
}

.recommended-badge {
    display: inline-block;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    font-weight: 600;
    margin-top: 5px;
}

.type-arrow {
    color: #999;
    font-size: 1.2rem;
    margin-left: 10px;
    transition: transform 0.2s ease;
}

.credential-type:hover .type-arrow {
    transform: translateX(5px);
    color: #667eea;
}

/* 表单样式 */
.form-header {
    margin-bottom: 25px;
}

.form-header h3 {
    color: #333;
    margin: 0 0 10px 0;
    font-size: 1.3rem;
}

.form-header p {
    color: #666;
    margin: 0;
    line-height: 1.5;
}

.credential-form {
    margin-bottom: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
    font-size: 0.9rem;
}

.form-group input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 1rem;
    transition: all 0.2s ease;
    box-sizing: border-box;
}

.form-group input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-group input.error {
    border-color: #e74c3c;
    box-shadow: 0 0 0 3px rgba(231, 76, 60, 0.1);
}

.form-hint {
    margin-top: 5px;
    font-size: 0.8rem;
    color: #999;
    line-height: 1.4;
}

.form-error {
    margin-top: 5px;
    font-size: 0.8rem;
    color: #e74c3c;
    font-weight: 500;
}

/* 用户名预览 */
.preview-section {
    margin-top: 20px;
    padding: 15px;
    background: #f8f9ff;
    border-radius: 8px;
    border: 1px solid #e8ecff;
}

.preview-section label {
    margin-bottom: 10px;
    color: #666;
    font-size: 0.9rem;
}

.username-preview {
    display: flex;
    align-items: center;
    font-family: 'Courier New', monospace;
    font-size: 1.1rem;
    font-weight: 600;
}

.preview-username {
    color: #667eea;
}

.preview-separator {
    color: #999;
    margin: 0 3px;
}

.preview-suffix {
    color: #764ba2;
}

/* 设备信息 */
.device-info {
    background: #f8f9ff;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    color: #666;
    font-weight: 500;
}

.info-value {
    color: #333;
    font-weight: 600;
}

/* 警告框 */
.warning-box {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 验证表单 */
.verify-header {
    margin-bottom: 25px;
}

.resend-section {
    text-align: center;
    margin-top: 15px;
}

.btn-link {
    background: none;
    border: none;
    color: #667eea;
    cursor: pointer;
    font-size: 0.9rem;
    text-decoration: underline;
    padding: 5px;
}

.btn-link:hover {
    color: #764ba2;
}

/* 成功消息 */
.success-message {
    text-align: center;
    padding: 20px 0;
}

.success-icon {
    font-size: 4rem;
    margin-bottom: 20px;
}

.success-message h3 {
    color: #27ae60;
    margin: 0 0 15px 0;
    font-size: 1.5rem;
}

.credential-info {
    color: #333;
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 10px;
}

.success-description {
    color: #666;
    line-height: 1.5;
}

/* 步骤操作按钮 */
.step-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
    padding-top: 20px;
    border-top: 1px solid #e1e5e9;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 100px;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e1e5e9;
}

.btn-secondary:hover {
    background: #e9ecef;
    color: #333;
}

/* 错误提示 */
.error-toast {
    position: fixed;
    top: 20px;
    right: 20px;
    background: #e74c3c;
    color: white;
    padding: 15px 20px;
    border-radius: 8px;
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
    z-index: 10001;
    animation: slideInRight 0.3s ease;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .credential-manager-modal {
        width: 95%;
        margin: 10px;
    }
    
    .modal-content {
        padding: 20px;
    }
    
    .credential-type {
        padding: 15px;
    }
    
    .type-icon {
        font-size: 1.5rem;
        margin-right: 12px;
        min-width: 40px;
    }
    
    .step-actions {
        flex-direction: column;
    }
    
    .btn {
        width: 100%;
    }
}

@media (max-width: 480px) {
    .modal-header {
        padding: 15px 20px;
    }
    
    .modal-header h2 {
        font-size: 1.3rem;
    }
    
    .modal-content {
        padding: 15px;
    }
    
    .credential-type {
        flex-direction: column;
        text-align: center;
        padding: 20px 15px;
    }
    
    .type-icon {
        margin-right: 0;
        margin-bottom: 10px;
    }
    
    .type-arrow {
        display: none;
    }
}
