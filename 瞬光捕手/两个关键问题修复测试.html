<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>两个关键问题修复测试 - 瞬光捕手</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            margin: 0;
            padding: 20px;
            min-height: 100vh;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(0, 0, 0, 0.3);
            padding: 30px;
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            border-left: 4px solid #4CAF50;
        }
        
        .test-result {
            margin: 8px 0;
            padding: 8px 12px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 14px;
        }
        
        .success { background: rgba(76, 175, 80, 0.3); border-left: 4px solid #4CAF50; }
        .error { background: rgba(244, 67, 54, 0.3); border-left: 4px solid #f44336; }
        .warning { background: rgba(255, 193, 7, 0.3); border-left: 4px solid #FFC107; }
        .info { background: rgba(33, 150, 243, 0.3); border-left: 4px solid #2196F3; }
        
        button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        button:hover { 
            background: #45a049; 
            transform: translateY(-2px);
        }
        
        .touch-test-area {
            background: rgba(255, 255, 255, 0.1);
            border: 2px dashed #4CAF50;
            border-radius: 10px;
            padding: 30px;
            margin: 15px 0;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .touch-test-area:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: #66BB6A;
        }
        
        .touch-test-area.active {
            background: rgba(76, 175, 80, 0.3);
            border-color: #4CAF50;
            border-style: solid;
        }
        
        .difficulty-test {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        select {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            margin: 0 10px;
        }
        
        select option {
            background: #333;
            color: white;
        }
        
        .problem-card {
            background: rgba(255, 255, 255, 0.05);
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #FF9800;
        }
        
        .fix-status {
            display: inline-block;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            margin-right: 10px;
            text-align: center;
            line-height: 20px;
            font-weight: bold;
            font-size: 12px;
        }
        
        .fix-status.success { background: #4CAF50; }
        .fix-status.error { background: #f44336; }
        
        .log-area {
            background: rgba(0, 0, 0, 0.5);
            padding: 15px;
            border-radius: 5px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
            margin-top: 15px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 瞬光捕手 - 两个关键问题修复测试</h1>
        <p>验证难度设置保存失败和触摸交互失效两个问题的修复效果</p>
        
        <div class="test-controls">
            <button onclick="runAllTests()">运行全部测试</button>
            <button onclick="testStorageAPI()">测试存储API</button>
            <button onclick="testTouchInteraction()">测试触摸交互</button>
            <button onclick="clearResults()">清除结果</button>
        </div>
        
        <div class="test-section">
            <h3>📋 修复项目概览</h3>
            <div class="problem-card">
                <div class="fix-status success">✓</div>
                <strong>问题1：难度设置保存失败</strong><br>
                <small>错误：TypeError: storageService.set is not a function</small><br>
                <small>修复：将 storageService.set() 改为 storageService.put()</small>
            </div>
            <div class="problem-card">
                <div class="fix-status success">✓</div>
                <strong>问题2：触摸交互失效</strong><br>
                <small>问题：非游戏界面的触摸操作无响应</small><br>
                <small>修复：完善触摸事件处理，支持所有界面的触摸交互</small>
            </div>
        </div>
        
        <div class="test-section">
            <h3>🗄️ 存储API测试</h3>
            <div id="storage-test-results"></div>
            <div class="difficulty-test">
                <label>测试难度设置保存：</label>
                <select id="test-difficulty-select" onchange="testDifficultySave(this.value)">
                    <option value="beginner">新手</option>
                    <option value="easy">简单</option>
                    <option value="normal" selected>普通</option>
                    <option value="hard">困难</option>
                    <option value="expert">专家</option>
                    <option value="master">大师</option>
                </select>
                <button onclick="testStorageOperations()">测试存储操作</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>👆 触摸交互测试</h3>
            <div id="touch-test-results"></div>
            <div class="touch-test-area" id="touch-test-area" 
                 ontouchstart="handleTouchTest(event, 'touchstart')"
                 ontouchmove="handleTouchTest(event, 'touchmove')"
                 ontouchend="handleTouchTest(event, 'touchend')"
                 onclick="handleTouchTest(event, 'click')">
                <h4>触摸测试区域</h4>
                <p>请在此区域进行各种触摸操作：</p>
                <p>• 单击/点击</p>
                <p>• 双击</p>
                <p>• 长按</p>
                <p>• 滑动</p>
            </div>
            <div>
                <button onclick="simulateTouchEvents()">模拟触摸事件</button>
                <button onclick="testButtonTouch(this)">测试按钮触摸</button>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试日志</h3>
            <div id="test-log" class="log-area"></div>
        </div>
    </div>

    <!-- 加载所有必需的脚本文件 -->
    <!-- EdgeOne 云存储支持 -->
    <script src="js/utils/edgeOneStorageImpl.js"></script>
    <script src="js/config/edge-one-storage-init.js"></script>

    <!-- 核心存储和服务模块 -->
    <script src="js/utils/storage.js"></script>
    <script src="js/utils/i18n.js"></script>
    <script src="js/utils/touch-helper.js"></script>

    <!-- 难度配置管理器 -->
    <script src="js/config/difficulty-config.js"></script>

    <!-- 新用户管理系统 -->
    <script src="js/core/user-data-structure.js"></script>
    <script src="js/utils/user-storage-service.js"></script>
    <script src="js/core/user-manager.js"></script>
    <script src="js/core/user-switch-manager.js"></script>
    <script src="js/core/user-system-adapter.js"></script>
    <script src="js/core/user-credential-system.js"></script>
    <script src="js/ui/user-management-ui.js"></script>

    <!-- 游戏核心模块 -->
    <script src="js/core/game-engine.js"></script>
    <script src="js/core/player-manager.js"></script>
    <script src="js/core/level-manager.js"></script>
    <script src="js/core/leaderboard-manager.js"></script>
    <script src="js/core/difficulty-leaderboard-manager.js"></script>
    <script src="js/core/level-editor.js"></script>
    <script src="js/ui/screen-manager.js"></script>
    <script src="js/ui/input-handler.js"></script>
    <script src="js/ui/modern-effects.js"></script>

    <script>
        // 日志系统
        const testLog = document.getElementById('test-log');
        let touchTestCount = 0;
        
        function logMessage(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const div = document.createElement('div');
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${message}`;
            div.style.color = type === 'error' ? '#f44336' : type === 'warning' ? '#FFC107' : '#4CAF50';
            testLog.appendChild(div);
            testLog.scrollTop = testLog.scrollHeight;
        }
        
        function addResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.textContent = message;
            container.appendChild(div);
        }
        
        function clearResults() {
            ['storage-test-results', 'touch-test-results'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
            testLog.innerHTML = '';
            touchTestCount = 0;
        }

        function testStorageAPI() {
            logMessage('🗄️ 开始测试存储API修复...', 'info');
            
            // 检查storageService是否存在
            if (window.storageService) {
                addResult('storage-test-results', '✅ storageService 模块已加载', 'success');
                
                // 检查put方法是否存在
                if (typeof storageService.put === 'function') {
                    addResult('storage-test-results', '✅ storageService.put 方法存在', 'success');
                    logMessage('✅ 存储API put方法验证通过', 'success');
                } else {
                    addResult('storage-test-results', '❌ storageService.put 方法不存在', 'error');
                    logMessage('❌ 存储API put方法验证失败', 'error');
                }
                
                // 检查get方法是否存在
                if (typeof storageService.get === 'function') {
                    addResult('storage-test-results', '✅ storageService.get 方法存在', 'success');
                    logMessage('✅ 存储API get方法验证通过', 'success');
                } else {
                    addResult('storage-test-results', '❌ storageService.get 方法不存在', 'error');
                    logMessage('❌ 存储API get方法验证失败', 'error');
                }
                
                // 检查是否有错误的set方法调用
                if (typeof storageService.set === 'function') {
                    addResult('storage-test-results', '⚠️ storageService.set 方法仍然存在（可能是兼容性方法）', 'warning');
                } else {
                    addResult('storage-test-results', '✅ 没有错误的 storageService.set 方法', 'success');
                }
                
            } else {
                addResult('storage-test-results', '❌ storageService 模块未加载', 'error');
                logMessage('❌ storageService 模块验证失败', 'error');
            }
            
            // 检查难度配置管理器
            if (window.difficultyConfigManager) {
                addResult('storage-test-results', '✅ difficultyConfigManager 模块已加载', 'success');
                logMessage('✅ 难度配置管理器验证通过', 'success');
            } else {
                addResult('storage-test-results', '❌ difficultyConfigManager 模块未加载', 'error');
                logMessage('❌ 难度配置管理器验证失败', 'error');
            }
            
            addResult('storage-test-results', '📋 存储API测试完成', 'info');
        }

        async function testDifficultySave(difficulty) {
            logMessage(`🎯 测试难度设置保存: ${difficulty}`, 'info');
            
            if (window.difficultyConfigManager) {
                try {
                    const success = await difficultyConfigManager.setDifficulty(difficulty);
                    if (success) {
                        addResult('storage-test-results', `✅ 难度设置保存成功: ${difficulty}`, 'success');
                        logMessage(`✅ 难度设置保存成功: ${difficulty}`, 'success');
                    } else {
                        addResult('storage-test-results', `❌ 难度设置保存失败: ${difficulty}`, 'error');
                        logMessage(`❌ 难度设置保存失败: ${difficulty}`, 'error');
                    }
                } catch (error) {
                    addResult('storage-test-results', `❌ 难度设置保存出错: ${error.message}`, 'error');
                    logMessage(`❌ 难度设置保存出错: ${error.message}`, 'error');
                }
            } else {
                addResult('storage-test-results', '❌ difficultyConfigManager 未加载', 'error');
            }
        }

        async function testStorageOperations() {
            logMessage('🗄️ 测试基本存储操作...', 'info');
            
            if (window.storageService) {
                try {
                    // 测试put操作
                    const testKey = 'test_key_' + Date.now();
                    const testValue = { test: true, timestamp: Date.now() };
                    
                    const putResult = await storageService.put(testKey, testValue);
                    if (putResult) {
                        addResult('storage-test-results', '✅ 存储put操作成功', 'success');
                        logMessage('✅ 存储put操作成功', 'success');
                        
                        // 测试get操作
                        const getValue = await storageService.get(testKey);
                        if (getValue && getValue.test === true) {
                            addResult('storage-test-results', '✅ 存储get操作成功', 'success');
                            logMessage('✅ 存储get操作成功', 'success');
                        } else {
                            addResult('storage-test-results', '❌ 存储get操作失败', 'error');
                            logMessage('❌ 存储get操作失败', 'error');
                        }
                    } else {
                        addResult('storage-test-results', '❌ 存储put操作失败', 'error');
                        logMessage('❌ 存储put操作失败', 'error');
                    }
                } catch (error) {
                    addResult('storage-test-results', `❌ 存储操作出错: ${error.message}`, 'error');
                    logMessage(`❌ 存储操作出错: ${error.message}`, 'error');
                }
            }
        }

        function testTouchInteraction() {
            logMessage('👆 开始测试触摸交互修复...', 'info');
            
            // 检查InputHandler是否存在
            if (window.inputHandler) {
                addResult('touch-test-results', '✅ InputHandler 模块已加载', 'success');
                
                // 检查触摸处理方法是否存在
                const touchMethods = ['handleTouchStart', 'handleTouchEnd', 'handleTouchMove', 'handleTap'];
                touchMethods.forEach(method => {
                    if (typeof inputHandler[method] === 'function') {
                        addResult('touch-test-results', `✅ ${method} 方法存在`, 'success');
                        logMessage(`✅ ${method} 方法验证通过`, 'success');
                    } else {
                        addResult('touch-test-results', `❌ ${method} 方法不存在`, 'error');
                        logMessage(`❌ ${method} 方法验证失败`, 'error');
                    }
                });
                
                // 检查新增的方法
                const newMethods = ['simulateMouseClickFromTouch', 'handleGameSwipe', 'handleUISwipe'];
                newMethods.forEach(method => {
                    if (typeof inputHandler[method] === 'function') {
                        addResult('touch-test-results', `✅ 新增方法 ${method} 存在`, 'success');
                        logMessage(`✅ 新增方法 ${method} 验证通过`, 'success');
                    } else {
                        addResult('touch-test-results', `⚠️ 新增方法 ${method} 不存在`, 'warning');
                        logMessage(`⚠️ 新增方法 ${method} 验证失败`, 'warning');
                    }
                });
                
            } else {
                addResult('touch-test-results', '❌ InputHandler 模块未加载', 'error');
                logMessage('❌ InputHandler 模块验证失败', 'error');
            }
            
            addResult('touch-test-results', '📋 触摸交互测试完成', 'info');
            addResult('touch-test-results', '💡 请在触摸测试区域进行实际触摸操作验证', 'info');
        }

        function handleTouchTest(event, eventType) {
            touchTestCount++;
            const testArea = document.getElementById('touch-test-area');
            
            // 视觉反馈
            testArea.classList.add('active');
            setTimeout(() => {
                testArea.classList.remove('active');
            }, 200);
            
            const logEntry = `第${touchTestCount}次触摸事件: ${eventType}`;
            addResult('touch-test-results', logEntry, 'info');
            logMessage(`👆 ${logEntry}`, 'info');
            
            if (eventType === 'touchstart' || eventType === 'click') {
                logMessage(`👆 触摸坐标: (${event.clientX || event.touches?.[0]?.clientX || 'N/A'}, ${event.clientY || event.touches?.[0]?.clientY || 'N/A'})`, 'info');
            }
        }

        function testButtonTouch(button) {
            logMessage('👆 按钮触摸测试成功', 'success');
            addResult('touch-test-results', '✅ 按钮触摸响应正常', 'success');
            
            // 视觉反馈
            const originalText = button.textContent;
            button.textContent = '触摸成功！';
            button.style.background = '#4CAF50';
            
            setTimeout(() => {
                button.textContent = originalText;
                button.style.background = '';
            }, 1000);
        }

        function simulateTouchEvents() {
            logMessage('👆 模拟触摸事件...', 'info');
            
            const testArea = document.getElementById('touch-test-area');
            const rect = testArea.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;
            
            // 模拟触摸开始
            const touchStart = new TouchEvent('touchstart', {
                bubbles: true,
                cancelable: true,
                touches: [{
                    clientX: centerX,
                    clientY: centerY,
                    target: testArea
                }]
            });
            
            testArea.dispatchEvent(touchStart);
            
            // 模拟触摸结束
            setTimeout(() => {
                const touchEnd = new TouchEvent('touchend', {
                    bubbles: true,
                    cancelable: true,
                    changedTouches: [{
                        clientX: centerX,
                        clientY: centerY,
                        target: testArea
                    }]
                });
                
                testArea.dispatchEvent(touchEnd);
                addResult('touch-test-results', '✅ 模拟触摸事件完成', 'success');
            }, 100);
        }

        function runAllTests() {
            clearResults();
            logMessage('🚀 开始运行两个关键问题修复测试...', 'info');
            
            setTimeout(() => {
                testStorageAPI();
                setTimeout(() => {
                    testTouchInteraction();
                    setTimeout(() => {
                        logMessage('📊 所有测试完成', 'success');
                    }, 500);
                }, 500);
            }, 100);
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', () => {
            logMessage('📄 两个关键问题修复测试页面加载完成', 'info');
            setTimeout(() => {
                runAllTests();
            }, 1000);
        });
    </script>
</body>
</html>
