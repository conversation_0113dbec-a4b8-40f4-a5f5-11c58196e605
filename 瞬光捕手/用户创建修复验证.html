<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户创建修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
            line-height: 1.6;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #16213e;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.danger {
            background: #f44336;
        }
        .results {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .results.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .results.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .results.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #333;
            border-radius: 4px;
            background: #2a2a4e;
            color: white;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 500px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 用户创建修复验证</h1>
        
        <div class="section">
            <h2>📋 修复内容说明</h2>
            <div class="results info">
修复的关键问题：
1. 用户管理器在用户系统适配器中创建时，存储服务引用未正确设置
2. 用户管理器构造函数现在接受存储服务参数
3. 用户系统适配器现在正确传递存储服务给用户管理器

修复的文件：
- js/core/user-manager.js: 修改构造函数接受存储服务参数
- js/core/user-system-adapter.js: 修改用户管理器创建过程，正确传递存储服务

预期效果：
- 用户创建功能应该正常工作
- 不再出现"获取null对象的get字段出错"错误
- 用户管理界面应该能正常创建用户
            </div>
        </div>

        <div class="section">
            <h2>🎮 游戏环境测试</h2>
            <div class="test-controls">
                <button class="test-button" onclick="loadGame()">加载游戏</button>
                <button class="test-button" onclick="checkSystemStatus()">检查系统状态</button>
                <button class="test-button danger" onclick="clearData()">清除数据重新测试</button>
            </div>
            
            <div class="iframe-container" id="game-container" style="display: none;">
                <iframe id="game-frame" src=""></iframe>
            </div>
            
            <div id="game-status" class="results info">
                点击"加载游戏"开始测试...
            </div>
        </div>

        <div class="section">
            <h2>🧪 直接用户创建测试</h2>
            <div class="form-group">
                <label for="test-identifier">用户标识符:</label>
                <input type="text" id="test-identifier" placeholder="输入测试用户标识符" value="testuser123">
            </div>
            <div class="form-group">
                <label for="test-display-name">显示名称:</label>
                <input type="text" id="test-display-name" placeholder="输入测试显示名称" value="测试用户123">
            </div>
            <div class="test-controls">
                <button class="test-button" onclick="testDirectUserCreation()">直接测试用户创建</button>
                <button class="test-button" onclick="testUserCreationInGame()">在游戏中测试</button>
            </div>
            
            <div id="creation-results" class="results info">
                填写用户信息后点击测试按钮...
            </div>
        </div>

        <div class="section">
            <h2>📊 详细诊断</h2>
            <div class="test-controls">
                <button class="test-button" onclick="runFullDiagnostic()">运行完整诊断</button>
                <button class="test-button" onclick="checkUserManagerState()">检查用户管理器状态</button>
                <button class="test-button" onclick="testStorageService()">测试存储服务</button>
            </div>
            
            <div id="diagnostic-results" class="results info">
                点击按钮开始诊断...
            </div>
        </div>
    </div>

    <script>
        let gameFrame = null;

        function loadGame() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            
            updateResults('game-status', 'info', '🔄 正在加载游戏...');
            
            frame.src = './index.html';
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                updateResults('game-status', 'success', '✅ 游戏已加载，等待初始化完成...');
                
                // 等待游戏完全初始化
                setTimeout(() => {
                    checkSystemStatus();
                }, 3000);
            };

            frame.onerror = function(error) {
                updateResults('game-status', 'error', `❌ 游戏加载失败: ${error}`);
            };
        }

        function checkSystemStatus() {
            if (!gameFrame) {
                updateResults('game-status', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 系统状态检查:\n\n';

                // 检查核心服务
                const coreServices = [
                    'storageService',
                    'userSystemAdapter', 
                    'userManagementUI'
                ];

                report += '核心服务状态:\n';
                coreServices.forEach(service => {
                    const exists = gameWindow[service] !== undefined;
                    const initialized = exists && gameWindow[service].initialized;
                    report += `  ${service}: ${exists ? '✅' : '❌'}`;
                    if (exists && typeof initialized === 'boolean') {
                        report += ` (${initialized ? '已初始化' : '未初始化'})`;
                    }
                    report += '\n';
                });

                // 检查用户管理器
                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                    try {
                        const userManager = gameWindow.userSystemAdapter.getUserManager();
                        report += '\n用户管理器状态:\n';
                        report += `  存在: ${userManager ? '✅' : '❌'}\n`;
                        if (userManager) {
                            report += `  已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                            report += `  存储服务: ${userManager.storageService ? '✅' : '❌'}\n`;
                            if (userManager.storageService) {
                                report += `  存储服务类型: ${userManager.storageService.constructor.name}\n`;
                                report += `  存储服务已初始化: ${userManager.storageService.initialized ? '✅' : '❌'}\n`;
                            }
                        }
                    } catch (error) {
                        report += `\n用户管理器获取失败: ❌ ${error.message}\n`;
                    }
                } else {
                    report += '\n用户系统适配器未初始化\n';
                }

                updateResults('game-status', 'info', report);

            } catch (error) {
                updateResults('game-status', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        async function testDirectUserCreation() {
            const identifier = document.getElementById('test-identifier').value.trim();
            const displayName = document.getElementById('test-display-name').value.trim();

            if (!identifier || !displayName) {
                updateResults('creation-results', 'error', '❌ 请填写用户标识符和显示名称');
                return;
            }

            if (!gameFrame) {
                updateResults('creation-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = `🧪 直接用户创建测试:\n标识符: ${identifier}\n显示名称: ${displayName}\n\n`;

                // 获取用户管理器
                if (!gameWindow.userSystemAdapter || !gameWindow.userSystemAdapter.initialized) {
                    report += '❌ 用户系统适配器未初始化\n';
                    updateResults('creation-results', 'error', report);
                    return;
                }

                const userManager = gameWindow.userSystemAdapter.getUserManager();
                if (!userManager) {
                    report += '❌ 无法获取用户管理器\n';
                    updateResults('creation-results', 'error', report);
                    return;
                }

                report += '开始创建用户...\n';
                updateResults('creation-results', 'info', report);

                // 执行用户创建
                const newUser = await userManager.createUser(identifier, displayName);
                
                report += `✅ 用户创建成功!\n`;
                report += `用户ID: ${newUser.identifier}\n`;
                report += `显示名称: ${newUser.displayName}\n`;
                report += `用户类型: ${newUser.type}\n`;
                report += `创建时间: ${new Date(newUser.createdAt).toLocaleString()}\n`;

                updateResults('creation-results', 'success', report);

            } catch (error) {
                let report = `❌ 用户创建失败:\n`;
                report += `错误信息: ${error.message}\n`;
                if (error.stack) {
                    report += `错误堆栈:\n${error.stack}\n`;
                }
                updateResults('creation-results', 'error', report);
            }
        }

        function testUserCreationInGame() {
            if (!gameFrame) {
                updateResults('creation-results', 'error', '❌ 请先加载游戏');
                return;
            }

            updateResults('creation-results', 'info', 
                '🎮 在游戏中测试用户创建:\n\n' +
                '步骤:\n' +
                '1. 在游戏界面中点击"用户管理"按钮\n' +
                '2. 在弹出的对话框中填写用户信息\n' +
                '3. 点击"创建用户"按钮\n' +
                '4. 观察是否成功创建用户\n\n' +
                '💡 同时观察浏览器控制台的日志信息'
            );
        }

        function runFullDiagnostic() {
            if (!gameFrame) {
                updateResults('diagnostic-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '📊 完整系统诊断:\n\n';

                // 基础环境检查
                report += '=== 基础环境 ===\n';
                report += `浏览器: ${navigator.userAgent.split(' ')[0]}\n`;
                report += `localStorage: ${typeof Storage !== 'undefined' ? '✅' : '❌'}\n`;
                report += `IndexedDB: ${typeof indexedDB !== 'undefined' ? '✅' : '❌'}\n`;

                // 依赖项检查
                report += '\n=== 依赖项检查 ===\n';
                const dependencies = [
                    'StorageKeySchema', 'UserDataValidator', 'UserDataStructure',
                    'UserManager', 'UserStorageService', 'UserSystemAdapter'
                ];
                
                dependencies.forEach(dep => {
                    const exists = gameWindow[dep] !== undefined;
                    report += `${dep}: ${exists ? '✅' : '❌'}\n`;
                });

                // 服务状态检查
                report += '\n=== 服务状态 ===\n';
                const services = ['storageService', 'userSystemAdapter'];
                services.forEach(service => {
                    const exists = gameWindow[service] !== undefined;
                    const initialized = exists && gameWindow[service].initialized;
                    report += `${service}: ${exists ? '✅' : '❌'}`;
                    if (exists && typeof initialized === 'boolean') {
                        report += ` (${initialized ? '已初始化' : '未初始化'})`;
                    }
                    report += '\n';
                });

                // 用户管理器详细检查
                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                    const userManager = gameWindow.userSystemAdapter.getUserManager();
                    if (userManager) {
                        report += '\n=== 用户管理器详细状态 ===\n';
                        report += `类型: ${userManager.constructor.name}\n`;
                        report += `已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                        report += `存储服务: ${userManager.storageService ? '✅' : '❌'}\n`;
                        report += `用户数量: ${userManager.users ? userManager.users.size : 0}\n`;
                        report += `当前用户: ${userManager.currentUser ? userManager.currentUser.displayName : '无'}\n`;
                    }
                }

                updateResults('diagnostic-results', 'info', report);

            } catch (error) {
                updateResults('diagnostic-results', 'error', `❌ 诊断失败: ${error.message}`);
            }
        }

        function checkUserManagerState() {
            if (!gameFrame) {
                updateResults('diagnostic-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 用户管理器状态检查:\n\n';

                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                    const userManager = gameWindow.userSystemAdapter.getUserManager();
                    
                    if (userManager) {
                        report += '用户管理器基本信息:\n';
                        report += `  构造函数: ${userManager.constructor.name}\n`;
                        report += `  已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                        
                        report += '\n存储服务检查:\n';
                        if (userManager.storageService) {
                            report += `  存储服务存在: ✅\n`;
                            report += `  存储服务类型: ${userManager.storageService.constructor.name}\n`;
                            report += `  存储服务已初始化: ${userManager.storageService.initialized ? '✅' : '❌'}\n`;
                            report += `  存储类型: ${userManager.storageService.storageType || '未知'}\n`;
                        } else {
                            report += `  存储服务存在: ❌\n`;
                        }

                        report += '\n方法检查:\n';
                        const methods = ['createUser', 'userExists', 'loadUserCore'];
                        methods.forEach(method => {
                            const exists = typeof userManager[method] === 'function';
                            report += `  ${method}: ${exists ? '✅' : '❌'}\n`;
                        });

                    } else {
                        report += '❌ 用户管理器不存在\n';
                    }
                } else {
                    report += '❌ 用户系统适配器未初始化\n';
                }

                updateResults('diagnostic-results', 'info', report);

            } catch (error) {
                updateResults('diagnostic-results', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        function testStorageService() {
            if (!gameFrame) {
                updateResults('diagnostic-results', 'error', '❌ 请先加载游戏');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🗄️ 存储服务测试:\n\n';

                if (gameWindow.storageService) {
                    const storage = gameWindow.storageService;
                    report += '基础存储服务:\n';
                    report += `  存在: ✅\n`;
                    report += `  已初始化: ${storage.initialized ? '✅' : '❌'}\n`;
                    report += `  存储类型: ${storage.storageType || '未知'}\n`;
                    
                    // 测试基础操作
                    report += '\n基础操作测试:\n';
                    try {
                        // 测试写入和读取
                        const testKey = 'test_key_' + Date.now();
                        const testValue = { test: true, timestamp: Date.now() };
                        
                        storage.put(testKey, testValue).then(() => {
                            return storage.get(testKey);
                        }).then((result) => {
                            if (result && result.test === true) {
                                report += '  读写测试: ✅\n';
                            } else {
                                report += '  读写测试: ❌ 数据不匹配\n';
                            }
                            // 清理测试数据
                            storage.delete(testKey);
                            updateResults('diagnostic-results', 'success', report);
                        }).catch((error) => {
                            report += `  读写测试: ❌ ${error.message}\n`;
                            updateResults('diagnostic-results', 'error', report);
                        });
                        
                        report += '  读写测试: 进行中...\n';
                        
                    } catch (error) {
                        report += `  读写测试: ❌ ${error.message}\n`;
                    }
                } else {
                    report += '❌ 存储服务不存在\n';
                }

                updateResults('diagnostic-results', 'info', report);

            } catch (error) {
                updateResults('diagnostic-results', 'error', `❌ 测试失败: ${error.message}`);
            }
        }

        function clearData() {
            if (confirm('确定要清除所有数据吗？这将删除所有用户和游戏数据。')) {
                try {
                    localStorage.clear();
                    if (gameFrame) {
                        gameFrame.contentWindow.localStorage.clear();
                    }
                    updateResults('game-status', 'success', '✅ 数据已清除，请重新加载游戏进行测试');
                } catch (error) {
                    updateResults('game-status', 'error', `❌ 清除数据失败: ${error.message}`);
                }
            }
        }

        function updateResults(elementId, type, content) {
            const element = document.getElementById(elementId);
            element.className = `results ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        // 页面加载完成后显示说明
        document.addEventListener('DOMContentLoaded', () => {
            updateResults('game-status', 'info', 
                '🔧 用户创建修复验证工具\n\n' +
                '本工具用于验证用户创建功能的修复效果。\n\n' +
                '测试步骤:\n' +
                '1. 点击"加载游戏"加载游戏环境\n' +
                '2. 等待系统初始化完成\n' +
                '3. 使用"直接测试用户创建"验证修复效果\n' +
                '4. 在游戏中实际测试用户管理功能\n\n' +
                '如果修复成功，应该不再出现null对象错误。'
            );
        });
    </script>
</body>
</html>
