# 瞬光捕手 - EdgeOne 云存储集成指南

## 📋 概述

本指南详细说明了如何在瞬光捕手游戏中正确集成和使用 EdgeOne 云存储功能。通过依赖注入的方式，无需修改游戏核心逻辑即可启用云存储。

## 🏗️ 架构设计

### 存储系统架构

```mermaid
graph TD
    A[游戏启动] --> B[加载 EdgeOne 模块]
    B --> C[EdgeOne 存储初始化器]
    C --> D[创建 EdgeOneStorageImpl 实例]
    D --> E[注入到 window.edgeOneStorage]
    E --> F[StorageService 自动检测]
    F --> G{检测到云存储?}
    G -->|是| H[使用 EdgeOne 云存储]
    G -->|否| I[使用本地存储]
    H --> J[游戏存储操作]
    I --> J
```

### 关键特性

- ✅ **零侵入集成**：无需修改游戏核心逻辑
- ✅ **自动检测机制**：智能选择最佳存储方案
- ✅ **优雅降级**：云存储不可用时自动切换到本地存储
- ✅ **环境适配**：支持开发、测试、生产环境的不同配置

## 🚀 集成步骤

### 步骤 1：文件结构确认

确保以下文件已正确添加到项目中：

```
瞬光捕手/
├── js/
│   ├── utils/
│   │   └── edgeOneStorageImpl.js          # EdgeOne 存储实现类
│   └── config/
│       └── edge-one-storage-init.js       # EdgeOne 存储初始化模块
├── index.html                             # 主页面（已更新脚本引用）
├── edge-one-deployment-example.html       # 部署配置示例
└── EdgeOne云存储集成指南.md               # 本文档
```

### 步骤 2：页面脚本加载顺序

在 `index.html` 中，脚本加载顺序如下：

```html
<!-- EdgeOne 云存储支持 -->
<script src="js/utils/edgeOneStorageImpl.js"></script>
<script src="js/config/edge-one-storage-init.js"></script>

<!-- 核心存储和服务模块 -->
<script src="js/utils/storage.js"></script>
<!-- 其他模块... -->
```

### 步骤 3：游戏初始化流程

游戏的 `main.js` 已更新，包含以下关键方法：

1. **`initEdgeOneStorageIfAvailable()`** - 在存储服务初始化前尝试初始化 EdgeOne 云存储
2. **`getEdgeOneStorageConfig()`** - 获取 EdgeOne 存储配置

## 🔧 使用方法

### 方法 1：开发环境使用（相对路径）

在开发环境中，EdgeOne 存储会自动使用相对路径调用本地 Cloudflare Functions：

```javascript
// 游戏启动时自动执行，无需手动调用
// EdgeOne 存储初始化器会自动配置为使用相对路径 /storage/
```

### 方法 2：生产环境部署

在生产环境部署时，需要在游戏加载前注入配置：

```html
<script>
// 在游戏主页面的 <head> 部分添加
(async function() {
    // 等待 EdgeOne 初始化器加载
    function waitForInitializer() {
        return new Promise((resolve) => {
            const checkInitializer = () => {
                if (typeof window.initEdgeOneStorage === 'function') {
                    resolve();
                } else {
                    setTimeout(checkInitializer, 100);
                }
            };
            checkInitializer();
        });
    }
    
    // 等待页面和初始化器就绪
    if (document.readyState === 'loading') {
        await new Promise(resolve => {
            document.addEventListener('DOMContentLoaded', resolve);
        });
    }
    await waitForInitializer();
    
    // 配置 EdgeOne 云存储
    const deploymentConfig = {
        // 生产环境 API URL
        productionApiUrl: 'https://your-game-domain.com',
        
        // 自定义配置
        customConfig: {
            testConnection: true,
            timeout: 15000,
            retryCount: 3,
            enableCache: true,
            enableFallback: true
        }
    };
    
    // 初始化 EdgeOne 云存储
    await window.initEdgeOneStorage(deploymentConfig);
})();
</script>
```

### 方法 3：手动配置（高级用户）

对于需要精细控制的场景，可以手动配置：

```javascript
// 自定义配置示例
const customConfig = {
    productionApiUrl: 'https://api.your-domain.com',
    customConfig: {
        // 连接配置
        timeout: 20000,
        retryCount: 5,
        retryDelay: 2000,
        
        // 功能开关
        enableCache: true,
        enableFallback: true,
        testConnection: false,
        
        // 缓存配置
        cacheTimeout: 300000  // 5分钟缓存
    }
};

// 手动初始化
const success = await window.initEdgeOneStorage(customConfig);
console.log('EdgeOne 存储初始化结果:', success);
```

## 📊 状态监控

### 检查存储状态

```javascript
// 获取 EdgeOne 存储状态
const status = window.getEdgeOneStorageStatus();
console.log('存储状态:', status);

// 状态信息包含：
// - initialized: 是否已初始化
// - available: 是否可用
// - mode: 存储模式 ('remote' | 'local' | 'none')
// - requestCount: 请求计数
// - environment: 当前环境
```

### 监控存储操作

```javascript
// 测试存储操作
async function testStorage() {
    if (window.edgeOneStorage) {
        try {
            await window.edgeOneStorage.put('test-key', 'test-value');
            const value = await window.edgeOneStorage.get('test-key');
            await window.edgeOneStorage.delete('test-key');
            console.log('存储测试成功:', value);
        } catch (error) {
            console.error('存储测试失败:', error);
        }
    }
}
```

## 🎮 游戏中的使用

### 存储操作示例

游戏代码无需修改，继续使用原有的存储接口：

```javascript
// 游戏中的存储操作（无需修改）
await storageService.put('player-progress', gameData);
const progress = await storageService.get('player-progress');
await storageService.delete('old-save');

// 存储服务会自动选择使用 EdgeOne 云存储或本地存储
```

### 存储类型检查

```javascript
// 检查当前使用的存储类型
console.log('当前存储类型:', storageService.storageType);
// 可能的值：'edgeone', 'indexeddb', 'localstorage', 'memory'
```

## 🔍 故障排除

### 常见问题

1. **EdgeOne 存储未初始化**
   - 检查脚本加载顺序
   - 确认 `edgeOneStorageImpl.js` 和 `edge-one-storage-init.js` 已正确加载

2. **云存储连接失败**
   - 检查 Cloudflare Functions 是否已部署
   - 验证 API 端点是否可访问
   - 检查网络连接和 CORS 配置

3. **存储操作异常**
   - 查看浏览器控制台错误信息
   - 检查存储服务是否已正确初始化
   - 验证数据格式是否正确

### 调试工具

使用部署示例页面进行调试：

```bash
# 在浏览器中打开
瞬光捕手/edge-one-deployment-example.html
```

## 📈 性能优化

### 缓存配置

```javascript
const optimizedConfig = {
    customConfig: {
        enableCache: true,
        cacheTimeout: 300000,  // 5分钟缓存
        timeout: 10000,        // 适当的超时时间
        retryCount: 2          // 减少重试次数
    }
};
```

### 批量操作

```javascript
// 推荐：使用批量操作
const batchData = {
    'key1': 'value1',
    'key2': 'value2',
    'key3': 'value3'
};
await window.edgeOneStorage.putBatch(batchData);

// 避免：循环单个操作
for (const [key, value] of Object.entries(batchData)) {
    await window.edgeOneStorage.put(key, value);  // 不推荐
}
```

## 🚀 部署清单

- [ ] 确认 Cloudflare Functions 已部署
- [ ] 配置正确的生产环境 API URL
- [ ] 测试云存储连接
- [ ] 验证降级机制工作正常
- [ ] 检查游戏存储操作功能
- [ ] 监控存储性能和错误率

## 📞 技术支持

如果在集成过程中遇到问题，请：

1. 查看浏览器控制台的详细错误信息
2. 使用部署示例页面进行诊断
3. 检查 Cloudflare Functions 的部署状态
4. 验证网络连接和 API 端点可访问性

---

**注意**：本集成方案采用依赖注入模式，确保了游戏核心逻辑的稳定性和可维护性。EdgeOne 云存储作为可选功能，不会影响游戏的基本运行。
