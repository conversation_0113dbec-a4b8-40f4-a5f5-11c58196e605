# 瞬光捕手 - 用户配置管理系统快速使用指南

> 5分钟快速上手新的用户管理系统

## 🚀 快速开始

### 1. 打开游戏
访问瞬光捕手游戏页面，系统会自动初始化新的用户管理系统。

### 2. 用户管理入口
- **方法一**：点击右上角的用户切换按钮（👤 游客 ⚙️）
- **方法二**：使用快捷键 `Ctrl+U`

### 3. 创建第一个用户
1. 在用户管理界面中，找到"创建新用户"部分
2. 输入**用户标识符**（如：`myuser123`）
   - 只能包含字母、数字、下划线、连字符
   - 长度3-32个字符
   - 创建后不可修改
3. 输入**显示名称**（如：`我的游戏账号`）
   - 其他人看到的名称
   - 可以随时修改
   - 支持中文
4. 点击"创建用户"按钮

### 4. 切换用户
1. 在用户列表中找到目标用户
2. 点击"切换"按钮
3. 系统会自动加载该用户的所有数据和设置

## 🎯 核心概念

### 用户标识符 vs 显示名称

| 特性 | 用户标识符 | 显示名称 |
|------|------------|----------|
| **用途** | 系统内部识别 | 用户界面显示 |
| **格式** | 字母数字下划线连字符 | 支持中文和常见符号 |
| **唯一性** | 必须唯一 | 可以重复 |
| **可修改** | 创建后不可修改 | 随时可以修改 |
| **示例** | `player001`, `zhang_san` | `超级玩家`, `张三` |

### 数据隔离机制

每个用户的数据完全独立：
```
用户A (user001):
├── user001_gameData    (游戏数据)
├── user001_settings    (用户设置)
└── user001_progress    (游戏进度)

用户B (user002):
├── user002_gameData    (游戏数据)
├── user002_settings    (用户设置)
└── user002_progress    (游戏进度)
```

## 💡 使用技巧

### 1. 用户命名建议
```javascript
// ✅ 推荐的标识符
'player001'     // 简洁明了
'zhang_san'     // 拼音命名
'team_alpha'    // 团队账号
'test_2024'     // 测试账号

// ✅ 推荐的显示名称
'超级玩家'      // 中文名称
'Team Alpha'    // 英文名称
'张三的账号'    // 个人账号
'测试账号 2024' // 临时账号
```

### 2. 数据管理
```javascript
// 保存游戏进度
await userStorageService.putUserData('progress', {
    currentLevel: 5,
    unlockedLevels: [1, 2, 3, 4, 5]
});

// 保存用户设置
await userStorageService.putUserData('settings', {
    soundVolume: 80,
    language: 'zh-CN'
}, 'audio');

// 读取数据
const progress = await userStorageService.getUserData('progress');
const audioSettings = await userStorageService.getUserData('settings', {}, 'audio');
```

### 3. 用户切换最佳实践
- 切换前确保当前游戏已保存
- 定期备份重要用户数据
- 使用有意义的用户标识符便于管理

## 🔧 常用操作

### 创建用户
```javascript
// 程序化创建
const user = await userManager.createUser('newuser', '新用户');

// 界面创建
// 用户管理 -> 创建新用户 -> 填写信息 -> 创建
```

### 切换用户
```javascript
// 程序化切换
await userManager.switchToUser('targetuser');

// 界面切换
// 用户管理 -> 用户列表 -> 点击切换按钮
```

### 数据备份
```javascript
// 导出当前用户数据
const exportData = await userManager.exportUserData('myuser');

// 保存到文件（界面操作）
// 用户管理 -> 用户操作 -> 导出当前用户数据
```

### 数据恢复
```javascript
// 导入用户数据
await userManager.importUserData(exportData);

// 从文件导入（界面操作）
// 用户管理 -> 用户操作 -> 导入用户数据 -> 选择文件
```

## 🎮 游戏集成

### 自动数据保存
系统会自动为当前用户保存：
- 游戏进度和关卡解锁状态
- 用户设置和偏好
- 统计数据和成就
- 自定义关卡

### 用户切换时的数据处理
1. **保存当前数据**：自动保存当前用户的游戏状态
2. **加载目标数据**：加载目标用户的所有数据
3. **更新界面**：更新游戏界面显示目标用户信息
4. **恢复状态**：恢复目标用户的游戏设置和进度

## 🔍 故障排除

### 问题：用户创建失败
**可能原因**：
- 标识符格式不正确
- 标识符已被使用
- 显示名称为空

**解决方案**：
1. 检查标识符是否只包含允许的字符
2. 尝试使用不同的标识符
3. 确保显示名称不为空

### 问题：用户切换后数据丢失
**可能原因**：
- 数据未正确保存
- 存储服务异常

**解决方案**：
1. 检查浏览器控制台错误信息
2. 尝试重新切换用户
3. 使用数据导出功能备份数据

### 问题：界面显示异常
**可能原因**：
- CSS文件未正确加载
- JavaScript错误

**解决方案**：
1. 刷新页面
2. 检查网络连接
3. 查看浏览器开发者工具

## 📱 移动端支持

新的用户管理系统完全支持移动端：
- 响应式界面设计
- 触摸友好的操作
- 移动端优化的用户体验

## 🔒 数据安全

### 本地存储
- 数据存储在用户设备本地
- 不同用户数据完全隔离
- 支持数据导出备份

### 云存储（如果启用）
- 支持EdgeOne云存储
- 数据加密传输
- 自动故障转移到本地存储

## 📞 获取帮助

如果遇到问题：
1. 查看本指南的故障排除部分
2. 运行测试页面验证系统功能
3. 查看详细的技术文档
4. 检查浏览器控制台的错误信息

---

**提示**：建议在正式使用前先在测试页面熟悉各项功能，确保理解用户管理系统的工作原理。
