<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户创建功能诊断</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: white;
            line-height: 1.6;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #333;
            border-radius: 8px;
            background: #16213e;
        }
        .test-button {
            background: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        .test-button:hover {
            background: #45a049;
        }
        .test-button.danger {
            background: #f44336;
        }
        .results {
            margin: 20px 0;
            padding: 15px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .results.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .results.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .results.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .iframe-container {
            margin: 20px 0;
            border: 2px solid #333;
            border-radius: 8px;
            overflow: hidden;
        }
        iframe {
            width: 100%;
            height: 600px;
            border: none;
        }
        .form-group {
            margin: 15px 0;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
        }
        .form-group input {
            width: 100%;
            padding: 8px;
            border: 1px solid #333;
            border-radius: 4px;
            background: #2a2a4e;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 用户创建功能详细诊断</h1>
        
        <div class="section">
            <h2>🎮 游戏环境加载</h2>
            <div class="test-controls">
                <button class="test-button" onclick="loadGameEnvironment()">加载游戏环境</button>
                <button class="test-button" onclick="checkGameStatus()">检查游戏状态</button>
            </div>
            
            <div class="iframe-container" id="game-container" style="display: none;">
                <iframe id="game-frame" src=""></iframe>
            </div>
            
            <div id="game-status" class="results info">
                点击"加载游戏环境"开始...
            </div>
        </div>

        <div class="section">
            <h2>🔍 系统依赖检查</h2>
            <div class="test-controls">
                <button class="test-button" onclick="checkAllDependencies()">检查所有依赖</button>
                <button class="test-button" onclick="checkUserSystemComponents()">检查用户系统组件</button>
                <button class="test-button" onclick="checkStorageServices()">检查存储服务</button>
            </div>
            
            <div id="dependency-results" class="results info">
                点击按钮开始检查...
            </div>
        </div>

        <div class="section">
            <h2>👤 用户管理器测试</h2>
            <div class="test-controls">
                <button class="test-button" onclick="testUserManagerAccess()">测试用户管理器访问</button>
                <button class="test-button" onclick="testUserManagerMethods()">测试用户管理器方法</button>
                <button class="test-button" onclick="simulateUserCreation()">模拟用户创建</button>
            </div>
            
            <div id="user-manager-results" class="results info">
                点击按钮开始测试...
            </div>
        </div>

        <div class="section">
            <h2>🧪 实际用户创建测试</h2>
            <div class="form-group">
                <label for="test-identifier">用户标识符:</label>
                <input type="text" id="test-identifier" placeholder="输入测试用户标识符" value="testuser001">
            </div>
            <div class="form-group">
                <label for="test-display-name">显示名称:</label>
                <input type="text" id="test-display-name" placeholder="输入测试显示名称" value="测试用户001">
            </div>
            <div class="test-controls">
                <button class="test-button" onclick="performActualUserCreation()">执行实际用户创建</button>
                <button class="test-button" onclick="testUserCreationInGame()">在游戏中测试创建</button>
                <button class="test-button danger" onclick="clearAllUserData()">清除所有用户数据</button>
            </div>
            
            <div id="creation-test-results" class="results info">
                填写用户信息后点击测试按钮...
            </div>
        </div>

        <div class="section">
            <h2>📊 错误日志收集</h2>
            <div class="test-controls">
                <button class="test-button" onclick="captureConsoleErrors()">捕获控制台错误</button>
                <button class="test-button" onclick="checkNetworkRequests()">检查网络请求</button>
                <button class="test-button" onclick="generateDiagnosticReport()">生成诊断报告</button>
            </div>
            
            <div id="error-log-results" class="results info">
                点击按钮收集错误信息...
            </div>
        </div>
    </div>

    <script>
        let gameFrame = null;
        let errorLog = [];
        let networkLog = [];

        // 捕获所有错误
        window.addEventListener('error', (event) => {
            errorLog.push({
                type: 'JavaScript Error',
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error ? event.error.stack : null,
                timestamp: new Date().toISOString()
            });
        });

        // 捕获未处理的Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            errorLog.push({
                type: 'Unhandled Promise Rejection',
                message: event.reason,
                timestamp: new Date().toISOString()
            });
        });

        function loadGameEnvironment() {
            const container = document.getElementById('game-container');
            const frame = document.getElementById('game-frame');
            
            updateResults('game-status', 'info', '🔄 正在加载游戏环境...');
            
            frame.src = './index.html';
            container.style.display = 'block';
            
            frame.onload = function() {
                gameFrame = frame;
                updateResults('game-status', 'success', '✅ 游戏环境已加载\n可以开始进行详细测试');
                
                // 等待一段时间让游戏完全初始化
                setTimeout(() => {
                    checkGameStatus();
                }, 2000);
            };

            frame.onerror = function(error) {
                updateResults('game-status', 'error', `❌ 游戏环境加载失败: ${error}`);
            };
        }

        function checkGameStatus() {
            if (!gameFrame) {
                updateResults('game-status', 'error', '❌ 游戏环境未加载');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 游戏状态检查:\n\n';

                // 检查基础对象
                const basicObjects = ['window', 'document', 'console'];
                report += '基础对象:\n';
                basicObjects.forEach(obj => {
                    const exists = gameWindow[obj] !== undefined;
                    report += `  ${obj}: ${exists ? '✅' : '❌'}\n`;
                });

                // 检查游戏核心对象
                const gameObjects = [
                    'storageService', 'userManager', 'userSystemAdapter', 
                    'userManagementUI', 'userSwitchManager'
                ];
                report += '\n游戏核心对象:\n';
                gameObjects.forEach(obj => {
                    const exists = gameWindow[obj] !== undefined;
                    const initialized = exists && gameWindow[obj].initialized;
                    report += `  ${obj}: ${exists ? '✅' : '❌'}`;
                    if (exists && typeof initialized === 'boolean') {
                        report += ` (${initialized ? '已初始化' : '未初始化'})`;
                    }
                    report += '\n';
                });

                updateResults('game-status', 'info', report);

            } catch (error) {
                updateResults('game-status', 'error', `❌ 检查游戏状态失败: ${error.message}`);
            }
        }

        function checkAllDependencies() {
            let report = '🔍 完整依赖检查:\n\n';

            // 检查当前页面的依赖
            report += '=== 当前页面依赖 ===\n';
            const currentDeps = [
                'window', 'document', 'localStorage', 'indexedDB'
            ];
            currentDeps.forEach(dep => {
                const exists = window[dep] !== undefined;
                report += `${dep}: ${exists ? '✅' : '❌'}\n`;
            });

            // 检查游戏环境依赖
            if (gameFrame) {
                try {
                    const gameWindow = gameFrame.contentWindow;
                    report += '\n=== 游戏环境依赖 ===\n';
                    
                    const gameDeps = [
                        'storageService', 'userManager', 'userSystemAdapter',
                        'userManagementUI', 'StorageKeySchema', 'UserDataValidator',
                        'UserDataStructure', 'userSwitchManager'
                    ];
                    
                    gameDeps.forEach(dep => {
                        const exists = gameWindow[dep] !== undefined;
                        report += `${dep}: ${exists ? '✅' : '❌'}\n`;
                    });

                } catch (error) {
                    report += `\n❌ 无法访问游戏环境: ${error.message}\n`;
                }
            } else {
                report += '\n⚠️ 游戏环境未加载\n';
            }

            updateResults('dependency-results', 'info', report);
        }

        function checkUserSystemComponents() {
            if (!gameFrame) {
                updateResults('dependency-results', 'error', '❌ 请先加载游戏环境');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 用户系统组件检查:\n\n';

                // 检查用户系统适配器
                if (gameWindow.userSystemAdapter) {
                    const adapter = gameWindow.userSystemAdapter;
                    report += '用户系统适配器:\n';
                    report += `  存在: ✅\n`;
                    report += `  已初始化: ${adapter.initialized ? '✅' : '❌'}\n`;
                    
                    if (adapter.initialized) {
                        try {
                            const userManager = adapter.getUserManager();
                            const storageService = adapter.getUserStorageService();
                            report += `  用户管理器: ${userManager ? '✅' : '❌'}\n`;
                            report += `  存储服务: ${storageService ? '✅' : '❌'}\n`;
                        } catch (error) {
                            report += `  获取服务失败: ❌ ${error.message}\n`;
                        }
                    }
                } else {
                    report += '用户系统适配器: ❌ 不存在\n';
                }

                // 检查用户管理界面
                if (gameWindow.userManagementUI) {
                    const ui = gameWindow.userManagementUI;
                    report += '\n用户管理界面:\n';
                    report += `  存在: ✅\n`;
                    report += `  已初始化: ${ui.initialized ? '✅' : '❌'}\n`;
                    report += `  用户管理器: ${ui.userManager ? '✅' : '❌'}\n`;
                } else {
                    report += '\n用户管理界面: ❌ 不存在\n';
                }

                updateResults('dependency-results', 'info', report);

            } catch (error) {
                updateResults('dependency-results', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        function checkStorageServices() {
            if (!gameFrame) {
                updateResults('dependency-results', 'error', '❌ 请先加载游戏环境');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 存储服务检查:\n\n';

                // 检查基础存储服务
                if (gameWindow.storageService) {
                    const storage = gameWindow.storageService;
                    report += '基础存储服务:\n';
                    report += `  存在: ✅\n`;
                    report += `  已初始化: ${storage.initialized ? '✅' : '❌'}\n`;
                    report += `  存储类型: ${storage.storageType || '未知'}\n`;
                } else {
                    report += '基础存储服务: ❌ 不存在\n';
                }

                // 检查用户存储服务
                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                    try {
                        const userStorage = gameWindow.userSystemAdapter.getUserStorageService();
                        report += '\n用户存储服务:\n';
                        report += `  存在: ${userStorage ? '✅' : '❌'}\n`;
                        if (userStorage) {
                            report += `  基础存储: ${userStorage.baseStorage ? '✅' : '❌'}\n`;
                        }
                    } catch (error) {
                        report += `\n用户存储服务: ❌ 获取失败 - ${error.message}\n`;
                    }
                } else {
                    report += '\n用户存储服务: ⚠️ 用户系统适配器未初始化\n';
                }

                updateResults('dependency-results', 'info', report);

            } catch (error) {
                updateResults('dependency-results', 'error', `❌ 检查失败: ${error.message}`);
            }
        }

        function testUserManagerAccess() {
            if (!gameFrame) {
                updateResults('user-manager-results', 'error', '❌ 请先加载游戏环境');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 用户管理器访问测试:\n\n';

                // 测试直接访问
                report += '直接访问测试:\n';
                report += `  window.userManager: ${gameWindow.userManager ? '✅' : '❌'}\n`;

                // 测试通过适配器访问
                if (gameWindow.userSystemAdapter) {
                    try {
                        const userManager = gameWindow.userSystemAdapter.getUserManager();
                        report += `  通过适配器获取: ${userManager ? '✅' : '❌'}\n`;
                        
                        if (userManager) {
                            report += `  管理器类型: ${userManager.constructor.name}\n`;
                            report += `  已初始化: ${userManager.initialized ? '✅' : '❌'}\n`;
                        }
                    } catch (error) {
                        report += `  通过适配器获取: ❌ ${error.message}\n`;
                    }
                } else {
                    report += '  用户系统适配器: ❌ 不存在\n';
                }

                // 测试用户管理界面的访问
                if (gameWindow.userManagementUI) {
                    const ui = gameWindow.userManagementUI;
                    report += `\n用户管理界面访问:\n`;
                    report += `  界面存在: ✅\n`;
                    report += `  用户管理器引用: ${ui.userManager ? '✅' : '❌'}\n`;
                    
                    if (ui.userManager) {
                        report += `  管理器类型: ${ui.userManager.constructor.name}\n`;
                    }
                }

                updateResults('user-manager-results', 'info', report);

            } catch (error) {
                updateResults('user-manager-results', 'error', `❌ 测试失败: ${error.message}`);
            }
        }

        function testUserManagerMethods() {
            if (!gameFrame) {
                updateResults('user-manager-results', 'error', '❌ 请先加载游戏环境');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 用户管理器方法测试:\n\n';

                let userManager = null;

                // 获取用户管理器
                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                    userManager = gameWindow.userSystemAdapter.getUserManager();
                } else if (gameWindow.userManager) {
                    userManager = gameWindow.userManager;
                }

                if (!userManager) {
                    report += '❌ 无法获取用户管理器\n';
                    updateResults('user-manager-results', 'error', report);
                    return;
                }

                report += '用户管理器方法检查:\n';
                const methods = ['createUser', 'userExists', 'loadUserCore', 'saveUserList'];
                methods.forEach(method => {
                    const exists = typeof userManager[method] === 'function';
                    report += `  ${method}: ${exists ? '✅' : '❌'}\n`;
                });

                // 测试基础方法调用
                report += '\n基础方法测试:\n';
                try {
                    const users = userManager.users;
                    report += `  获取用户列表: ✅ (${users ? users.size : 0} 个用户)\n`;
                } catch (error) {
                    report += `  获取用户列表: ❌ ${error.message}\n`;
                }

                try {
                    const currentUser = userManager.currentUser;
                    report += `  获取当前用户: ✅ (${currentUser ? currentUser.displayName : '无'})\n`;
                } catch (error) {
                    report += `  获取当前用户: ❌ ${error.message}\n`;
                }

                updateResults('user-manager-results', 'info', report);

            } catch (error) {
                updateResults('user-manager-results', 'error', `❌ 测试失败: ${error.message}`);
            }
        }

        function simulateUserCreation() {
            if (!gameFrame) {
                updateResults('user-manager-results', 'error', '❌ 请先加载游戏环境');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = '🔍 模拟用户创建测试:\n\n';

                let userManager = null;

                // 获取用户管理器
                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                    userManager = gameWindow.userSystemAdapter.getUserManager();
                } else if (gameWindow.userManager) {
                    userManager = gameWindow.userManager;
                }

                if (!userManager) {
                    report += '❌ 无法获取用户管理器\n';
                    updateResults('user-manager-results', 'error', report);
                    return;
                }

                // 检查依赖项
                report += '依赖项检查:\n';
                const deps = ['StorageKeySchema', 'UserDataValidator', 'UserDataStructure'];
                let allDepsOk = true;
                
                deps.forEach(dep => {
                    const exists = gameWindow[dep] !== undefined;
                    report += `  ${dep}: ${exists ? '✅' : '❌'}\n`;
                    if (!exists) allDepsOk = false;
                });

                if (!allDepsOk) {
                    report += '\n❌ 依赖项不完整，无法进行用户创建\n';
                    updateResults('user-manager-results', 'error', report);
                    return;
                }

                // 检查存储服务
                report += '\n存储服务检查:\n';
                if (userManager.storageService) {
                    report += `  存储服务: ✅\n`;
                    report += `  存储类型: ${userManager.storageService.storageType || '未知'}\n`;
                } else {
                    report += `  存储服务: ❌ 不存在\n`;
                    updateResults('user-manager-results', 'error', report);
                    return;
                }

                report += '\n✅ 所有依赖项检查通过，可以尝试用户创建\n';
                updateResults('user-manager-results', 'success', report);

            } catch (error) {
                updateResults('user-manager-results', 'error', `❌ 模拟测试失败: ${error.message}\n${error.stack}`);
            }
        }

        async function performActualUserCreation() {
            const identifier = document.getElementById('test-identifier').value.trim();
            const displayName = document.getElementById('test-display-name').value.trim();

            if (!identifier || !displayName) {
                updateResults('creation-test-results', 'error', '❌ 请填写用户标识符和显示名称');
                return;
            }

            if (!gameFrame) {
                updateResults('creation-test-results', 'error', '❌ 请先加载游戏环境');
                return;
            }

            try {
                const gameWindow = gameFrame.contentWindow;
                let report = `🧪 实际用户创建测试:\n标识符: ${identifier}\n显示名称: ${displayName}\n\n`;

                // 获取用户管理器
                let userManager = null;
                if (gameWindow.userSystemAdapter && gameWindow.userSystemAdapter.initialized) {
                    userManager = gameWindow.userSystemAdapter.getUserManager();
                } else if (gameWindow.userManager) {
                    userManager = gameWindow.userManager;
                }

                if (!userManager) {
                    report += '❌ 无法获取用户管理器\n';
                    updateResults('creation-test-results', 'error', report);
                    return;
                }

                report += '开始创建用户...\n';
                updateResults('creation-test-results', 'info', report);

                // 执行用户创建
                const newUser = await userManager.createUser(identifier, displayName);
                
                report += `✅ 用户创建成功!\n`;
                report += `用户ID: ${newUser.identifier}\n`;
                report += `显示名称: ${newUser.displayName}\n`;
                report += `创建时间: ${new Date(newUser.createdAt).toLocaleString()}\n`;

                updateResults('creation-test-results', 'success', report);

            } catch (error) {
                let report = `❌ 用户创建失败:\n`;
                report += `错误信息: ${error.message}\n`;
                if (error.stack) {
                    report += `错误堆栈:\n${error.stack}\n`;
                }
                updateResults('creation-test-results', 'error', report);
            }
        }

        function testUserCreationInGame() {
            if (!gameFrame) {
                updateResults('creation-test-results', 'error', '❌ 请先加载游戏环境');
                return;
            }

            updateResults('creation-test-results', 'info', 
                '🎮 在游戏中测试用户创建:\n\n' +
                '1. 在游戏界面中点击"用户管理"按钮\n' +
                '2. 填写用户信息并点击"创建用户"\n' +
                '3. 观察浏览器控制台的详细日志\n' +
                '4. 检查是否有错误信息\n\n' +
                '💡 建议同时打开浏览器开发者工具查看详细信息'
            );
        }

        function clearAllUserData() {
            if (confirm('确定要清除所有用户数据吗？这将删除所有用户和相关数据。')) {
                try {
                    localStorage.clear();
                    if (gameFrame) {
                        gameFrame.contentWindow.localStorage.clear();
                    }
                    updateResults('creation-test-results', 'success', '✅ 所有用户数据已清除');
                } catch (error) {
                    updateResults('creation-test-results', 'error', `❌ 清除数据失败: ${error.message}`);
                }
            }
        }

        function captureConsoleErrors() {
            let report = '📊 控制台错误收集:\n\n';
            
            if (errorLog.length === 0) {
                report += '✅ 当前没有捕获到错误\n';
            } else {
                report += `捕获到 ${errorLog.length} 个错误:\n\n`;
                errorLog.forEach((error, index) => {
                    report += `错误 ${index + 1}:\n`;
                    report += `  类型: ${error.type}\n`;
                    report += `  消息: ${error.message}\n`;
                    report += `  时间: ${error.timestamp}\n`;
                    if (error.filename) {
                        report += `  文件: ${error.filename}:${error.lineno}:${error.colno}\n`;
                    }
                    if (error.error) {
                        report += `  堆栈: ${error.error}\n`;
                    }
                    report += '\n';
                });
            }

            updateResults('error-log-results', errorLog.length > 0 ? 'error' : 'success', report);
        }

        function checkNetworkRequests() {
            updateResults('error-log-results', 'info', 
                '🌐 网络请求检查:\n\n' +
                '请打开浏览器开发者工具的Network标签页，\n' +
                '然后尝试创建用户，观察是否有:\n' +
                '1. 失败的HTTP请求\n' +
                '2. 超时的请求\n' +
                '3. 404或500错误\n' +
                '4. CORS错误\n\n' +
                '💡 大多数用户创建操作应该是本地存储，\n' +
                '   不应该有网络请求失败'
            );
        }

        function generateDiagnosticReport() {
            let report = '📋 完整诊断报告\n';
            report += '='.repeat(50) + '\n\n';
            
            report += `生成时间: ${new Date().toLocaleString()}\n`;
            report += `浏览器: ${navigator.userAgent}\n`;
            report += `页面URL: ${window.location.href}\n\n`;
            
            report += '=== 环境检查 ===\n';
            report += `localStorage支持: ${typeof Storage !== 'undefined' ? '✅' : '❌'}\n`;
            report += `IndexedDB支持: ${typeof indexedDB !== 'undefined' ? '✅' : '❌'}\n`;
            report += `游戏环境加载: ${gameFrame ? '✅' : '❌'}\n\n`;
            
            if (errorLog.length > 0) {
                report += '=== 错误日志 ===\n';
                errorLog.forEach((error, index) => {
                    report += `${index + 1}. [${error.type}] ${error.message}\n`;
                });
                report += '\n';
            }
            
            report += '=== 建议的修复步骤 ===\n';
            report += '1. 检查浏览器控制台的详细错误信息\n';
            report += '2. 确认所有依赖项正确加载\n';
            report += '3. 验证存储服务初始化状态\n';
            report += '4. 测试用户管理器的方法调用\n';
            report += '5. 检查网络连接和CORS设置\n';

            updateResults('error-log-results', 'info', report);
        }

        function updateResults(elementId, type, content) {
            const element = document.getElementById(elementId);
            element.className = `results ${type}`;
            element.textContent = content;
            element.style.display = 'block';
        }

        // 页面加载完成后自动开始检查
        document.addEventListener('DOMContentLoaded', () => {
            updateResults('game-status', 'info', '🔍 用户创建功能诊断工具已就绪\n\n请按顺序执行以下步骤:\n1. 加载游戏环境\n2. 检查系统依赖\n3. 测试用户管理器\n4. 执行实际创建测试');
        });
    </script>
</body>
</html>
