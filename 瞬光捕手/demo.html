<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>瞬光捕手 - 三大功能演示</title>
    
    <!-- 样式文件 -->
    <link rel="stylesheet" href="styles/credential-manager.css">
    <link rel="stylesheet" href="styles/difficulty-selector.css">
    <link rel="stylesheet" href="styles/difficulty-leaderboard.css">
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }
        
        .demo-header {
            text-align: center;
            margin-bottom: 50px;
        }
        
        .demo-header h1 {
            font-size: 3rem;
            margin-bottom: 20px;
            text-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }
        
        .demo-header p {
            font-size: 1.2rem;
            opacity: 0.9;
            max-width: 600px;
            margin: 0 auto;
            line-height: 1.6;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-bottom: 50px;
        }
        
        .feature-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            padding: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .feature-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .feature-description {
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.6;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .feature-button {
            width: 100%;
            background: linear-gradient(135deg, #FFC107 0%, #FF9800 100%);
            color: white;
            border: none;
            padding: 15px 25px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .feature-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 193, 7, 0.3);
        }
        
        .status-panel {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin-bottom: 30px;
        }
        
        .status-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }
        
        .status-item {
            text-align: center;
            padding: 15px;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
        }
        
        .status-label {
            font-size: 0.9rem;
            color: rgba(255, 255, 255, 0.7);
            margin-bottom: 5px;
        }
        
        .status-value {
            font-size: 1.2rem;
            font-weight: 600;
            color: #FFC107;
        }
        
        .demo-actions {
            text-align: center;
            margin-top: 40px;
        }
        
        .demo-button {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 1px solid rgba(255, 255, 255, 0.3);
            padding: 12px 25px;
            border-radius: 8px;
            margin: 0 10px;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 1rem;
        }
        
        .demo-button:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }
        
        .console-output {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .console-line {
            margin-bottom: 5px;
            opacity: 0.8;
        }
        
        .console-line.success {
            color: #4CAF50;
        }
        
        .console-line.error {
            color: #F44336;
        }
        
        .console-line.info {
            color: #2196F3;
        }
        
        @media (max-width: 768px) {
            .demo-header h1 {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <!-- 头部 -->
        <div class="demo-header">
            <h1>🎮 瞬光捕手</h1>
            <p>体验三大核心功能：云存储用户凭证系统、难度选择器UI组件、排行榜难度分类系统</p>
        </div>
        
        <!-- 系统状态面板 -->
        <div class="status-panel">
            <div class="status-title">📊 系统状态</div>
            <div class="status-grid">
                <div class="status-item">
                    <div class="status-label">用户凭证系统</div>
                    <div class="status-value" id="credentialStatus">初始化中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">难度配置系统</div>
                    <div class="status-value" id="difficultyStatus">初始化中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">排行榜系统</div>
                    <div class="status-value" id="leaderboardStatus">初始化中...</div>
                </div>
                <div class="status-item">
                    <div class="status-label">当前用户</div>
                    <div class="status-value" id="currentUser">未登录</div>
                </div>
                <div class="status-item">
                    <div class="status-label">当前难度</div>
                    <div class="status-value" id="currentDifficulty">普通</div>
                </div>
                <div class="status-item">
                    <div class="status-label">排行榜条目</div>
                    <div class="status-value" id="leaderboardCount">0</div>
                </div>
            </div>
        </div>
        
        <!-- 功能卡片 -->
        <div class="features-grid">
            <!-- 用户凭证系统 -->
            <div class="feature-card">
                <div class="feature-icon">🔐</div>
                <div class="feature-title">云存储用户凭证系统</div>
                <div class="feature-description">
                    支持多种登录方式：用户名+后缀、邮箱验证、设备指纹、匿名模式。
                    提供用户友好的身份识别和多设备数据同步功能。
                </div>
                <button class="feature-button" onclick="showCredentialManager()">
                    管理用户凭证
                </button>
            </div>
            
            <!-- 难度选择器 -->
            <div class="feature-card">
                <div class="feature-icon">🎯</div>
                <div class="feature-title">难度选择器UI组件</div>
                <div class="feature-description">
                    6个难度等级可选，每个难度有独特的视觉主题。
                    实时显示难度参数和特性，支持智能推荐。
                </div>
                <button class="feature-button" onclick="showDifficultySelector()">
                    选择游戏难度
                </button>
            </div>
            
            <!-- 排行榜系统 -->
            <div class="feature-card">
                <div class="feature-icon">🏆</div>
                <div class="feature-title">排行榜难度分类系统</div>
                <div class="feature-description">
                    按难度等级分类的排行榜，支持多种排行类别。
                    提供跨难度比较和完整的统计分析功能。
                </div>
                <button class="feature-button" onclick="showLeaderboard()">
                    查看排行榜
                </button>
            </div>
        </div>
        
        <!-- 演示操作 -->
        <div class="demo-actions">
            <button class="demo-button" onclick="simulateGameplay()">🎮 模拟游戏</button>
            <button class="demo-button" onclick="generateTestData()">📊 生成测试数据</button>
            <button class="demo-button" onclick="exportData()">💾 导出数据</button>
            <button class="demo-button" onclick="resetAllData()">🔄 重置数据</button>
        </div>
        
        <!-- 控制台输出 -->
        <div class="console-output" id="consoleOutput">
            <div class="console-line info">🚀 系统正在初始化...</div>
        </div>
    </div>
    
    <!-- 核心脚本 -->
    <script src="js/config/difficulty-config.js"></script>
    <script src="js/core/user-credential-system.js"></script>
    <script src="js/core/difficulty-leaderboard-manager.js"></script>
    
    <!-- UI组件 -->
    <script src="js/ui/credential-manager-ui.js"></script>
    <script src="js/ui/difficulty-selector.js"></script>
    <script src="js/ui/difficulty-leaderboard-ui.js"></script>
    
    <!-- 演示脚本 -->
    <script>
        // 模拟存储服务
        window.storageService = {
            data: new Map(),
            
            async init() {
                console.log('📦 存储服务已初始化');
                return true;
            },
            
            async get(key, defaultValue = null) {
                return this.data.get(key) || defaultValue;
            },
            
            async set(key, value) {
                this.data.set(key, value);
                return true;
            },
            
            async remove(key) {
                return this.data.delete(key);
            },
            
            async clear() {
                this.data.clear();
                return true;
            },
            
            async list(prefix = '') {
                return Array.from(this.data.keys()).filter(key => key.startsWith(prefix));
            }
        };
        
        // 控制台输出函数
        function addConsoleLog(message, type = 'info') {
            const console = document.getElementById('consoleOutput');
            const line = document.createElement('div');
            line.className = `console-line ${type}`;
            line.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
            console.appendChild(line);
            console.scrollTop = console.scrollHeight;
        }
        
        // 更新状态显示
        function updateStatus() {
            // 用户凭证状态
            const credentialStatus = document.getElementById('credentialStatus');
            if (window.userCredentialSystem && window.userCredentialSystem.initialized) {
                credentialStatus.textContent = '✅ 已就绪';
                credentialStatus.style.color = '#4CAF50';
            } else {
                credentialStatus.textContent = '❌ 未就绪';
                credentialStatus.style.color = '#F44336';
            }
            
            // 难度配置状态
            const difficultyStatus = document.getElementById('difficultyStatus');
            if (window.difficultyConfigManager && window.difficultyConfigManager.initialized) {
                difficultyStatus.textContent = '✅ 已就绪';
                difficultyStatus.style.color = '#4CAF50';
            } else {
                difficultyStatus.textContent = '❌ 未就绪';
                difficultyStatus.style.color = '#F44336';
            }
            
            // 排行榜状态
            const leaderboardStatus = document.getElementById('leaderboardStatus');
            if (window.difficultyLeaderboardManager && window.difficultyLeaderboardManager.initialized) {
                leaderboardStatus.textContent = '✅ 已就绪';
                leaderboardStatus.style.color = '#4CAF50';
            } else {
                leaderboardStatus.textContent = '❌ 未就绪';
                leaderboardStatus.style.color = '#F44336';
            }
            
            // 当前用户
            const currentUser = document.getElementById('currentUser');
            if (window.userCredentialSystem) {
                const credential = userCredentialSystem.getCurrentCredential();
                if (credential) {
                    currentUser.textContent = credential.displayName;
                    currentUser.style.color = '#4CAF50';
                } else {
                    currentUser.textContent = '未登录';
                    currentUser.style.color = '#F44336';
                }
            }
            
            // 当前难度
            const currentDifficulty = document.getElementById('currentDifficulty');
            if (window.difficultyConfigManager) {
                const difficulty = difficultyConfigManager.getCurrentDifficulty();
                const difficultyInfo = difficultyConfigManager.getCurrentDifficultyInfo();
                if (difficultyInfo) {
                    currentDifficulty.textContent = difficultyInfo.name;
                    currentDifficulty.style.color = difficultyInfo.color;
                }
            }
        }
        
        // 功能函数
        function showCredentialManager() {
            if (window.credentialManagerUI) {
                credentialManagerUI.show();
                addConsoleLog('显示用户凭证管理界面', 'info');
            } else {
                addConsoleLog('用户凭证管理器未加载', 'error');
            }
        }
        
        function showDifficultySelector() {
            if (window.difficultySelectorUI) {
                difficultySelectorUI.show();
                addConsoleLog('显示难度选择器界面', 'info');
            } else {
                addConsoleLog('难度选择器未加载', 'error');
            }
        }
        
        function showLeaderboard() {
            if (window.difficultyLeaderboardUI) {
                difficultyLeaderboardUI.show();
                addConsoleLog('显示排行榜界面', 'info');
            } else {
                addConsoleLog('排行榜界面未加载', 'error');
            }
        }
        
        async function simulateGameplay() {
            try {
                addConsoleLog('开始模拟游戏...', 'info');
                
                // 确保有用户凭证
                let credential = userCredentialSystem.getCurrentCredential();
                if (!credential) {
                    credential = await userCredentialSystem.createAnonymousCredential();
                    addConsoleLog(`创建匿名用户: ${credential.displayName}`, 'success');
                }
                
                // 模拟游戏结果
                const gameResult = {
                    score: Math.floor(Math.random() * 20000) + 1000,
                    combo: Math.floor(Math.random() * 50) + 10,
                    perfectHits: Math.floor(Math.random() * 30) + 5,
                    accuracy: Math.random() * 40 + 60,
                    duration: Math.floor(Math.random() * 60000) + 30000
                };
                
                // 提交到排行榜
                const difficulty = difficultyConfigManager.getCurrentDifficulty();
                const result = await difficultyLeaderboardManager.submitScore(
                    difficulty,
                    'high_score',
                    {
                        ...gameResult,
                        playerId: credential.identifier,
                        playerName: credential.displayName
                    }
                );
                
                if (result.success) {
                    addConsoleLog(`游戏完成! 分数: ${gameResult.score}, 排名: #${result.rank}`, 'success');
                } else {
                    addConsoleLog(`提交分数失败: ${result.error}`, 'error');
                }
                
                updateStatus();
            } catch (error) {
                addConsoleLog(`模拟游戏失败: ${error.message}`, 'error');
            }
        }
        
        async function generateTestData() {
            try {
                addConsoleLog('生成测试数据...', 'info');
                
                const difficulties = ['beginner', 'easy', 'normal', 'hard', 'expert', 'master'];
                const categories = ['high_score', 'combo_record', 'perfect_hits'];
                
                for (const difficulty of difficulties) {
                    for (const category of categories) {
                        for (let i = 0; i < 5; i++) {
                            await difficultyLeaderboardManager.submitScore(
                                difficulty,
                                category,
                                {
                                    score: Math.floor(Math.random() * 15000) + 1000,
                                    playerId: `test_${difficulty}_${i}`,
                                    playerName: `测试玩家${i}`,
                                    combo: Math.floor(Math.random() * 40) + 10,
                                    perfectHits: Math.floor(Math.random() * 25) + 5
                                }
                            );
                        }
                    }
                }
                
                addConsoleLog('测试数据生成完成', 'success');
                updateStatus();
            } catch (error) {
                addConsoleLog(`生成测试数据失败: ${error.message}`, 'error');
            }
        }
        
        function exportData() {
            try {
                const exportData = difficultyLeaderboardManager.exportLeaderboardData();
                const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
                    type: 'application/json' 
                });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'game_data_export.json';
                a.click();
                URL.revokeObjectURL(url);
                
                addConsoleLog('数据导出完成', 'success');
            } catch (error) {
                addConsoleLog(`数据导出失败: ${error.message}`, 'error');
            }
        }
        
        async function resetAllData() {
            try {
                await storageService.clear();
                addConsoleLog('所有数据已重置', 'success');
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } catch (error) {
                addConsoleLog(`重置数据失败: ${error.message}`, 'error');
            }
        }
        
        // 初始化系统
        async function initializeSystems() {
            try {
                addConsoleLog('开始初始化系统...', 'info');
                
                // 初始化存储服务
                await storageService.init();
                addConsoleLog('存储服务初始化完成', 'success');
                
                // 初始化难度配置管理器
                await difficultyConfigManager.init();
                addConsoleLog('难度配置管理器初始化完成', 'success');
                
                // 初始化用户凭证系统
                await userCredentialSystem.init();
                addConsoleLog('用户凭证系统初始化完成', 'success');
                
                // 初始化难度排行榜管理器
                await difficultyLeaderboardManager.init();
                addConsoleLog('难度排行榜管理器初始化完成', 'success');
                
                addConsoleLog('🎉 所有系统初始化完成!', 'success');
                updateStatus();
                
                // 定期更新状态
                setInterval(updateStatus, 5000);
                
            } catch (error) {
                addConsoleLog(`系统初始化失败: ${error.message}`, 'error');
            }
        }
        
        // 监听事件
        window.addEventListener('credentialChanged', (event) => {
            const { newCredential } = event.detail;
            if (newCredential) {
                addConsoleLog(`用户凭证已更新: ${newCredential.displayName}`, 'success');
            } else {
                addConsoleLog('用户已注销', 'info');
            }
            updateStatus();
        });
        
        window.addEventListener('difficultyChanged', (event) => {
            const { newDifficulty, difficultyInfo } = event.detail;
            addConsoleLog(`难度已更改为: ${difficultyInfo.name}`, 'success');
            updateStatus();
        });
        
        // 页面加载完成后初始化
        window.addEventListener('DOMContentLoaded', initializeSystems);
    </script>
</body>
</html>
