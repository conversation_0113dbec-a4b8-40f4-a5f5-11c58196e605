<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>EdgeOne 云存储部署示例 - 瞬光捕手</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: #333;
            line-height: 1.6;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #4a5568;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .section {
            margin: 30px 0;
            padding: 20px;
            border-left: 4px solid #667eea;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 20px;
            border-radius: 8px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
        
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        
        .status.info { background: #bee3f8; color: #2b6cb0; }
        .status.success { background: #c6f6d5; color: #2f855a; }
        .status.warning { background: #fef5e7; color: #c05621; }
        .status.error { background: #fed7d7; color: #c53030; }
        
        .btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.3s;
        }
        
        .btn:hover {
            background: #5a67d8;
        }
        
        .btn.secondary {
            background: #718096;
        }
        
        .btn.secondary:hover {
            background: #4a5568;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 EdgeOne 云存储部署示例</h1>
        
        <div class="section">
            <h2>📋 部署说明</h2>
            <p>本页面演示了如何在实际部署环境中正确配置和启用 EdgeOne 云存储功能。</p>
            
            <div class="status info">
                <strong>重要提示：</strong>这是一个部署配置示例，展示了如何在生产环境中注入 EdgeOne 存储服务。
            </div>
        </div>

        <div class="section">
            <h2>🔧 部署配置代码</h2>
            <p>在实际部署时，您需要在游戏加载前添加以下配置代码：</p>
            
            <div class="code-block">
&lt;!-- 在游戏主页面的 &lt;head&gt; 部分添加 --&gt;
&lt;script&gt;
// EdgeOne 云存储部署配置
(async function() {
    console.log('🚀 开始 EdgeOne 云存储部署配置...');
    
    // 等待 EdgeOne 存储初始化器加载
    function waitForInitializer() {
        return new Promise((resolve) => {
            const checkInitializer = () => {
                if (typeof window.initEdgeOneStorage === 'function') {
                    resolve();
                } else {
                    setTimeout(checkInitializer, 100);
                }
            };
            checkInitializer();
        });
    }
    
    // 等待页面加载完成
    if (document.readyState === 'loading') {
        await new Promise(resolve => {
            document.addEventListener('DOMContentLoaded', resolve);
        });
    }
    
    // 等待初始化器可用
    await waitForInitializer();
    
    // 配置 EdgeOne 云存储
    const deploymentConfig = {
        // 生产环境 API URL（替换为您的实际域名）
        productionApiUrl: 'https://your-game-domain.com',
        
        // 自定义配置
        customConfig: {
            // 启用连接测试
            testConnection: true,
            
            // 生产环境优化配置
            timeout: 15000,
            retryCount: 3,
            retryDelay: 1500,
            
            // 启用缓存和降级
            enableCache: true,
            enableFallback: true
        }
    };
    
    // 初始化 EdgeOne 云存储
    try {
        const success = await window.initEdgeOneStorage(deploymentConfig);
        if (success) {
            console.log('✅ EdgeOne 云存储部署配置成功');
        } else {
            console.warn('⚠️ EdgeOne 云存储配置失败，将使用本地存储');
        }
    } catch (error) {
        console.error('❌ EdgeOne 云存储配置错误:', error);
    }
})();
&lt;/script&gt;
            </div>
        </div>

        <div class="section">
            <h2>🎮 测试部署配置</h2>
            <p>点击下面的按钮来测试 EdgeOne 云存储的部署配置：</p>
            
            <button class="btn" onclick="testDeploymentConfig()">🧪 测试部署配置</button>
            <button class="btn secondary" onclick="showStorageStatus()">📊 查看存储状态</button>
            <button class="btn secondary" onclick="testStorageOperations()">🔄 测试存储操作</button>
            
            <div id="testResults"></div>
        </div>

        <div class="section">
            <h2>📝 部署检查清单</h2>
            <div id="deploymentChecklist">
                <div class="status info">正在检查部署环境...</div>
            </div>
        </div>

        <div class="section">
            <h2>🔍 故障排除</h2>
            <p>如果 EdgeOne 云存储无法正常工作，请检查以下项目：</p>
            <ul>
                <li>✅ 确保 Cloudflare Functions 已正确部署</li>
                <li>✅ 检查 API 端点是否可访问</li>
                <li>✅ 验证 CORS 配置是否正确</li>
                <li>✅ 确认网络连接正常</li>
                <li>✅ 检查浏览器控制台是否有错误信息</li>
            </ul>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/utils/edgeOneStorageImpl.js"></script>
    <script src="js/config/edge-one-storage-init.js"></script>

    <script>
        // 测试部署配置
        async function testDeploymentConfig() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="status info">🧪 正在测试部署配置...</div>';
            
            try {
                // 模拟部署配置
                const deploymentConfig = {
                    // 使用相对路径进行测试
                    customConfig: {
                        testConnection: true,
                        timeout: 10000,
                        enableCache: true,
                        enableFallback: true
                    }
                };
                
                // 测试初始化
                if (typeof window.initEdgeOneStorage === 'function') {
                    const success = await window.initEdgeOneStorage(deploymentConfig);
                    
                    if (success) {
                        resultsDiv.innerHTML = '<div class="status success">✅ 部署配置测试成功！EdgeOne 云存储已就绪。</div>';
                    } else {
                        resultsDiv.innerHTML = '<div class="status warning">⚠️ 部署配置测试完成，但云存储不可用，已切换到本地存储模式。</div>';
                    }
                } else {
                    resultsDiv.innerHTML = '<div class="status error">❌ EdgeOne 存储初始化器未找到，请检查脚本加载。</div>';
                }
                
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ 部署配置测试失败: ${error.message}</div>`;
            }
        }
        
        // 显示存储状态
        function showStorageStatus() {
            const resultsDiv = document.getElementById('testResults');
            
            if (typeof window.getEdgeOneStorageStatus === 'function') {
                const status = window.getEdgeOneStorageStatus();
                resultsDiv.innerHTML = `
                    <div class="status info">
                        <h4>📊 EdgeOne 存储状态</h4>
                        <p><strong>初始化状态:</strong> ${status.initialized ? '✅ 已初始化' : '❌ 未初始化'}</p>
                        <p><strong>可用性:</strong> ${status.available ? '✅ 可用' : '❌ 不可用'}</p>
                        <p><strong>存储模式:</strong> ${status.mode}</p>
                        <p><strong>请求计数:</strong> ${status.requestCount || 0}</p>
                        <p><strong>环境:</strong> ${status.environment || '未知'}</p>
                    </div>
                `;
            } else {
                resultsDiv.innerHTML = '<div class="status warning">⚠️ EdgeOne 存储状态查询功能不可用</div>';
            }
        }
        
        // 测试存储操作
        async function testStorageOperations() {
            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="status info">🔄 正在测试存储操作...</div>';
            
            try {
                if (window.edgeOneStorage) {
                    // 测试基本操作
                    const testKey = `deployment_test_${Date.now()}`;
                    const testValue = { message: '部署测试数据', timestamp: Date.now() };
                    
                    await window.edgeOneStorage.put(testKey, testValue);
                    const retrievedValue = await window.edgeOneStorage.get(testKey);
                    await window.edgeOneStorage.delete(testKey);
                    
                    if (JSON.stringify(retrievedValue) === JSON.stringify(testValue)) {
                        resultsDiv.innerHTML = '<div class="status success">✅ 存储操作测试成功！数据读写正常。</div>';
                    } else {
                        resultsDiv.innerHTML = '<div class="status error">❌ 存储操作测试失败：数据不匹配。</div>';
                    }
                } else {
                    resultsDiv.innerHTML = '<div class="status warning">⚠️ EdgeOne 存储实例不可用，请先初始化。</div>';
                }
            } catch (error) {
                resultsDiv.innerHTML = `<div class="status error">❌ 存储操作测试失败: ${error.message}</div>`;
            }
        }
        
        // 页面加载时执行部署检查
        window.addEventListener('load', function() {
            setTimeout(checkDeploymentEnvironment, 1000);
        });
        
        // 检查部署环境
        function checkDeploymentEnvironment() {
            const checklistDiv = document.getElementById('deploymentChecklist');
            const checks = [];
            
            // 检查脚本加载
            checks.push({
                name: 'EdgeOneStorageImpl 类',
                status: typeof EdgeOneStorageImpl !== 'undefined',
                message: typeof EdgeOneStorageImpl !== 'undefined' ? '✅ 已加载' : '❌ 未加载'
            });
            
            checks.push({
                name: 'EdgeOne 初始化器',
                status: typeof window.initEdgeOneStorage === 'function',
                message: typeof window.initEdgeOneStorage === 'function' ? '✅ 已加载' : '❌ 未加载'
            });
            
            checks.push({
                name: '存储状态查询',
                status: typeof window.getEdgeOneStorageStatus === 'function',
                message: typeof window.getEdgeOneStorageStatus === 'function' ? '✅ 可用' : '❌ 不可用'
            });
            
            // 生成检查结果
            let html = '<h4>🔍 部署环境检查结果</h4>';
            checks.forEach(check => {
                const statusClass = check.status ? 'success' : 'error';
                html += `<div class="status ${statusClass}"><strong>${check.name}:</strong> ${check.message}</div>`;
            });
            
            checklistDiv.innerHTML = html;
        }
    </script>
</body>
</html>
