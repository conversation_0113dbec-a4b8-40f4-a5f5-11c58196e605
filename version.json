{"version": "1.0.0", "build_time": "2025-08-07T14:36:37.908481", "build_timestamp": 1754577397, "build_date": "2025-08-07", "build_time_formatted": "2025-08-07 14:36:37", "git": {"commit_hash": "f150089450c9d2266b43bb734c23f3241f60554f", "commit_hash_short": "f1500894", "branch": "main", "commit_date": "2025-08-07 13:30:31 +0000", "commit_message": "修复 EdgeOneStorageImpl 文件加载 404 错误", "tag": null, "is_dirty": true, "remote_url": "*****************:aier/split-second-spark.git"}, "build_environment": {"platform": "Linux-6.8.12-4-pve-x86_64-with-glibc2.36", "system": "Linux", "release": "6.8.12-4-pve", "version": "#1 SMP PREEMPT_DYNAMIC PMX 6.8.12-4 (2024-11-06T15:04Z)", "machine": "x86_64", "processor": "", "python_version": "3.11.2", "python_implementation": "CPython", "hostname": "dev", "user": "root", "build_tool": "Python Version Generator", "cwd": "/root/workspace/git.atjog.com/aier/split-second-spark"}, "project": {"name": "Split-Second Spark", "description": "捕捉决定性瞬间，引燃无限可能", "type": "Web Game Collection", "games": ["时空织梦者", "瞬光捕手", "量子共鸣者"]}, "meta": {"generator": "VersionGenerator", "generator_version": "1.0.0", "format_version": "1.0", "encoding": "utf-8"}}