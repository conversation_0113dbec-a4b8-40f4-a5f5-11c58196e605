# Split-Second Spark 版本信息系统说明文档

## 📋 概述

本文档详细介绍了 Split-Second Spark 项目中的版本信息自动生成和显示系统。该系统能够在构建过程中自动生成包含版本号、构建时间、Git信息和环境信息的版本文件，并提供在运行时读取和显示版本信息的功能。

## 🏗️ 系统架构

### 核心组件

1. **版本信息生成器** (`version_generator.py`)
   - 自动从项目配置文件读取版本号
   - 获取Git仓库信息（提交哈希、分支、状态等）
   - 收集构建环境信息
   - 生成多种格式的版本文件

2. **构建脚本集成** (`build-static.py`)
   - 在打包过程中自动调用版本生成器
   - 确保版本文件被正确包含在部署包中
   - 更新部署信息以包含版本数据

3. **前端版本模块** (`js/version.js`)
   - 自动生成的JavaScript模块
   - 提供版本信息访问接口
   - 支持浏览器和Node.js环境

4. **版本显示组件** (`js/utils/version-display.js`)
   - 提供可视化版本信息显示
   - 支持模态框、按钮等多种显示方式
   - 包含复制功能和响应式设计

## 📁 文件结构

```
项目根目录/
├── version_generator.py          # 版本信息生成器
├── build-static.py              # 构建脚本（已集成版本生成）
├── version.json                 # JSON格式版本信息
├── version.txt                  # 文本格式版本信息
├── js/
│   ├── version.js              # JavaScript版本模块
│   └── utils/
│       └── version-display.js  # 版本显示组件
└── dist/                       # 构建输出目录
    ├── version.json
    ├── version.txt
    └── js/
        ├── version.js
        └── utils/
            └── version-display.js
```

## 🚀 使用方法

### 1. 生成版本信息

#### 方法一：通过构建脚本（推荐）
```bash
# 执行完整构建，自动生成版本信息
python build-static.py
```

#### 方法二：单独生成版本信息
```bash
# 生成所有格式的版本文件
python version_generator.py

# 只生成JSON格式
python version_generator.py --json-only

# 指定输出目录
python version_generator.py --output dist

# 查看帮助
python version_generator.py --help
```

### 2. 在前端使用版本信息

#### 方法一：使用自动生成的JavaScript模块
```html
<!-- 引入版本模块 -->
<script src="js/version.js"></script>

<script>
// 获取版本信息
const versionInfo = VersionInfo.getVersionInfo();
console.log('版本号:', VersionInfo.getVersion());
console.log('构建时间:', VersionInfo.getBuildTime());
console.log('Git提交:', VersionInfo.getCommitHashShort());

// 显示版本信息到控制台
VersionInfo.logVersionInfo();

// 格式化版本字符串
const versionString = VersionInfo.formatVersionString();
console.log(versionString); // 例如: v1.0.0 (a1b2c3d4) - 2025-08-07
</script>
```

#### 方法二：使用版本显示组件
```html
<!-- 引入版本显示组件 -->
<script src="js/utils/version-display.js"></script>

<script>
// 自动添加版本按钮（默认行为）
// 用户可以点击按钮查看详细版本信息

// 手动显示版本模态框
versionDisplay.showVersionModal();

// 创建自定义版本按钮
const button = versionDisplay.createVersionButton({
    text: '关于',
    style: { bottom: '10px', left: '10px' }
});
document.body.appendChild(button);

// 获取简短版本字符串
const version = versionDisplay.getVersionString();
document.title = `Split-Second Spark ${version}`;
</script>
```

#### 方法三：直接读取JSON文件
```javascript
// 异步加载版本信息
async function loadVersionInfo() {
    try {
        const response = await fetch('/version.json');
        const versionInfo = await response.json();
        
        console.log('版本信息:', versionInfo);
        return versionInfo;
    } catch (error) {
        console.error('加载版本信息失败:', error);
        return null;
    }
}

// 使用示例
loadVersionInfo().then(info => {
    if (info) {
        document.getElementById('version').textContent = info.version;
        document.getElementById('build-time').textContent = info.build_time_formatted;
    }
});
```

## 📊 版本信息格式

### JSON格式 (version.json)
```json
{
  "version": "1.0.0",
  "build_time": "2025-08-07T10:30:45.123456",
  "build_timestamp": 1691401845,
  "build_date": "2025-08-07",
  "build_time_formatted": "2025-08-07 10:30:45",
  "git": {
    "commit_hash": "a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0",
    "commit_hash_short": "a1b2c3d4",
    "branch": "main",
    "commit_date": "2025-08-07 10:25:30 +0800",
    "commit_message": "添加版本信息系统",
    "tag": "v1.0.0",
    "is_dirty": false,
    "remote_url": "https://github.com/user/split-second-spark.git"
  },
  "build_environment": {
    "platform": "Linux-5.4.0-74-generic-x86_64-with-glibc2.31",
    "system": "Linux",
    "python_version": "3.11.2",
    "hostname": "build-server",
    "user": "developer",
    "build_tool": "Python Version Generator"
  },
  "project": {
    "name": "Split-Second Spark",
    "description": "捕捉决定性瞬间，引燃无限可能",
    "type": "Web Game Collection",
    "games": ["时空织梦者", "瞬光捕手", "量子共鸣者"]
  },
  "meta": {
    "generator": "VersionGenerator",
    "generator_version": "1.0.0",
    "format_version": "1.0",
    "encoding": "utf-8"
  }
}
```

### 文本格式 (version.txt)
```
Split-Second Spark 版本信息
==================================================

基本信息:
  版本号: 1.0.0
  构建时间: 2025-08-07 10:30:45
  构建日期: 2025-08-07

Git信息:
  提交哈希: a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0
  短哈希: a1b2c3d4
  分支: main
  提交日期: 2025-08-07 10:25:30 +0800
  提交消息: 添加版本信息系统
  最近标签: v1.0.0
  工作区状态: 干净
  远程仓库: https://github.com/user/split-second-spark.git

构建环境:
  操作系统: Linux-5.4.0-74-generic-x86_64-with-glibc2.31
  Python版本: 3.11.2
  主机名: build-server
  用户: developer
  构建工具: Python Version Generator

项目信息:
  名称: Split-Second Spark
  描述: 捕捉决定性瞬间，引燃无限可能
  类型: Web Game Collection
  游戏列表: 时空织梦者, 瞬光捕手, 量子共鸣者

==================================================
生成时间: 2025-08-07 10:30:45
生成器: VersionGenerator v1.0.0
```

## ⚙️ 配置选项

### 版本生成器配置

版本生成器会自动从以下位置读取版本号：
1. `manifest.json` 文件中的 `version` 字段
2. 如果未找到，使用默认版本号 `1.0.0`

### 构建脚本配置

在 `build-static.py` 中，版本文件会被自动包含在构建输出中：
- `version.json` - JSON格式版本信息
- `version.txt` - 文本格式版本信息  
- `js/version.js` - JavaScript模块
- `js/utils/version-display.js` - 版本显示组件

### 前端显示配置

版本显示组件支持以下配置：

```javascript
// 禁用自动版本按钮
// 在URL中添加 ?version=false 参数

// 自定义版本按钮样式
const button = versionDisplay.createVersionButton({
    text: '版本 v1.0.0',
    className: 'custom-version-btn',
    style: {
        bottom: '10px',
        left: '10px',
        background: '#333',
        color: '#fff'
    }
});
```

## 🔧 高级用法

### 自定义版本信息显示

```javascript
// 创建自定义版本信息显示
class CustomVersionDisplay {
    constructor() {
        this.loadVersionInfo();
    }
    
    async loadVersionInfo() {
        const response = await fetch('/version.json');
        this.versionInfo = await response.json();
        this.updateUI();
    }
    
    updateUI() {
        // 更新页面标题
        document.title = `${this.versionInfo.project.name} v${this.versionInfo.version}`;
        
        // 更新页脚版本信息
        const footer = document.querySelector('.footer-version');
        if (footer) {
            footer.textContent = `v${this.versionInfo.version} (${this.versionInfo.git.commit_hash_short})`;
        }
        
        // 在控制台显示构建信息
        console.group('🏗️ 构建信息');
        console.log('版本:', this.versionInfo.version);
        console.log('构建时间:', this.versionInfo.build_time_formatted);
        console.log('Git提交:', this.versionInfo.git.commit_hash_short);
        console.log('是否为开发版本:', this.versionInfo.git.is_dirty);
        console.groupEnd();
    }
}

// 初始化自定义版本显示
new CustomVersionDisplay();
```

### 版本信息API集成

```javascript
// 将版本信息发送到分析服务
function reportVersionInfo() {
    if (window.VersionInfo) {
        const info = VersionInfo.getVersionInfo();
        
        // 发送到分析服务
        analytics.track('app_version', {
            version: info.version,
            commit: info.git.commit_hash_short,
            build_date: info.build_date,
            is_development: info.git.is_dirty
        });
    }
}

// 页面加载时报告版本信息
document.addEventListener('DOMContentLoaded', reportVersionInfo);
```

## 🐛 故障排除

### 常见问题

1. **版本信息未生成**
   - 确保 `version_generator.py` 文件存在且可执行
   - 检查Python环境和依赖
   - 查看构建日志中的错误信息

2. **Git信息为空**
   - 确保项目在Git仓库中
   - 检查Git命令是否可用
   - 确保有至少一个提交记录

3. **前端无法加载版本信息**
   - 检查 `version.json` 文件是否存在
   - 确保Web服务器可以访问版本文件
   - 检查浏览器控制台中的网络错误

4. **版本按钮不显示**
   - 确保引入了 `version-display.js`
   - 检查URL参数是否包含 `?version=false`
   - 查看浏览器控制台中的JavaScript错误

### 调试方法

```javascript
// 启用详细日志
localStorage.setItem('version-debug', 'true');

// 手动检查版本信息加载
console.log('VersionInfo available:', typeof window.VersionInfo !== 'undefined');
console.log('versionDisplay available:', typeof window.versionDisplay !== 'undefined');

// 测试版本信息加载
fetch('/version.json')
    .then(response => response.json())
    .then(data => console.log('Version data:', data))
    .catch(error => console.error('Failed to load version:', error));
```

## 📝 更新日志

### v1.0.0 (2025-08-07)
- ✨ 初始版本发布
- 🚀 支持自动版本信息生成
- 🎨 提供多种格式输出（JSON、文本、JavaScript）
- 📱 包含响应式版本信息显示组件
- 🔧 集成到构建流程中
- 📚 完整的中文文档和使用示例

---

## 📞 技术支持

如果您在使用过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查浏览器控制台中的错误信息
3. 确认所有依赖文件都已正确部署
4. 验证Web服务器配置是否正确

**🎯 享受版本管理的便利！**
