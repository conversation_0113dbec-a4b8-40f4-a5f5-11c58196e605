<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Split-Second Spark - 完整存储系统演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.8em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .system-status {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online { background: #51cf66; }
        .status-offline { background: #ff6b6b; }
        .status-warning { background: #ffd43b; }

        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-section h2 {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #4ecdc4;
        }

        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .control-group {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .control-group h3 {
            color: #ff6b6b;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .form-group input, .form-group select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
        }

        .btn-success {
            background: linear-gradient(45deg, #51cf66, #40c057);
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffd43b, #fab005);
        }

        .log-area {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-info { color: #4ecdc4; }
        .log-success { color: #51cf66; }
        .log-warning { color: #ffd43b; }
        .log-error { color: #ff6b6b; }

        .stats-display {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .mode-selector {
            display: flex;
            gap: 10px;
            margin-bottom: 20px;
        }

        .mode-btn {
            flex: 1;
            padding: 10px;
            border: 2px solid rgba(255, 255, 255, 0.3);
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .mode-btn.active {
            border-color: #4ecdc4;
            background: rgba(76, 205, 196, 0.2);
        }

        .mode-btn:hover {
            border-color: #ff6b6b;
        }

        @media (max-width: 768px) {
            .control-panel {
                grid-template-columns: 1fr;
            }
            
            .status-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🚀 完整存储系统</h1>
            <p>用户认证 + 数据存储 + 跨设备同步的完整解决方案</p>
        </div>

        <!-- 系统状态 -->
        <div class="system-status">
            <h2>📊 系统状态</h2>
            <div class="status-grid" id="systemStatus">
                <div class="status-card">
                    <h3>🔐 用户认证</h3>
                    <p><span class="status-indicator status-offline"></span>未初始化</p>
                </div>
                <div class="status-card">
                    <h3>💾 存储系统</h3>
                    <p><span class="status-indicator status-offline"></span>未初始化</p>
                </div>
                <div class="status-card">
                    <h3>☁️ 云端同步</h3>
                    <p><span class="status-indicator status-offline"></span>未连接</p>
                </div>
                <div class="status-card">
                    <h3>👤 当前用户</h3>
                    <p>未登录</p>
                </div>
            </div>
        </div>

        <!-- 存储模式选择 -->
        <div class="demo-section">
            <h2>🔄 存储模式</h2>
            <div class="mode-selector">
                <div class="mode-btn active" data-mode="local">
                    📱 本地存储
                    <small>仅本设备</small>
                </div>
                <div class="mode-btn" data-mode="cloud">
                    ☁️ 云端存储
                    <small>跨设备同步</small>
                </div>
                <div class="mode-btn" data-mode="hybrid">
                    🔄 混合模式
                    <small>本地+云端</small>
                </div>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="demo-section">
            <h2>🎮 操作控制</h2>
            <div class="control-panel">
                <!-- 系统控制 -->
                <div class="control-group">
                    <h3>🔧 系统控制</h3>
                    <button class="btn" onclick="initSystem()">初始化系统</button>
                    <button class="btn btn-secondary" onclick="refreshStatus()">刷新状态</button>
                    <button class="btn btn-warning" onclick="resetSystem()">重置系统</button>
                </div>

                <!-- 用户认证 -->
                <div class="control-group">
                    <h3>👤 用户认证</h3>
                    <button class="btn" onclick="signInAsGuest()">访客模式</button>
                    <button class="btn" onclick="signInAnonymously()">匿名登录</button>
                    <div class="form-group">
                        <input type="email" id="userEmail" placeholder="邮箱地址">
                    </div>
                    <div class="form-group">
                        <input type="password" id="userPassword" placeholder="密码">
                    </div>
                    <button class="btn" onclick="signInWithEmail()">邮箱登录</button>
                    <button class="btn btn-secondary" onclick="signOut()">登出</button>
                </div>

                <!-- 数据操作 -->
                <div class="control-group">
                    <h3>💾 数据操作</h3>
                    <div class="form-group">
                        <input type="text" id="dataKey" placeholder="数据键名">
                    </div>
                    <div class="form-group">
                        <input type="text" id="dataValue" placeholder="数据值">
                    </div>
                    <button class="btn" onclick="saveData()">保存数据</button>
                    <button class="btn btn-secondary" onclick="loadData()">读取数据</button>
                    <button class="btn btn-warning" onclick="deleteData()">删除数据</button>
                    <button class="btn btn-success" onclick="generateTestData()">生成测试数据</button>
                </div>
            </div>
        </div>

        <!-- 存储统计 -->
        <div class="demo-section">
            <h2>📈 存储统计</h2>
            <div class="stats-display" id="storageStats">
                <p>请先初始化系统查看统计信息</p>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="demo-section">
            <h2>📝 操作日志</h2>
            <div class="log-area" id="logArea">
                <div class="log-entry log-info">🚀 完整存储系统演示已准备就绪</div>
            </div>
            <button class="btn btn-secondary" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script type="module">
        // 全局变量
        let storageSystem = null;
        let isInitialized = false;

        // Firebase 配置（示例配置，实际使用时需要替换）
        const firebaseConfig = {
            apiKey: "demo-api-key",
            authDomain: "split-second-spark-demo.firebaseapp.com",
            projectId: "split-second-spark-demo",
            storageBucket: "split-second-spark-demo.appspot.com",
            messagingSenderId: "123456789",
            appId: "demo-app-id"
        };

        // 初始化系统
        window.initSystem = async function() {
            if (isInitialized) {
                log('⚠️ 系统已初始化', 'warning');
                return;
            }

            try {
                log('🚀 开始初始化完整存储系统...', 'info');
                
                // 动态导入完整存储系统
                const { default: CompleteStorageSystem } = await import('./js/examples/complete-storage-integration.js');
                
                // 创建存储系统实例
                storageSystem = new CompleteStorageSystem(firebaseConfig, {
                    enableGuestMode: true,
                    enableAnonymousAuth: true,
                    enableEmailAuth: true,
                    autoUpgrade: true,
                    dataEncryption: false,
                    compressionEnabled: true,
                    enableDataVersioning: true
                });
                
                // 设置事件监听器
                setupEventListeners();
                
                // 初始化系统
                await storageSystem.init();
                
                isInitialized = true;
                log('✅ 完整存储系统初始化成功', 'success');
                
                // 刷新状态显示
                refreshStatus();
                
            } catch (error) {
                log(`❌ 系统初始化失败: ${error.message}`, 'error');
                console.error('初始化错误:', error);
            }
        };

        // 设置事件监听器
        function setupEventListeners() {
            // 系统初始化完成
            window.addEventListener('storageSystem:initialized', (event) => {
                const { storageMode, user } = event.detail;
                log(`🎉 存储系统初始化完成，模式: ${storageMode}`, 'success');
                updateSystemStatus();
            });

            // 存储模式变更
            window.addEventListener('storageSystem:modeChanged', (event) => {
                const { previousMode, currentMode, user } = event.detail;
                log(`🔄 存储模式已切换: ${previousMode} → ${currentMode}`, 'info');
                updateModeSelector(currentMode);
                updateSystemStatus();
            });

            // 数据操作事件
            window.addEventListener('storageSystem:dataSaved', (event) => {
                const { key, value, storageMode } = event.detail;
                log(`💾 数据已保存 [${storageMode}]: ${key} = ${JSON.stringify(value)}`, 'success');
                refreshStorageStats();
            });

            window.addEventListener('storageSystem:dataLoaded', (event) => {
                const { key, value, storageMode } = event.detail;
                log(`📥 数据已读取 [${storageMode}]: ${key} = ${JSON.stringify(value)}`, 'info');
            });

            window.addEventListener('storageSystem:dataDeleted', (event) => {
                const { key, storageMode } = event.detail;
                log(`🗑️ 数据已删除 [${storageMode}]: ${key}`, 'warning');
                refreshStorageStats();
            });

            // 模式选择器点击事件
            document.querySelectorAll('.mode-btn').forEach(btn => {
                btn.addEventListener('click', async () => {
                    const mode = btn.dataset.mode;
                    await switchStorageMode(mode);
                });
            });
        }

        // 用户认证方法
        window.signInAsGuest = async function() {
            if (!storageSystem) {
                log('❌ 系统未初始化', 'error');
                return;
            }

            try {
                log('👥 以访客身份登录...', 'info');
                const user = await storageSystem.signInAsGuest();
                log(`✅ 访客登录成功: ${user.uid}`, 'success');
                updateSystemStatus();
            } catch (error) {
                log(`❌ 访客登录失败: ${error.message}`, 'error');
            }
        };

        window.signInAnonymously = async function() {
            if (!storageSystem) {
                log('❌ 系统未初始化', 'error');
                return;
            }

            try {
                log('🎭 匿名登录中...', 'info');
                const user = await storageSystem.signInAnonymously();
                log(`✅ 匿名登录成功: ${user.uid}`, 'success');
                updateSystemStatus();
            } catch (error) {
                log(`❌ 匿名登录失败: ${error.message}`, 'error');
            }
        };

        window.signInWithEmail = async function() {
            const email = document.getElementById('userEmail').value.trim();
            const password = document.getElementById('userPassword').value.trim();

            if (!email || !password) {
                log('❌ 请输入邮箱和密码', 'error');
                return;
            }

            if (!storageSystem) {
                log('❌ 系统未初始化', 'error');
                return;
            }

            try {
                log('🔐 邮箱登录中...', 'info');
                const user = await storageSystem.signInWithEmail(email, password);
                log(`✅ 邮箱登录成功: ${user.displayName || user.uid}`, 'success');
                
                // 清空表单
                document.getElementById('userEmail').value = '';
                document.getElementById('userPassword').value = '';
                
                updateSystemStatus();
            } catch (error) {
                log(`❌ 邮箱登录失败: ${error.message}`, 'error');
            }
        };

        window.signOut = async function() {
            if (!storageSystem) {
                log('❌ 系统未初始化', 'error');
                return;
            }

            try {
                log('👤 登出中...', 'info');
                await storageSystem.signOut();
                log('✅ 已登出', 'success');
                updateSystemStatus();
            } catch (error) {
                log(`❌ 登出失败: ${error.message}`, 'error');
            }
        };

        // 数据操作方法
        window.saveData = async function() {
            const key = document.getElementById('dataKey').value.trim();
            const value = document.getElementById('dataValue').value.trim();

            if (!key || !value) {
                log('❌ 请输入数据键名和值', 'error');
                return;
            }

            if (!storageSystem) {
                log('❌ 系统未初始化', 'error');
                return;
            }

            try {
                await storageSystem.saveData(key, value);
                
                // 清空表单
                document.getElementById('dataKey').value = '';
                document.getElementById('dataValue').value = '';
                
            } catch (error) {
                log(`❌ 数据保存失败: ${error.message}`, 'error');
            }
        };

        window.loadData = async function() {
            const key = document.getElementById('dataKey').value.trim();

            if (!key) {
                log('❌ 请输入数据键名', 'error');
                return;
            }

            if (!storageSystem) {
                log('❌ 系统未初始化', 'error');
                return;
            }

            try {
                const value = await storageSystem.loadData(key);
                if (value !== null) {
                    document.getElementById('dataValue').value = typeof value === 'string' ? value : JSON.stringify(value);
                } else {
                    log(`⚠️ 未找到数据: ${key}`, 'warning');
                }
            } catch (error) {
                log(`❌ 数据读取失败: ${error.message}`, 'error');
            }
        };

        window.deleteData = async function() {
            const key = document.getElementById('dataKey').value.trim();

            if (!key) {
                log('❌ 请输入数据键名', 'error');
                return;
            }

            if (!storageSystem) {
                log('❌ 系统未初始化', 'error');
                return;
            }

            try {
                await storageSystem.deleteData(key);
                document.getElementById('dataValue').value = '';
            } catch (error) {
                log(`❌ 数据删除失败: ${error.message}`, 'error');
            }
        };

        window.generateTestData = async function() {
            if (!storageSystem) {
                log('❌ 系统未初始化', 'error');
                return;
            }

            const testData = {
                'player-name': '测试玩家',
                'player-level': '25',
                'player-score': '50000',
                'game-settings': JSON.stringify({
                    volume: 0.8,
                    difficulty: 'expert',
                    language: 'zh-CN'
                }),
                'achievements': JSON.stringify(['first_win', 'speed_demon', 'perfectionist'])
            };

            try {
                for (const [key, value] of Object.entries(testData)) {
                    await storageSystem.saveData(key, value);
                }
                log(`🎲 已生成 ${Object.keys(testData).length} 条测试数据`, 'success');
            } catch (error) {
                log(`❌ 测试数据生成失败: ${error.message}`, 'error');
            }
        };

        // 存储模式切换
        async function switchStorageMode(mode) {
            if (!storageSystem) {
                log('❌ 系统未初始化', 'error');
                return;
            }

            try {
                log(`🔄 切换存储模式到: ${mode}`, 'info');
                await storageSystem.switchStorageMode(mode);
            } catch (error) {
                log(`❌ 存储模式切换失败: ${error.message}`, 'error');
            }
        }

        // 更新模式选择器
        function updateModeSelector(currentMode) {
            document.querySelectorAll('.mode-btn').forEach(btn => {
                btn.classList.toggle('active', btn.dataset.mode === currentMode);
            });
        }

        // 更新系统状态显示
        function updateSystemStatus() {
            if (!storageSystem) return;

            const status = storageSystem.getSystemStatus();
            const statusCards = document.querySelectorAll('.status-card');

            // 用户认证状态
            const authCard = statusCards[0];
            const authIndicator = authCard.querySelector('.status-indicator');
            const authText = authCard.querySelector('p');
            
            if (status.userManager.initialized) {
                authIndicator.className = 'status-indicator status-online';
                authText.innerHTML = '<span class="status-indicator status-online"></span>已初始化';
            } else {
                authIndicator.className = 'status-indicator status-warning';
                authText.innerHTML = '<span class="status-indicator status-warning"></span>本地模式';
            }

            // 存储系统状态
            const storageCard = statusCards[1];
            const storageIndicator = storageCard.querySelector('.status-indicator');
            const storageText = storageCard.querySelector('p');
            
            if (status.isInitialized) {
                storageIndicator.className = 'status-indicator status-online';
                storageText.innerHTML = `<span class="status-indicator status-online"></span>${status.storageMode}模式`;
            } else {
                storageIndicator.className = 'status-indicator status-offline';
                storageText.innerHTML = '<span class="status-indicator status-offline"></span>未初始化';
            }

            // 云端同步状态
            const cloudCard = statusCards[2];
            const cloudIndicator = cloudCard.querySelector('.status-indicator');
            const cloudText = cloudCard.querySelector('p');
            
            if (status.availableStorageModes.cloud) {
                cloudIndicator.className = 'status-indicator status-online';
                cloudText.innerHTML = '<span class="status-indicator status-online"></span>已连接';
            } else {
                cloudIndicator.className = 'status-indicator status-offline';
                cloudText.innerHTML = '<span class="status-indicator status-offline"></span>未连接';
            }

            // 当前用户状态
            const userCard = statusCards[3];
            const userText = userCard.querySelector('p');
            
            if (status.currentUser) {
                const user = status.currentUser;
                const userType = user.isGuest ? '访客' : (user.isAnonymous ? '匿名用户' : '注册用户');
                userText.textContent = `${user.displayName || user.uid} (${userType})`;
            } else {
                userText.textContent = '未登录';
            }

            // 更新模式选择器
            updateModeSelector(status.storageMode);
        }

        // 刷新状态
        window.refreshStatus = function() {
            updateSystemStatus();
            refreshStorageStats();
            log('🔄 状态已刷新', 'info');
        };

        // 刷新存储统计
        async function refreshStorageStats() {
            if (!storageSystem) {
                document.getElementById('storageStats').innerHTML = '<p>请先初始化系统查看统计信息</p>';
                return;
            }

            try {
                const stats = await storageSystem.getStorageStats();
                const statsContainer = document.getElementById('storageStats');
                
                statsContainer.innerHTML = `
                    <h3>📊 存储统计信息</h3>
                    <p><strong>存储类型:</strong> ${stats.type || '未知'}</p>
                    <p><strong>数据条数:</strong> ${stats.keyCount || 0}</p>
                    <p><strong>状态:</strong> ${stats.isAvailable ? '可用' : '不可用'}</p>
                    <p><strong>当前模式:</strong> ${stats.systemStatus?.storageMode || '未知'}</p>
                    ${stats.user ? `<p><strong>用户:</strong> ${stats.user.displayName || stats.user.uid}</p>` : ''}
                    ${stats.error ? `<p style="color: #ff6b6b;"><strong>错误:</strong> ${stats.error}</p>` : ''}
                `;
            } catch (error) {
                document.getElementById('storageStats').innerHTML = `<p style="color: #ff6b6b;">统计信息获取失败: ${error.message}</p>`;
            }
        }

        // 重置系统
        window.resetSystem = function() {
            if (storageSystem) {
                storageSystem.destroy();
                storageSystem = null;
            }
            
            isInitialized = false;
            updateSystemStatus();
            
            document.getElementById('storageStats').innerHTML = '<p>请先初始化系统查看统计信息</p>';
            log('🔄 系统已重置', 'warning');
        };

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(message);
        }

        // 清空日志
        window.clearLog = function() {
            document.getElementById('logArea').innerHTML = '';
        };

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 完整存储系统演示页面已加载', 'info');
            updateSystemStatus();
        });
    </script>
</body>
</html>
