# Split-Second Spark 技术架构说明

> 详细的技术架构、模块设计和实现原理

## 📋 目录

- [整体架构](#整体架构)
- [核心模块](#核心模块)
- [存储系统](#存储系统)
- [国际化系统](#国际化系统)
- [游戏引擎](#游戏引擎)
- [性能优化](#性能优化)
- [安全机制](#安全机制)

## 🏗️ 整体架构

### 系统架构图
```
┌─────────────────────────────────────────────────────────────┐
│                    Split-Second Spark                        │
│                     游戏选择界面                              │
├─────────────────────────────────────────────────────────────┤
│  主界面层 (Main Interface Layer)                             │
│  ├── 游戏选择器 (Game Selector)                              │
│  ├── 设置管理器 (Settings Manager)                           │
│  ├── 模态框管理器 (Modal Manager)                            │
│  └── 语言切换器 (Language Switcher)                          │
├─────────────────────────────────────────────────────────────┤
│  核心服务层 (Core Services Layer)                            │
│  ├── 存储服务 (Storage Service)                              │
│  ├── 国际化服务 (i18n Service)                               │
│  ├── 游戏启动器 (Game Launcher)                              │
│  └── 事件管理器 (Event Manager)                              │
├─────────────────────────────────────────────────────────────┤
│  游戏引擎层 (Game Engine Layer)                              │
│  ├── 量子共鸣者引擎                                           │
│  │   ├── 音频引擎 (Audio Engine)                             │
│  │   ├── 物理引擎 (Physics Engine)                           │
│  │   ├── 量子引擎 (Quantum Engine)                           │
│  │   └── 渲染引擎 (Render Engine)                            │
│  ├── 瞬光捕手引擎                                             │
│  │   ├── 游戏引擎 (Game Engine)                              │
│  │   ├── 关卡管理器 (Level Manager)                          │
│  │   ├── 玩家管理器 (Player Manager)                         │
│  │   └── 输入处理器 (Input Handler)                          │
│  └── 时空织梦者引擎                                           │
│      ├── 时间引擎 (Time Engine)                              │
│      ├── 梦境编织器 (Dream Weaver)                           │
│      ├── 物理引擎 (Physics Engine)                           │
│      └── 渲染引擎 (Render Engine)                            │
├─────────────────────────────────────────────────────────────┤
│  数据存储层 (Data Storage Layer)                             │
│  ├── IndexedDB (首选)                                        │
│  ├── localStorage (备选)                                     │
│  └── Memory Storage (临时)                                   │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈
- **前端框架**: 原生JavaScript (ES6+)
- **图形渲染**: Canvas 2D API
- **音频处理**: Web Audio API
- **数据存储**: IndexedDB / localStorage
- **样式框架**: 原生CSS3 + Flexbox + Grid
- **构建工具**: 无需构建，直接运行
- **部署方式**: 静态文件部署

### 设计原则
1. **模块化设计**: 每个功能模块独立，便于维护和扩展
2. **统一接口**: 相同功能的不同实现使用统一接口
3. **渐进增强**: 基础功能优先，高级功能可选
4. **性能优先**: 优化关键路径，确保流畅体验
5. **跨平台兼容**: 支持PC、移动端、平板等多种设备

## 🔧 核心模块

### 1. 存储服务 (Storage Service)

#### 架构设计
```javascript
/**
 * 统一存储服务接口
 * 支持多种后端存储方案的自动切换
 */
class StorageService {
    constructor() {
        this.backend = this.selectBestBackend();
        this.cache = new Map(); // 内存缓存
    }
    
    /**
     * 自动选择最佳存储后端
     * 优先级: IndexedDB > localStorage > Memory
     */
    selectBestBackend() {
        if (this.isIndexedDBAvailable()) {
            return new IndexedDBBackend();
        } else if (this.isLocalStorageAvailable()) {
            return new LocalStorageBackend();
        } else {
            return new MemoryBackend();
        }
    }
    
    // 统一接口方法
    async put(key, value) { /* 实现 */ }
    async get(key) { /* 实现 */ }
    async delete(key) { /* 实现 */ }
    async list(prefix) { /* 实现 */ }
    async clear() { /* 实现 */ }
}
```

#### 后端实现
```javascript
// IndexedDB后端 - 大容量，支持复杂数据
class IndexedDBBackend {
    constructor() {
        this.dbName = 'SplitSecondSpark';
        this.version = 1;
        this.db = null;
    }
    
    async init() {
        return new Promise((resolve, reject) => {
            const request = indexedDB.open(this.dbName, this.version);
            
            request.onerror = () => reject(request.error);
            request.onsuccess = () => {
                this.db = request.result;
                resolve();
            };
            
            request.onupgradeneeded = (event) => {
                const db = event.target.result;
                if (!db.objectStoreNames.contains('gameData')) {
                    db.createObjectStore('gameData', { keyPath: 'key' });
                }
            };
        });
    }
    
    async put(key, value) {
        const transaction = this.db.transaction(['gameData'], 'readwrite');
        const store = transaction.objectStore('gameData');
        return store.put({ key, value, timestamp: Date.now() });
    }
    
    async get(key) {
        const transaction = this.db.transaction(['gameData'], 'readonly');
        const store = transaction.objectStore('gameData');
        const result = await store.get(key);
        return result ? result.value : null;
    }
}

// localStorage后端 - 中等容量，简单键值对
class LocalStorageBackend {
    constructor() {
        this.prefix = 'sss_'; // Split-Second Spark prefix
    }
    
    async put(key, value) {
        try {
            const data = JSON.stringify({
                value,
                timestamp: Date.now()
            });
            localStorage.setItem(this.prefix + key, data);
            return true;
        } catch (error) {
            console.error('localStorage存储失败:', error);
            return false;
        }
    }
    
    async get(key) {
        try {
            const data = localStorage.getItem(this.prefix + key);
            if (!data) return null;
            
            const parsed = JSON.parse(data);
            return parsed.value;
        } catch (error) {
            console.error('localStorage读取失败:', error);
            return null;
        }
    }
}

// 内存后端 - 临时存储，页面刷新后丢失
class MemoryBackend {
    constructor() {
        this.data = new Map();
    }
    
    async put(key, value) {
        this.data.set(key, {
            value,
            timestamp: Date.now()
        });
        return true;
    }
    
    async get(key) {
        const item = this.data.get(key);
        return item ? item.value : null;
    }
}
```

### 2. 国际化服务 (i18n Service)

#### 核心实现
```javascript
/**
 * 国际化服务
 * 支持多语言切换和动态加载
 */
class I18nService {
    constructor() {
        this.currentLanguage = 'zh-CN';
        this.translations = {};
        this.fallbackLanguage = 'zh-CN';
        this.loadTranslations();
    }
    
    /**
     * 加载翻译数据
     */
    loadTranslations() {
        // 中文翻译
        this.translations['zh-CN'] = {
            'game.title': 'Split-Second Spark',
            'main.subtitle': '捕捉决定性瞬间，引燃无限可能',
            'games.title': '选择游戏',
            'games.quantum.title': '量子共鸣者',
            'games.spark.title': '瞬光捕手',
            'games.temporal.title': '时空织梦者',
            // ... 更多翻译
        };
        
        // 英文翻译
        this.translations['en-US'] = {
            'game.title': 'Split-Second Spark',
            'main.subtitle': 'Capture decisive moments, ignite infinite possibilities',
            'games.title': 'Choose Game',
            'games.quantum.title': 'Quantum Resonance',
            'games.spark.title': 'Split-Second Spark',
            'games.temporal.title': 'Temporal Dream Weaver',
            // ... 更多翻译
        };
    }
    
    /**
     * 获取翻译文本
     * @param {string} key - 翻译键
     * @param {object} params - 参数替换
     * @returns {string} 翻译后的文本
     */
    t(key, params = {}) {
        const translation = this.getTranslation(key);
        return this.interpolate(translation, params);
    }
    
    /**
     * 获取翻译
     */
    getTranslation(key) {
        const currentLangTranslations = this.translations[this.currentLanguage];
        const fallbackTranslations = this.translations[this.fallbackLanguage];
        
        return currentLangTranslations[key] || 
               fallbackTranslations[key] || 
               key;
    }
    
    /**
     * 参数插值
     */
    interpolate(text, params) {
        return text.replace(/\{\{(\w+)\}\}/g, (match, key) => {
            return params[key] || match;
        });
    }
    
    /**
     * 切换语言
     */
    async setLanguage(language) {
        if (this.translations[language]) {
            this.currentLanguage = language;
            await this.updateDOM();
            await this.saveLanguagePreference();
        }
    }
    
    /**
     * 更新DOM中的翻译文本
     */
    async updateDOM() {
        const elements = document.querySelectorAll('[data-i18n]');
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            element.textContent = this.t(key);
        });
    }
}
```

## 🎮 游戏引擎架构

### 1. 量子共鸣者引擎

#### 音频引擎 (Audio Engine)
```javascript
/**
 * 音频引擎
 * 处理音频播放、频率分析和麦克风输入
 */
class AudioEngine {
    constructor() {
        this.audioContext = null;
        this.analyser = null;
        this.microphone = null;
        this.frequencyData = null;
        this.init();
    }

    async init() {
        // 初始化音频上下文
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // 创建分析器节点
        this.analyser = this.audioContext.createAnalyser();
        this.analyser.fftSize = 2048;
        this.frequencyData = new Uint8Array(this.analyser.frequencyBinCount);

        // 请求麦克风权限
        await this.initMicrophone();
    }

    async initMicrophone() {
        try {
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            this.microphone = this.audioContext.createMediaStreamSource(stream);
            this.microphone.connect(this.analyser);
        } catch (error) {
            console.warn('麦克风初始化失败:', error);
        }
    }

    /**
     * 获取当前频率数据
     */
    getFrequencyData() {
        this.analyser.getByteFrequencyData(this.frequencyData);
        return this.frequencyData;
    }

    /**
     * 获取主要频率
     */
    getDominantFrequency() {
        const frequencyData = this.getFrequencyData();
        let maxIndex = 0;
        let maxValue = 0;

        for (let i = 0; i < frequencyData.length; i++) {
            if (frequencyData[i] > maxValue) {
                maxValue = frequencyData[i];
                maxIndex = i;
            }
        }

        // 将索引转换为频率
        const nyquist = this.audioContext.sampleRate / 2;
        return (maxIndex / frequencyData.length) * nyquist;
    }

    /**
     * 播放音效
     */
    playSound(frequency, duration = 0.1, type = 'sine') {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();

        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);

        oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
        oscillator.type = type;

        gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);

        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }
}
```

#### 量子引擎 (Quantum Engine)
```javascript
/**
 * 量子引擎
 * 处理量子粒子的共鸣、能量传播和连锁反应
 */
class QuantumEngine {
    constructor() {
        this.particles = [];
        this.quantumField = new QuantumField();
        this.resonanceThreshold = 0.1;
        this.energyDecayRate = 0.95;
    }

    /**
     * 创建量子粒子
     */
    createParticle(x, y, frequency, energy = 1.0) {
        const particle = new QuantumParticle({
            x, y, frequency, energy,
            radius: 10 + energy * 5,
            color: this.frequencyToColor(frequency)
        });

        this.particles.push(particle);
        return particle;
    }

    /**
     * 更新量子系统
     */
    update(deltaTime) {
        // 更新量子场
        this.quantumField.update(deltaTime);

        // 更新粒子
        this.particles.forEach(particle => {
            particle.update(deltaTime);
            this.updateParticleResonance(particle);
        });

        // 处理粒子间相互作用
        this.processParticleInteractions();

        // 清理死亡粒子
        this.particles = this.particles.filter(p => p.isAlive());
    }

    /**
     * 更新粒子共鸣状态
     */
    updateParticleResonance(particle) {
        const fieldStrength = this.quantumField.getStrengthAt(particle.x, particle.y);
        const resonance = this.calculateResonance(particle.frequency, this.targetFrequency);

        if (resonance > this.resonanceThreshold) {
            particle.activate(resonance * fieldStrength);
        }
    }

    /**
     * 计算频率共鸣强度
     */
    calculateResonance(freq1, freq2) {
        if (!freq2) return 0;

        const ratio = freq1 / freq2;
        const harmonics = [1, 2, 3, 4, 0.5, 1.5, 2.5]; // 谐波关系

        let maxResonance = 0;
        harmonics.forEach(harmonic => {
            const diff = Math.abs(ratio - harmonic);
            const resonance = Math.max(0, 1 - diff * 10);
            maxResonance = Math.max(maxResonance, resonance);
        });

        return maxResonance;
    }

    /**
     * 处理粒子间相互作用
     */
    processParticleInteractions() {
        for (let i = 0; i < this.particles.length; i++) {
            for (let j = i + 1; j < this.particles.length; j++) {
                const p1 = this.particles[i];
                const p2 = this.particles[j];

                const distance = Math.sqrt(
                    Math.pow(p1.x - p2.x, 2) + Math.pow(p1.y - p2.y, 2)
                );

                if (distance < p1.radius + p2.radius) {
                    this.handleParticleCollision(p1, p2);
                }

                if (p1.isActivated && distance < p1.influenceRadius) {
                    this.propagateEnergy(p1, p2, distance);
                }
            }
        }
    }

    /**
     * 能量传播
     */
    propagateEnergy(source, target, distance) {
        const energyTransfer = source.energy * (1 - distance / source.influenceRadius) * 0.1;
        target.receiveEnergy(energyTransfer);

        // 创建能量波效果
        this.createEnergyWave(source.x, source.y, target.x, target.y);
    }
}

/**
 * 量子粒子类
 */
class QuantumParticle {
    constructor(options) {
        this.x = options.x;
        this.y = options.y;
        this.frequency = options.frequency;
        this.energy = options.energy;
        this.radius = options.radius;
        this.color = options.color;

        this.isActivated = false;
        this.activationTime = 0;
        this.influenceRadius = this.radius * 3;
        this.lifespan = 10000; // 10秒
        this.age = 0;
    }

    update(deltaTime) {
        this.age += deltaTime;

        if (this.isActivated) {
            this.activationTime += deltaTime;
            this.energy *= 0.99; // 能量衰减

            // 更新影响半径
            this.influenceRadius = this.radius * (2 + Math.sin(this.activationTime * 0.01));
        }
    }

    activate(resonanceStrength) {
        this.isActivated = true;
        this.energy += resonanceStrength;
        this.activationTime = 0;

        // 发光效果
        this.glowIntensity = resonanceStrength;
    }

    receiveEnergy(amount) {
        this.energy += amount;
        if (this.energy > 0.5 && !this.isActivated) {
            this.activate(this.energy);
        }
    }

    isAlive() {
        return this.age < this.lifespan && this.energy > 0.01;
    }
}
```

### 2. 瞬光捕手引擎

#### 游戏引擎核心
```javascript
/**
 * 瞬光捕手游戏引擎
 * 处理光点生成、移动、碰撞检测和得分计算
 */
class SparkGameEngine {
    constructor(canvas) {
        this.canvas = canvas;
        this.ctx = canvas.getContext('2d');
        this.sparks = [];
        this.score = 0;
        this.combo = 0;
        this.level = 1;
        this.gameState = 'menu'; // menu, playing, paused, gameover

        this.config = {
            sparkSpawnRate: 2000,    // 光点生成间隔
            sparkSpeed: 1.0,         // 光点移动速度
            sparkLifetime: 3000,     // 光点生命周期
            perfectWindow: 100,      // 完美时机窗口
            goodWindow: 200,         // 良好时机窗口
            comboMultiplier: 1.5     // 连击倍数
        };

        this.lastSparkTime = 0;
        this.animationId = null;
    }

    /**
     * 开始游戏
     */
    start() {
        this.gameState = 'playing';
        this.score = 0;
        this.combo = 0;
        this.level = 1;
        this.sparks = [];
        this.lastSparkTime = 0;

        this.gameLoop();
    }

    /**
     * 游戏主循环
     */
    gameLoop() {
        const currentTime = performance.now();
        const deltaTime = currentTime - this.lastFrameTime || 0;
        this.lastFrameTime = currentTime;

        if (this.gameState === 'playing') {
            this.update(deltaTime);
            this.render();

            // 生成新光点
            if (currentTime - this.lastSparkTime > this.config.sparkSpawnRate) {
                this.spawnSpark();
                this.lastSparkTime = currentTime;
            }
        }

        this.animationId = requestAnimationFrame(() => this.gameLoop());
    }

    /**
     * 更新游戏状态
     */
    update(deltaTime) {
        // 更新光点
        this.sparks.forEach(spark => {
            spark.update(deltaTime);
        });

        // 移除过期光点
        this.sparks = this.sparks.filter(spark => {
            if (spark.isExpired()) {
                this.combo = 0; // 重置连击
                return false;
            }
            return true;
        });

        // 检查关卡升级
        this.checkLevelUp();
    }

    /**
     * 生成光点
     */
    spawnSpark() {
        const spark = new Spark({
            x: Math.random() * this.canvas.width,
            y: Math.random() * this.canvas.height,
            speed: this.config.sparkSpeed * (1 + this.level * 0.1),
            lifetime: this.config.sparkLifetime,
            perfectWindow: this.config.perfectWindow,
            goodWindow: this.config.goodWindow
        });

        this.sparks.push(spark);
    }

    /**
     * 处理点击事件
     */
    handleClick(x, y) {
        if (this.gameState !== 'playing') return;

        let hit = false;

        for (let i = this.sparks.length - 1; i >= 0; i--) {
            const spark = this.sparks[i];

            if (spark.isPointInside(x, y)) {
                const hitResult = spark.hit();
                this.processHit(hitResult);
                this.sparks.splice(i, 1);
                hit = true;
                break;
            }
        }

        if (!hit) {
            this.combo = 0; // 重置连击
        }
    }

    /**
     * 处理击中结果
     */
    processHit(hitResult) {
        let points = 0;

        switch (hitResult.quality) {
            case 'perfect':
                points = 100;
                break;
            case 'good':
                points = 50;
                break;
            case 'ok':
                points = 20;
                break;
        }

        // 应用连击倍数
        if (hitResult.quality !== 'miss') {
            this.combo++;
            points *= Math.pow(this.config.comboMultiplier, Math.min(this.combo - 1, 10));
        } else {
            this.combo = 0;
        }

        this.score += Math.round(points);

        // 创建得分效果
        this.createScoreEffect(hitResult.x, hitResult.y, points, hitResult.quality);
    }

    /**
     * 渲染游戏画面
     */
    render() {
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 绘制背景
        this.renderBackground();

        // 绘制光点
        this.sparks.forEach(spark => {
            spark.render(this.ctx);
        });

        // 绘制UI
        this.renderUI();
    }

    /**
     * 渲染背景
     */
    renderBackground() {
        const gradient = this.ctx.createRadialGradient(
            this.canvas.width / 2, this.canvas.height / 2, 0,
            this.canvas.width / 2, this.canvas.height / 2, this.canvas.width / 2
        );
        gradient.addColorStop(0, '#0f0f23');
        gradient.addColorStop(1, '#1a1a2e');

        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
    }

    /**
     * 渲染UI
     */
    renderUI() {
        this.ctx.fillStyle = '#ffffff';
        this.ctx.font = '24px Arial';
        this.ctx.textAlign = 'left';

        // 得分
        this.ctx.fillText(`得分: ${this.score}`, 20, 40);

        // 连击
        if (this.combo > 1) {
            this.ctx.fillStyle = '#ff6b6b';
            this.ctx.fillText(`连击: ${this.combo}x`, 20, 70);
        }

        // 关卡
        this.ctx.fillStyle = '#4ecdc4';
        this.ctx.fillText(`关卡: ${this.level}`, 20, 100);
    }
}

/**
 * 光点类
 */
class Spark {
    constructor(options) {
        this.x = options.x;
        this.y = options.y;
        this.speed = options.speed;
        this.lifetime = options.lifetime;
        this.perfectWindow = options.perfectWindow;
        this.goodWindow = options.goodWindow;

        this.age = 0;
        this.radius = 15;
        this.targetRadius = 5;
        this.phase = 'approaching'; // approaching, perfect, good, fading

        // 随机移动方向
        this.angle = Math.random() * Math.PI * 2;
        this.vx = Math.cos(this.angle) * this.speed;
        this.vy = Math.sin(this.angle) * this.speed;
    }

    update(deltaTime) {
        this.age += deltaTime;

        // 更新位置
        this.x += this.vx * deltaTime * 0.001;
        this.y += this.vy * deltaTime * 0.001;

        // 更新阶段
        this.updatePhase();

        // 更新半径
        this.updateRadius();
    }

    updatePhase() {
        const progress = this.age / this.lifetime;

        if (progress < 0.3) {
            this.phase = 'approaching';
        } else if (progress < 0.5) {
            this.phase = 'perfect';
        } else if (progress < 0.8) {
            this.phase = 'good';
        } else {
            this.phase = 'fading';
        }
    }

    updateRadius() {
        const progress = this.age / this.lifetime;
        this.radius = this.targetRadius + (15 - this.targetRadius) * (1 - progress);
    }

    render(ctx) {
        const alpha = this.phase === 'fading' ? 1 - (this.age - this.lifetime * 0.8) / (this.lifetime * 0.2) : 1;

        // 绘制光晕
        const gradient = ctx.createRadialGradient(this.x, this.y, 0, this.x, this.y, this.radius * 2);
        gradient.addColorStop(0, `rgba(255, 255, 255, ${alpha * 0.8})`);
        gradient.addColorStop(0.5, `rgba(255, 107, 107, ${alpha * 0.4})`);
        gradient.addColorStop(1, `rgba(255, 107, 107, 0)`);

        ctx.fillStyle = gradient;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius * 2, 0, Math.PI * 2);
        ctx.fill();

        // 绘制核心
        ctx.fillStyle = `rgba(255, 255, 255, ${alpha})`;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        ctx.fill();

        // 绘制时机指示器
        this.renderTimingIndicator(ctx, alpha);
    }

    renderTimingIndicator(ctx, alpha) {
        const progress = this.age / this.lifetime;
        let color = '#ffffff';

        switch (this.phase) {
            case 'perfect':
                color = '#00ff00';
                break;
            case 'good':
                color = '#ffff00';
                break;
            case 'fading':
                color = '#ff0000';
                break;
        }

        ctx.strokeStyle = `rgba(${this.hexToRgb(color).join(',')}, ${alpha * 0.6})`;
        ctx.lineWidth = 2;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius + 5, 0, Math.PI * 2 * progress);
        ctx.stroke();
    }

    hit() {
        const quality = this.getHitQuality();
        return {
            quality,
            x: this.x,
            y: this.y,
            points: this.calculatePoints(quality)
        };
    }

    getHitQuality() {
        switch (this.phase) {
            case 'perfect':
                return 'perfect';
            case 'good':
                return 'good';
            case 'approaching':
            case 'fading':
                return 'ok';
            default:
                return 'miss';
        }
    }

    isPointInside(x, y) {
        const distance = Math.sqrt(Math.pow(x - this.x, 2) + Math.pow(y - this.y, 2));
        return distance <= this.radius;
    }

    isExpired() {
        return this.age >= this.lifetime;
    }

    hexToRgb(hex) {
        const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
        return result ? [
            parseInt(result[1], 16),
            parseInt(result[2], 16),
            parseInt(result[3], 16)
        ] : [255, 255, 255];
    }
}
```
```

### 3. 游戏启动器 (Game Launcher)

#### 启动器实现
```javascript
/**
 * 游戏启动器
 * 负责游戏的启动、切换和管理
 */
class GameLauncher {
    constructor() {
        this.games = new Map();
        this.currentGame = null;
        this.registerGames();
    }
    
    /**
     * 注册所有游戏
     */
    registerGames() {
        this.games.set('quantum', {
            name: '量子共鸣者',
            path: './量子共鸣者/index.html',
            description: '音乐节奏与物理模拟结合的创新游戏',
            features: ['量子共鸣', '音乐节奏', '物理模拟'],
            requirements: {
                audio: true,
                microphone: true,
                webgl: false
            }
        });
        
        this.games.set('spark', {
            name: '瞬光捕手',
            path: './瞬光捕手/index.html',
            description: '考验反应速度和时机把握的休闲游戏',
            features: ['精准时机', '连击系统', '反应挑战'],
            requirements: {
                audio: false,
                microphone: false,
                webgl: false
            }
        });
        
        this.games.set('temporal', {
            name: '时空织梦者',
            path: './时空织梦者/index.html',
            description: '时间操控策略解谜游戏',
            features: ['时间操控', '策略解谜', '梦境编织'],
            requirements: {
                audio: false,
                microphone: false,
                webgl: false
            }
        });
    }
    
    /**
     * 启动游戏
     * @param {string} gameId - 游戏ID
     */
    async launchGame(gameId) {
        const game = this.games.get(gameId);
        if (!game) {
            throw new Error(`游戏不存在: ${gameId}`);
        }
        
        // 检查系统要求
        const compatible = await this.checkCompatibility(game);
        if (!compatible) {
            throw new Error('系统不兼容此游戏');
        }
        
        // 保存当前游戏状态
        await this.saveCurrentGameState();
        
        // 跳转到游戏页面
        window.location.href = game.path;
    }
    
    /**
     * 检查游戏兼容性
     */
    async checkCompatibility(game) {
        const requirements = game.requirements;
        
        // 检查音频支持
        if (requirements.audio && !window.AudioContext && !window.webkitAudioContext) {
            return false;
        }
        
        // 检查麦克风支持
        if (requirements.microphone && !navigator.mediaDevices) {
            return false;
        }
        
        // 检查WebGL支持
        if (requirements.webgl) {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            if (!gl) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * 预览游戏
     */
    async previewGame(gameId) {
        const game = this.games.get(gameId);
        if (!game) return null;
        
        return {
            name: game.name,
            description: game.description,
            features: game.features,
            screenshots: await this.loadScreenshots(gameId),
            compatibility: await this.checkCompatibility(game)
        };
    }
}
```

### 4. 事件管理器 (Event Manager)

#### 事件系统
```javascript
/**
 * 全局事件管理器
 * 实现发布-订阅模式，解耦模块间通信
 */
class EventManager {
    constructor() {
        this.listeners = new Map();
        this.onceListeners = new Map();
    }
    
    /**
     * 订阅事件
     * @param {string} event - 事件名称
     * @param {function} callback - 回调函数
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }
    
    /**
     * 订阅一次性事件
     */
    once(event, callback) {
        if (!this.onceListeners.has(event)) {
            this.onceListeners.set(event, []);
        }
        this.onceListeners.get(event).push(callback);
    }
    
    /**
     * 发布事件
     * @param {string} event - 事件名称
     * @param {any} data - 事件数据
     */
    emit(event, data) {
        // 触发普通监听器
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`事件处理错误 [${event}]:`, error);
                }
            });
        }
        
        // 触发一次性监听器
        if (this.onceListeners.has(event)) {
            const callbacks = this.onceListeners.get(event);
            this.onceListeners.delete(event);
            
            callbacks.forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error(`一次性事件处理错误 [${event}]:`, error);
                }
            });
        }
    }
    
    /**
     * 取消订阅
     */
    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }
}
```
