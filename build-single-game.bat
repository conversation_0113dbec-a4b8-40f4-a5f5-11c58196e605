@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

:: 设置控制台标题
title Split-Second Spark 单游戏部署打包工具

:: 显示标题
echo.
echo ============================================================
echo 🎮 Split-Second Spark 单游戏部署打包工具
echo ============================================================
echo.

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python
    echo 请先安装Python 3.6或更高版本
    echo 下载地址: https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

:: 显示Python版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo ✅ 检测到Python版本: %PYTHON_VERSION%
echo.

:: 运行打包脚本
echo 🚀 启动单游戏打包程序...
echo.
python build-single-game.py

:: 检查打包结果
if errorlevel 1 (
    echo.
    echo ❌ 打包失败！
    echo 请检查错误信息并重试
    echo.
    pause
    exit /b 1
)

:: 打包成功
echo.
echo ============================================================
echo 🎉 单游戏打包完成！
echo ============================================================
echo.

:: 询问是否打开输出目录
set /p OPEN_DIR="是否打开输出目录? (y/n): "
if /i "!OPEN_DIR!"=="y" (
    echo 📁 正在打开输出目录...
    start .
)

:: 询问是否启动本地服务器测试
echo.
set /p START_SERVER="是否启动本地服务器进行测试? (y/n): "
if /i "!START_SERVER!"=="y" (
    echo.
    echo 🌐 启动本地服务器...
    echo 服务器地址: http://localhost:8000
    echo 按 Ctrl+C 停止服务器
    echo.
    
    :: 查找独立部署目录
    for /d %%i in (*-standalone) do (
        echo 📂 进入目录: %%i
        cd "%%i"
        python -m http.server 8000
        cd ..
        goto :server_started
    )
    
    echo ❌ 未找到独立部署目录
    
    :server_started
)

echo.
echo 👋 感谢使用 Split-Second Spark 单游戏部署打包工具！
echo.
pause
