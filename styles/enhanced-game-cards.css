/**
 * Split-Second Spark - 增强游戏卡片样式
 * 提供更丰富的游戏卡片视觉效果和交互体验
 */

/* ===== 游戏卡片基础样式 ===== */
.game-card-enhanced {
    position: relative;
    background: rgba(255, 255, 255, 0.03);
    border: 1px solid rgba(255, 255, 255, 0.08);
    border-radius: 1.5rem;
    overflow: hidden;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(20px);
    cursor: pointer;
    min-height: 420px;
    display: flex;
    flex-direction: column;
    transform: translateZ(0);
}

/* 卡片悬浮效果 */
.game-card-enhanced:hover {
    transform: translateY(-12px) scale(1.02);
    border-color: rgba(255, 255, 255, 0.15);
    box-shadow: 
        0 25px 50px -12px rgba(0, 0, 0, 0.25),
        0 0 40px rgba(255, 107, 107, 0.1);
}

/* ===== 卡片背景动画系统 ===== */
.card-background-enhanced {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.6;
    transition: opacity 0.4s ease;
    overflow: hidden;
}

.game-card-enhanced:hover .card-background-enhanced {
    opacity: 0.8;
}

/* 渐变背景 */
.temporal-gradient-enhanced {
    background: linear-gradient(135deg, 
        rgba(102, 126, 234, 0.3) 0%, 
        rgba(118, 75, 162, 0.3) 50%,
        rgba(102, 126, 234, 0.3) 100%);
}

.spark-gradient-enhanced {
    background: linear-gradient(135deg, 
        rgba(255, 107, 107, 0.3) 0%, 
        rgba(255, 167, 38, 0.3) 50%,
        rgba(255, 107, 107, 0.3) 100%);
}

.quantum-gradient-enhanced {
    background: linear-gradient(135deg, 
        rgba(78, 205, 196, 0.3) 0%, 
        rgba(68, 160, 141, 0.3) 50%,
        rgba(78, 205, 196, 0.3) 100%);
}

/* ===== 动态粒子效果 ===== */
.card-particles-enhanced {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.particle-enhanced {
    position: absolute;
    width: 3px;
    height: 3px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 50%;
    animation: floatParticle 4s ease-in-out infinite;
}

.particle-enhanced:nth-child(1) {
    top: 20%;
    left: 15%;
    animation-delay: 0s;
    animation-duration: 3s;
}

.particle-enhanced:nth-child(2) {
    top: 60%;
    left: 80%;
    animation-delay: 1s;
    animation-duration: 4s;
}

.particle-enhanced:nth-child(3) {
    top: 80%;
    left: 30%;
    animation-delay: 2s;
    animation-duration: 3.5s;
}

.particle-enhanced:nth-child(4) {
    top: 30%;
    left: 70%;
    animation-delay: 1.5s;
    animation-duration: 4.5s;
}

.particle-enhanced:nth-child(5) {
    top: 50%;
    left: 20%;
    animation-delay: 0.5s;
    animation-duration: 3.8s;
}

@keyframes floatParticle {
    0%, 100% {
        transform: translateY(0px) scale(1);
        opacity: 0.7;
    }
    25% {
        transform: translateY(-15px) scale(1.2);
        opacity: 1;
    }
    50% {
        transform: translateY(-8px) scale(0.8);
        opacity: 0.8;
    }
    75% {
        transform: translateY(-20px) scale(1.1);
        opacity: 0.9;
    }
}

/* ===== 游戏特定动画效果 ===== */
/* 时空织梦者 - 时间波纹 */
.temporal-waves {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.time-ripple {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 60px;
    height: 60px;
    border: 2px solid rgba(102, 126, 234, 0.4);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: timeRipple 3s ease-out infinite;
}

.time-ripple:nth-child(2) {
    animation-delay: 1s;
}

.time-ripple:nth-child(3) {
    animation-delay: 2s;
}

@keyframes timeRipple {
    0% {
        transform: translate(-50%, -50%) scale(0.5);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(2);
        opacity: 0;
    }
}

/* 瞬光捕手 - 电光效果 */
.spark-lightning {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.lightning-bolt {
    position: absolute;
    width: 2px;
    height: 40px;
    background: linear-gradient(to bottom, 
        rgba(255, 255, 255, 0.9), 
        rgba(255, 167, 38, 0.7),
        transparent);
    animation: lightning 2s ease-in-out infinite;
}

.lightning-bolt:nth-child(1) {
    top: 20%;
    left: 30%;
    animation-delay: 0s;
    transform: rotate(15deg);
}

.lightning-bolt:nth-child(2) {
    top: 60%;
    left: 70%;
    animation-delay: 0.7s;
    transform: rotate(-20deg);
}

.lightning-bolt:nth-child(3) {
    top: 40%;
    left: 50%;
    animation-delay: 1.4s;
    transform: rotate(10deg);
}

@keyframes lightning {
    0%, 90%, 100% {
        opacity: 0;
        transform: scale(1) rotate(var(--rotation, 0deg));
    }
    5%, 85% {
        opacity: 1;
        transform: scale(1.2) rotate(var(--rotation, 0deg));
    }
}

/* 量子共鸣者 - 量子轨道 */
.quantum-orbits {
    position: absolute;
    width: 100%;
    height: 100%;
    pointer-events: none;
}

.quantum-orbit-enhanced {
    position: absolute;
    top: 50%;
    left: 50%;
    width: 80px;
    height: 80px;
    border: 1px solid rgba(78, 205, 196, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: quantumSpin 8s linear infinite;
}

.quantum-orbit-enhanced:nth-child(2) {
    width: 120px;
    height: 120px;
    animation-duration: 12s;
    animation-direction: reverse;
}

.quantum-electron {
    position: absolute;
    width: 4px;
    height: 4px;
    background: rgba(78, 205, 196, 0.8);
    border-radius: 50%;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    box-shadow: 0 0 8px rgba(78, 205, 196, 0.6);
}

@keyframes quantumSpin {
    from {
        transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
        transform: translate(-50%, -50%) rotate(360deg);
    }
}

/* ===== 卡片内容增强 ===== */
.card-content-enhanced {
    position: relative;
    z-index: 2;
    padding: 2rem;
    height: 100%;
    display: flex;
    flex-direction: column;
    backdrop-filter: blur(5px);
}

/* 游戏图标增强 */
.game-icon-enhanced {
    position: relative;
    width: 90px;
    height: 90px;
    margin: 0 auto 1.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    transition: all 0.3s ease;
}

.game-card-enhanced:hover .game-icon-enhanced {
    transform: scale(1.1);
    background: rgba(255, 255, 255, 0.12);
    box-shadow: 0 0 30px rgba(255, 255, 255, 0.1);
}

.game-symbol-enhanced {
    font-size: 2.5rem;
    filter: drop-shadow(0 0 15px rgba(255, 255, 255, 0.3));
    transition: all 0.3s ease;
}

.game-card-enhanced:hover .game-symbol-enhanced {
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.5));
    transform: scale(1.1);
}

/* 游戏信息增强 */
.game-info-enhanced {
    text-align: center;
    flex: 1;
    margin-bottom: 1.5rem;
}

.game-title-enhanced {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    color: var(--text-primary);
    transition: all 0.3s ease;
}

.game-card-enhanced:hover .game-title-enhanced {
    color: var(--spark-400);
    text-shadow: 0 0 10px rgba(255, 107, 107, 0.3);
}

.game-subtitle-enhanced {
    font-size: 0.875rem;
    color: var(--text-tertiary);
    margin-bottom: 1rem;
    font-style: italic;
    opacity: 0.8;
}

.game-description-enhanced {
    font-size: 0.875rem;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 1.5rem;
    opacity: 0.9;
}

/* 特性标签增强 */
.game-features-enhanced {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    justify-content: center;
    margin-bottom: 1.5rem;
}

.feature-tag-enhanced {
    padding: 0.375rem 0.75rem;
    background: rgba(255, 255, 255, 0.08);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 1rem;
    font-size: 0.75rem;
    color: var(--text-secondary);
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.game-card-enhanced:hover .feature-tag-enhanced {
    background: rgba(255, 255, 255, 0.12);
    border-color: rgba(255, 255, 255, 0.25);
    transform: translateY(-2px);
}

/* 操作按钮增强 */
.card-actions-enhanced {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: auto;
}

.action-btn-enhanced {
    flex: 1;
    max-width: 140px;
    padding: 0.75rem 1rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    position: relative;
    overflow: hidden;
}

.play-btn-enhanced {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3);
}

.play-btn-enhanced:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.preview-btn-enhanced {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-secondary);
    border: 1px solid rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
}

.preview-btn-enhanced:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.25);
    color: var(--text-primary);
    transform: translateY(-2px);
}
