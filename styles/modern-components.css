/**
 * Split-Second Spark - 现代化组件样式
 * 提供现代化的UI组件和布局系统
 */

/* ===== 现代化按钮系统 ===== */
.btn-modern {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 0.75rem;
    font-size: 0.875rem;
    font-weight: 600;
    line-height: 1;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    user-select: none;
    transform: translateZ(0);
}

/* 主要按钮 */
.btn-primary-modern {
    background: var(--gradient-primary);
    color: white;
    box-shadow: 0 4px 14px 0 rgba(255, 107, 107, 0.25);
}

.btn-primary-modern:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(255, 107, 107, 0.35);
}

.btn-primary-modern:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px 0 rgba(255, 107, 107, 0.2);
}

/* 次要按钮 */
.btn-secondary-modern {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
}

.btn-secondary-modern:hover {
    background: rgba(255, 255, 255, 0.1);
    border-color: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

/* 幽灵按钮 */
.btn-ghost-modern {
    background: transparent;
    color: var(--text-secondary);
    border: 1px solid transparent;
}

.btn-ghost-modern:hover {
    background: rgba(255, 255, 255, 0.05);
    color: var(--text-primary);
    border-color: rgba(255, 255, 255, 0.1);
}

/* 按钮尺寸 */
.btn-sm { padding: 0.5rem 1rem; font-size: 0.75rem; }
.btn-lg { padding: 1rem 2rem; font-size: 1rem; }
.btn-xl { padding: 1.25rem 2.5rem; font-size: 1.125rem; }

/* ===== 现代化卡片系统 ===== */
.card-modern {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 1rem;
    backdrop-filter: blur(20px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.card-modern::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
}

.card-modern:hover {
    transform: translateY(-4px);
    border-color: rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 1.5rem 1.5rem 0;
}

.card-body {
    padding: 1.5rem;
}

.card-footer {
    padding: 0 1.5rem 1.5rem;
    margin-top: auto;
}

/* ===== 现代化输入框 ===== */
.input-modern {
    width: 100%;
    padding: 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.input-modern:focus {
    outline: none;
    border-color: var(--spark-500);
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
    background: rgba(255, 255, 255, 0.08);
}

.input-modern::placeholder {
    color: var(--text-tertiary);
}

/* 输入框组 */
.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group .input-modern {
    padding-left: 2.5rem;
}

.input-icon {
    position: absolute;
    left: 0.75rem;
    color: var(--text-tertiary);
    z-index: 1;
}

/* ===== 现代化选择器 ===== */
.select-modern {
    position: relative;
    display: inline-block;
    width: 100%;
}

.select-modern select {
    width: 100%;
    padding: 0.75rem 2.5rem 0.75rem 1rem;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    color: var(--text-primary);
    font-size: 0.875rem;
    cursor: pointer;
    appearance: none;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
}

.select-modern::after {
    content: '▼';
    position: absolute;
    right: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-tertiary);
    pointer-events: none;
    font-size: 0.75rem;
}

.select-modern select:focus {
    outline: none;
    border-color: var(--spark-500);
    box-shadow: 0 0 0 3px rgba(255, 107, 107, 0.1);
}

/* ===== 现代化开关 ===== */
.switch-modern {
    position: relative;
    display: inline-block;
    width: 3rem;
    height: 1.5rem;
}

.switch-modern input {
    opacity: 0;
    width: 0;
    height: 0;
}

.switch-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 1.5rem;
}

.switch-slider:before {
    position: absolute;
    content: "";
    height: 1rem;
    width: 1rem;
    left: 0.125rem;
    bottom: 0.125rem;
    background: white;
    transition: 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .switch-slider {
    background: var(--spark-500);
    border-color: var(--spark-500);
}

input:checked + .switch-slider:before {
    transform: translateX(1.5rem);
}

/* ===== 现代化进度条 ===== */
.progress-modern {
    width: 100%;
    height: 0.5rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.25rem;
    overflow: hidden;
    position: relative;
}

.progress-bar-modern {
    height: 100%;
    background: var(--gradient-primary);
    border-radius: 0.25rem;
    transition: width 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.progress-bar-modern::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.3),
        transparent
    );
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

/* ===== 现代化标签 ===== */
.badge-modern {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    font-size: 0.75rem;
    font-weight: 500;
    border-radius: 9999px;
    border: 1px solid transparent;
}

.badge-primary {
    background: rgba(255, 107, 107, 0.1);
    color: var(--spark-400);
    border-color: rgba(255, 107, 107, 0.2);
}

.badge-secondary {
    background: rgba(78, 205, 196, 0.1);
    color: var(--cyan-400);
    border-color: rgba(78, 205, 196, 0.2);
}

.badge-accent {
    background: rgba(69, 183, 209, 0.1);
    color: var(--accent-400);
    border-color: rgba(69, 183, 209, 0.2);
}

/* ===== 现代化工具提示 ===== */
.tooltip-modern {
    position: relative;
    display: inline-block;
}

.tooltip-modern::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    padding: 0.5rem 0.75rem;
    background: rgba(0, 0, 0, 0.9);
    color: white;
    font-size: 0.75rem;
    border-radius: 0.375rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
    margin-bottom: 0.5rem;
}

.tooltip-modern::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 1000;
}

.tooltip-modern:hover::before,
.tooltip-modern:hover::after {
    opacity: 1;
    visibility: visible;
}

/* ===== 现代化分割线 ===== */
.divider-modern {
    height: 1px;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
    margin: 1.5rem 0;
}

.divider-text {
    position: relative;
    text-align: center;
    margin: 1.5rem 0;
}

.divider-text::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.1),
        transparent
    );
}

.divider-text span {
    background: var(--bg-primary);
    padding: 0 1rem;
    color: var(--text-tertiary);
    font-size: 0.875rem;
    position: relative;
    z-index: 1;
}

/* ===== 现代化网格系统 ===== */
.grid-modern {
    display: grid;
    gap: 1rem;
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-auto { grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); }

.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-full { grid-column: 1 / -1; }

/* ===== 现代化弹性布局 ===== */
.flex-modern {
    display: flex;
}

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

/* ===== 间距工具类 ===== */
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 0.75rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }
.p-6 { padding: 1.5rem; }
.p-8 { padding: 2rem; }

.m-1 { margin: 0.25rem; }
.m-2 { margin: 0.5rem; }
.m-3 { margin: 0.75rem; }
.m-4 { margin: 1rem; }
.m-6 { margin: 1.5rem; }
.m-8 { margin: 2rem; }
.m-auto { margin: auto; }

/* ===== 文本工具类 ===== */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.leading-tight { line-height: 1.25; }
.leading-normal { line-height: 1.5; }
.leading-relaxed { line-height: 1.625; }

/* ===== 圆角工具类 ===== */
.rounded-none { border-radius: 0; }
.rounded-sm { border-radius: 0.125rem; }
.rounded { border-radius: 0.25rem; }
.rounded-md { border-radius: 0.375rem; }
.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }
