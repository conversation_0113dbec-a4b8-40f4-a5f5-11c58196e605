/**
 * Split-Second Spark - 增强色彩系统
 * 提供更丰富的色彩层次和更好的可访问性
 */

:root {
    /* ===== 主色调系统 ===== */
    /* 主品牌色 - 火花红 */
    --spark-50: #fef2f2;
    --spark-100: #fee2e2;
    --spark-200: #fecaca;
    --spark-300: #fca5a5;
    --spark-400: #f87171;
    --spark-500: #ff6b6b;  /* 主色 */
    --spark-600: #dc2626;
    --spark-700: #b91c1c;
    --spark-800: #991b1b;
    --spark-900: #7f1d1d;
    
    /* 次要色 - 青蓝色 */
    --cyan-50: #ecfeff;
    --cyan-100: #cffafe;
    --cyan-200: #a5f3fc;
    --cyan-300: #67e8f9;
    --cyan-400: #22d3ee;
    --cyan-500: #4ecdc4;  /* 次要色 */
    --cyan-600: #0891b2;
    --cyan-700: #0e7490;
    --cyan-800: #155e75;
    --cyan-900: #164e63;
    
    /* 强调色 - 蓝紫色 */
    --accent-50: #eff6ff;
    --accent-100: #dbeafe;
    --accent-200: #bfdbfe;
    --accent-300: #93c5fd;
    --accent-400: #60a5fa;
    --accent-500: #45b7d1;  /* 强调色 */
    --accent-600: #2563eb;
    --accent-700: #1d4ed8;
    --accent-800: #1e40af;
    --accent-900: #1e3a8a;
    
    /* ===== 中性色系统 ===== */
    /* 深色主题中性色 */
    --neutral-50: #fafafa;
    --neutral-100: #f5f5f5;
    --neutral-200: #e5e5e5;
    --neutral-300: #d4d4d4;
    --neutral-400: #a3a3a3;
    --neutral-500: #737373;
    --neutral-600: #525252;
    --neutral-700: #404040;
    --neutral-800: #262626;
    --neutral-900: #171717;
    --neutral-950: #0a0a0a;
    
    /* 蓝灰色系 - 用于深色背景 */
    --slate-50: #f8fafc;
    --slate-100: #f1f5f9;
    --slate-200: #e2e8f0;
    --slate-300: #cbd5e1;
    --slate-400: #94a3b8;
    --slate-500: #64748b;
    --slate-600: #475569;
    --slate-700: #334155;
    --slate-800: #1e293b;
    --slate-900: #0f172a;
    --slate-950: #020617;
    
    /* ===== 语义化色彩 ===== */
    /* 成功状态 */
    --success-50: #f0fdf4;
    --success-100: #dcfce7;
    --success-200: #bbf7d0;
    --success-300: #86efac;
    --success-400: #4ade80;
    --success-500: #22c55e;
    --success-600: #16a34a;
    --success-700: #15803d;
    --success-800: #166534;
    --success-900: #14532d;
    
    /* 警告状态 */
    --warning-50: #fffbeb;
    --warning-100: #fef3c7;
    --warning-200: #fde68a;
    --warning-300: #fcd34d;
    --warning-400: #fbbf24;
    --warning-500: #f9ca24;
    --warning-600: #d97706;
    --warning-700: #b45309;
    --warning-800: #92400e;
    --warning-900: #78350f;
    
    /* 错误状态 */
    --error-50: #fef2f2;
    --error-100: #fee2e2;
    --error-200: #fecaca;
    --error-300: #fca5a5;
    --error-400: #f87171;
    --error-500: #ef4444;
    --error-600: #dc2626;
    --error-700: #b91c1c;
    --error-800: #991b1b;
    --error-900: #7f1d1d;
    
    /* 信息状态 */
    --info-50: #eff6ff;
    --info-100: #dbeafe;
    --info-200: #bfdbfe;
    --info-300: #93c5fd;
    --info-400: #60a5fa;
    --info-500: #3b82f6;
    --info-600: #2563eb;
    --info-700: #1d4ed8;
    --info-800: #1e40af;
    --info-900: #1e3a8a;
    
    /* ===== 应用色彩映射 ===== */
    /* 背景色 */
    --bg-primary: var(--slate-950);
    --bg-secondary: var(--slate-900);
    --bg-tertiary: var(--slate-800);
    --bg-quaternary: var(--slate-700);
    --bg-card: rgba(15, 23, 42, 0.8);
    --bg-modal: rgba(2, 6, 23, 0.95);
    --bg-overlay: rgba(0, 0, 0, 0.5);
    
    /* 文本色 */
    --text-primary: var(--neutral-50);
    --text-secondary: var(--slate-300);
    --text-tertiary: var(--slate-400);
    --text-quaternary: var(--slate-500);
    --text-muted: var(--slate-600);
    --text-accent: var(--spark-400);
    --text-link: var(--accent-400);
    --text-link-hover: var(--accent-300);
    
    /* 边框色 */
    --border-primary: var(--slate-700);
    --border-secondary: var(--slate-600);
    --border-tertiary: var(--slate-500);
    --border-accent: var(--spark-500);
    --border-focus: var(--accent-400);
    --border-error: var(--error-500);
    --border-success: var(--success-500);
    --border-warning: var(--warning-500);
    
    /* 阴影和光效 */
    --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
    --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
    --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);
    
    /* 光效 */
    --glow-spark: 0 0 20px rgba(255, 107, 107, 0.4);
    --glow-cyan: 0 0 20px rgba(78, 205, 196, 0.4);
    --glow-accent: 0 0 20px rgba(69, 183, 209, 0.4);
    --glow-success: 0 0 20px rgba(34, 197, 94, 0.4);
    --glow-warning: 0 0 20px rgba(249, 202, 36, 0.4);
    --glow-error: 0 0 20px rgba(239, 68, 68, 0.4);
    
    /* 渐变 */
    --gradient-primary: linear-gradient(135deg, var(--spark-500) 0%, var(--cyan-500) 100%);
    --gradient-secondary: linear-gradient(135deg, var(--accent-600) 0%, var(--accent-800) 100%);
    --gradient-background: linear-gradient(135deg, var(--slate-950) 0%, var(--slate-900) 50%, var(--slate-800) 100%);
    --gradient-card: linear-gradient(135deg, rgba(15, 23, 42, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
    --gradient-button: linear-gradient(135deg, var(--spark-500) 0%, var(--spark-600) 100%);
    --gradient-button-hover: linear-gradient(135deg, var(--spark-400) 0%, var(--spark-500) 100%);
    
    /* 特殊效果渐变 */
    --gradient-spark: linear-gradient(45deg, var(--spark-400), var(--warning-400), var(--spark-400));
    --gradient-quantum: linear-gradient(45deg, var(--accent-400), var(--cyan-400), var(--accent-400));
    --gradient-temporal: linear-gradient(45deg, var(--accent-600), var(--accent-800), var(--accent-600));
}

/* ===== 高对比度模式支持 ===== */
@media (prefers-contrast: high) {
    :root {
        --text-primary: #ffffff;
        --text-secondary: #e5e5e5;
        --bg-primary: #000000;
        --bg-secondary: #1a1a1a;
        --border-primary: #ffffff;
        --border-secondary: #cccccc;
    }
}

/* ===== 减少动画模式支持 ===== */
@media (prefers-reduced-motion: reduce) {
    :root {
        --transition-fast: 0s;
        --transition-medium: 0s;
        --transition-slow: 0s;
    }
    
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* ===== 色彩工具类 ===== */
.text-spark { color: var(--spark-500); }
.text-cyan { color: var(--cyan-500); }
.text-accent { color: var(--accent-500); }
.text-success { color: var(--success-500); }
.text-warning { color: var(--warning-500); }
.text-error { color: var(--error-500); }
.text-info { color: var(--info-500); }

.bg-spark { background-color: var(--spark-500); }
.bg-cyan { background-color: var(--cyan-500); }
.bg-accent { background-color: var(--accent-500); }
.bg-success { background-color: var(--success-500); }
.bg-warning { background-color: var(--warning-500); }
.bg-error { background-color: var(--error-500); }
.bg-info { background-color: var(--info-500); }

.border-spark { border-color: var(--spark-500); }
.border-cyan { border-color: var(--cyan-500); }
.border-accent { border-color: var(--accent-500); }
.border-success { border-color: var(--success-500); }
.border-warning { border-color: var(--warning-500); }
.border-error { border-color: var(--error-500); }
.border-info { border-color: var(--info-500); }
