/**
 * Split-Second Spark - 增强交互效果
 * 提供更丰富的微交互和用户反馈
 */

/* ===== 基础交互变量 ===== */
:root {
    /* 动画时长 */
    --duration-instant: 0.1s;
    --duration-fast: 0.2s;
    --duration-normal: 0.3s;
    --duration-slow: 0.5s;
    --duration-slower: 0.8s;
    
    /* 缓动函数 */
    --ease-out-cubic: cubic-bezier(0.33, 1, 0.68, 1);
    --ease-in-cubic: cubic-bezier(0.32, 0, 0.67, 0);
    --ease-in-out-cubic: cubic-bezier(0.65, 0, 0.35, 1);
    --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
    --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);
    
    /* 变换值 */
    --scale-hover: 1.05;
    --scale-active: 0.95;
    --translate-hover: -2px;
    --translate-active: 0px;
}

/* ===== 增强按钮交互 ===== */
.btn-enhanced {
    position: relative;
    overflow: hidden;
    transform: translateZ(0);
    transition: all var(--duration-normal) var(--ease-out-cubic);
    cursor: pointer;
    user-select: none;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
    );
    transition: left var(--duration-slow) var(--ease-out-cubic);
}

.btn-enhanced:hover {
    transform: translateY(var(--translate-hover)) scale(var(--scale-hover));
    box-shadow: var(--shadow-lg), var(--glow-spark);
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-enhanced:active {
    transform: translateY(var(--translate-active)) scale(var(--scale-active));
    transition-duration: var(--duration-fast);
}

.btn-enhanced:focus-visible {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}

/* ===== 游戏卡片增强交互 ===== */
.game-card-enhanced {
    position: relative;
    transform: translateZ(0);
    transition: all var(--duration-normal) var(--ease-out-cubic);
    cursor: pointer;
}

.game-card-enhanced::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(
        135deg,
        rgba(255, 255, 255, 0.1) 0%,
        transparent 50%,
        rgba(255, 255, 255, 0.05) 100%
    );
    opacity: 0;
    transition: opacity var(--duration-normal) var(--ease-out-cubic);
    pointer-events: none;
    border-radius: inherit;
}

.game-card-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: var(--shadow-2xl), var(--glow-spark);
}

.game-card-enhanced:hover::after {
    opacity: 1;
}

.game-card-enhanced:active {
    transform: translateY(-4px) scale(1.01);
    transition-duration: var(--duration-fast);
}

/* ===== 涟漪效果 ===== */
.ripple-effect {
    position: relative;
    overflow: hidden;
}

.ripple-effect::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transform: translate(-50%, -50%);
    transition: width var(--duration-slow) var(--ease-out-cubic),
                height var(--duration-slow) var(--ease-out-cubic);
}

.ripple-effect:active::before {
    width: 300px;
    height: 300px;
}

/* ===== 磁性效果 ===== */
.magnetic-effect {
    transition: transform var(--duration-normal) var(--ease-out-cubic);
}

.magnetic-effect:hover {
    transform: translate(var(--mouse-x, 0), var(--mouse-y, 0));
}

/* ===== 悬浮卡片效果 ===== */
.floating-card {
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* ===== 脉冲效果 ===== */
.pulse-effect {
    animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* ===== 呼吸光效 ===== */
.breathing-glow {
    animation: breathe 3s ease-in-out infinite;
}

@keyframes breathe {
    0%, 100% {
        box-shadow: var(--glow-spark);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 107, 107, 0.6);
    }
}

/* ===== 旋转效果 ===== */
.rotate-on-hover {
    transition: transform var(--duration-normal) var(--ease-out-cubic);
}

.rotate-on-hover:hover {
    transform: rotate(5deg);
}

/* ===== 弹性缩放 ===== */
.elastic-scale {
    transition: transform var(--duration-normal) var(--ease-bounce);
}

.elastic-scale:hover {
    transform: scale(1.1);
}

.elastic-scale:active {
    transform: scale(0.95);
}

/* ===== 渐变边框动画 ===== */
.animated-border {
    position: relative;
    background: var(--bg-card);
    border: 1px solid transparent;
}

.animated-border::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    padding: 1px;
    background: linear-gradient(45deg, var(--spark-400), var(--cyan-400), var(--accent-400));
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: exclude;
    -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: destination-out;
    animation: borderRotate 3s linear infinite;
}

@keyframes borderRotate {
    0% {
        background: linear-gradient(45deg, var(--spark-400), var(--cyan-400), var(--accent-400));
    }
    33% {
        background: linear-gradient(45deg, var(--cyan-400), var(--accent-400), var(--spark-400));
    }
    66% {
        background: linear-gradient(45deg, var(--accent-400), var(--spark-400), var(--cyan-400));
    }
    100% {
        background: linear-gradient(45deg, var(--spark-400), var(--cyan-400), var(--accent-400));
    }
}

/* ===== 文字打字机效果 ===== */
.typewriter {
    overflow: hidden;
    border-right: 2px solid var(--text-primary);
    white-space: nowrap;
    animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typing {
    from {
        width: 0;
    }
    to {
        width: 100%;
    }
}

@keyframes blink-caret {
    from, to {
        border-color: transparent;
    }
    50% {
        border-color: var(--text-primary);
    }
}

/* ===== 加载动画增强 ===== */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--border-primary);
    border-top: 4px solid var(--spark-500);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

.loading-dots {
    display: inline-flex;
    gap: 4px;
}

.loading-dots span {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: var(--spark-500);
    animation: bounce 1.4s ease-in-out infinite both;
}

.loading-dots span:nth-child(1) { animation-delay: -0.32s; }
.loading-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes bounce {
    0%, 80%, 100% {
        transform: scale(0);
    }
    40% {
        transform: scale(1);
    }
}

/* ===== 焦点管理增强 ===== */
.focus-visible {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
    border-radius: var(--border-radius);
}

/* 隐藏默认焦点样式，使用自定义样式 */
*:focus {
    outline: none;
}

*:focus-visible {
    outline: 2px solid var(--border-focus);
    outline-offset: 2px;
}

/* ===== 滚动条美化 ===== */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--border-secondary);
    border-radius: 4px;
    transition: background var(--duration-fast);
}

::-webkit-scrollbar-thumb:hover {
    background: var(--border-primary);
}

/* ===== 选择文本样式 ===== */
::selection {
    background: var(--spark-500);
    color: var(--text-primary);
}

::-moz-selection {
    background: var(--spark-500);
    color: var(--text-primary);
}
