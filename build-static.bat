@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ================================================================
echo 🎮 Split-Second Spark 静态部署打包工具 (Windows版)
echo ================================================================
echo.

:: 设置变量
set "SOURCE_DIR=%~dp0"
set "OUTPUT_DIR=%SOURCE_DIR%dist"
set "PACKAGE_NAME=split-second-spark-static"

:: 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python，请先安装Python 3.6+
    echo    下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

echo ✅ 检测到Python环境
echo.

:: 运行Python打包脚本
echo 🚀 开始执行打包脚本...
python "%SOURCE_DIR%build-static.py"

if errorlevel 1 (
    echo.
    echo ❌ 打包失败！请检查错误信息
    pause
    exit /b 1
)

echo.
echo ================================================================
echo 🎉 打包完成！
echo ================================================================
echo.
echo 📁 部署文件位置: %OUTPUT_DIR%
echo 📦 压缩包位置: %SOURCE_DIR%%PACKAGE_NAME%.zip
echo.
echo 📋 接下来的步骤:
echo    1. 将压缩包上传到您的Web服务器
echo    2. 解压到服务器的Web根目录
echo    3. 访问 http://your-domain.com/index.html
echo    4. 推荐使用HTTPS以获得最佳体验
echo.
echo 🌐 支持的部署平台:
echo    • GitHub Pages
echo    • Vercel  
echo    • Netlify
echo    • 阿里云OSS
echo    • 腾讯云COS
echo    • 任何静态文件服务器
echo.

:: 询问是否打开输出目录
set /p "OPEN_DIR=是否打开输出目录? (y/n): "
if /i "!OPEN_DIR!"=="y" (
    explorer "%OUTPUT_DIR%"
)

:: 询问是否启动本地测试服务器
set /p "START_SERVER=是否启动本地测试服务器? (y/n): "
if /i "!START_SERVER!"=="y" (
    echo.
    echo 🌐 启动本地测试服务器...
    echo    访问地址: http://localhost:8000
    echo    按 Ctrl+C 停止服务器
    echo.
    cd /d "%OUTPUT_DIR%"
    python -m http.server 8000
)

pause
