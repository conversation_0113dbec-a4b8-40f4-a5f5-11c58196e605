<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Split-Second Spark - 存储服务检查演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-section h2 {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #4ecdc4;
        }

        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .status-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-online { background: #51cf66; }
        .status-offline { background: #ff6b6b; }
        .status-warning { background: #ffd43b; }

        .control-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin-bottom: 30px;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
        }

        .btn-success {
            background: linear-gradient(45deg, #51cf66, #40c057);
        }

        .btn-warning {
            background: linear-gradient(45deg, #ffd43b, #fab005);
        }

        .log-area {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-info { color: #4ecdc4; }
        .log-success { color: #51cf66; }
        .log-warning { color: #ffd43b; }
        .log-error { color: #ff6b6b; }

        .config-selector {
            margin-bottom: 20px;
        }

        .config-selector select {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 16px;
        }

        .config-selector option {
            background: #333;
            color: white;
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: none;
            align-items: center;
            justify-content: center;
            z-index: 10000;
        }

        .modal-content {
            background: white;
            color: #333;
            padding: 30px;
            border-radius: 10px;
            max-width: 400px;
            text-align: center;
        }

        .modal-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
        }

        .modal-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }

        .modal-btn-primary {
            background: #4CAF50;
            color: white;
        }

        .modal-btn-secondary {
            background: #f44336;
            color: white;
        }

        @media (max-width: 768px) {
            .status-grid, .control-panel {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔍 存储服务检查</h1>
            <p>演示存储服务存在性检查和用户身份设置流程</p>
        </div>

        <!-- 系统状态 -->
        <div class="demo-section">
            <h2>📊 系统状态</h2>
            <div class="status-grid" id="systemStatus">
                <div class="status-card">
                    <h3>🔧 配置管理器</h3>
                    <p><span class="status-indicator status-offline"></span>未初始化</p>
                </div>
                <div class="status-card">
                    <h3>💾 存储服务</h3>
                    <p><span class="status-indicator status-offline"></span>未创建</p>
                </div>
                <div class="status-card">
                    <h3>☁️ 云存储支持</h3>
                    <p><span class="status-indicator status-offline"></span>未检测</p>
                </div>
                <div class="status-card">
                    <h3>👤 用户身份</h3>
                    <p><span class="status-indicator status-offline"></span>未设置</p>
                </div>
            </div>
        </div>

        <!-- 配置选择 -->
        <div class="demo-section">
            <h2>⚙️ 存储配置</h2>
            <div class="config-selector">
                <select id="configSelect">
                    <option value="local">本地存储配置</option>
                    <option value="firebase">Firebase 云存储配置</option>
                    <option value="hybrid">混合存储配置</option>
                    <option value="performance">高性能配置</option>
                    <option value="privacy">隐私模式配置</option>
                </select>
            </div>
        </div>

        <!-- 控制面板 -->
        <div class="demo-section">
            <h2>🎮 操作控制</h2>
            <div class="control-panel">
                <button class="btn" onclick="initConfigManager()">初始化配置管理器</button>
                <button class="btn" onclick="createStorageService()">创建存储服务</button>
                <button class="btn btn-secondary" onclick="checkExistingService()">检查现有服务</button>
                <button class="btn btn-success" onclick="switchConfig()">切换配置</button>
                <button class="btn btn-warning" onclick="resetSystem()">重置系统</button>
                <button class="btn btn-secondary" onclick="refreshStatus()">刷新状态</button>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="demo-section">
            <h2>📝 操作日志</h2>
            <div class="log-area" id="logArea">
                <div class="log-entry log-info">🚀 存储服务检查演示已准备就绪</div>
            </div>
            <button class="btn btn-secondary" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <!-- 用户身份设置模态框 -->
    <div class="modal" id="identityModal">
        <div class="modal-content">
            <h3>🔐 启用云存储功能</h3>
            <p>检测到您的游戏配置支持云存储功能，可以在多个设备间同步游戏数据。</p>
            <p>是否现在设置用户身份以启用云存储？</p>
            <div class="modal-buttons">
                <button class="modal-btn modal-btn-primary" onclick="setupIdentity()">设置身份</button>
                <button class="modal-btn modal-btn-secondary" onclick="skipIdentitySetup()">暂时跳过</button>
            </div>
        </div>
    </div>

    <script type="module">
        // 全局变量
        let configManager = null;
        let storageService = null;
        let isInitialized = false;

        // 初始化配置管理器
        window.initConfigManager = async function() {
            try {
                log('🔧 初始化配置管理器...', 'info');
                
                // 动态导入配置管理器
                const { StorageConfigManager } = await import('./js/config/storage-config.js');
                
                configManager = StorageConfigManager.getInstance();
                isInitialized = true;
                
                log('✅ 配置管理器初始化成功', 'success');
                updateSystemStatus();
                
            } catch (error) {
                log(`❌ 配置管理器初始化失败: ${error.message}`, 'error');
                console.error('初始化错误:', error);
            }
        };

        // 创建存储服务（包含检查逻辑）
        window.createStorageService = async function() {
            if (!configManager) {
                log('❌ 请先初始化配置管理器', 'error');
                return;
            }

            try {
                log('🔍 开始创建存储服务...', 'info');
                
                // 获取选择的配置
                const selectedConfig = document.getElementById('configSelect').value;
                log(`📋 选择的配置: ${selectedConfig}`, 'info');
                
                // 检查存储服务是否已存在
                const existingService = configManager.getCurrentStorageService();
                
                if (existingService) {
                    log('🔍 检测到已存在的存储服务', 'warning');
                    
                    // 检查是否为云存储配置
                    const isCloudStorage = configManager.isCloudStorageEnabled();
                    
                    if (isCloudStorage) {
                        log('☁️ 当前配置支持云存储，检查用户身份状态...', 'info');
                        
                        // 模拟检查用户身份状态
                        const hasUserIdentity = await checkUserIdentityStatus();
                        
                        if (!hasUserIdentity) {
                            log('⚠️ 需要设置用户身份以使用云存储功能', 'warning');
                            
                            // 显示用户身份设置提示
                            const shouldSetupIdentity = await promptUserIdentitySetup();
                            
                            if (shouldSetupIdentity) {
                                log('👤 用户选择设置身份', 'info');
                                storageService = await configManager.createStorageService(selectedConfig);
                            } else {
                                log('👤 用户选择跳过身份设置，使用现有服务', 'info');
                                storageService = existingService;
                            }
                        } else {
                            log('✅ 用户身份已设置，使用现有服务', 'success');
                            storageService = existingService;
                        }
                    } else {
                        log('📱 当前为本地存储配置，使用现有服务', 'info');
                        storageService = existingService;
                    }
                } else {
                    log('🆕 创建新的存储服务', 'info');
                    storageService = await configManager.createStorageService(selectedConfig);
                }
                
                log('✅ 存储服务创建/获取完成', 'success');
                updateSystemStatus();
                
            } catch (error) {
                log(`❌ 存储服务创建失败: ${error.message}`, 'error');
                console.error('创建错误:', error);
            }
        };

        // 检查现有服务
        window.checkExistingService = function() {
            if (!configManager) {
                log('❌ 请先初始化配置管理器', 'error');
                return;
            }

            const existingService = configManager.getCurrentStorageService();
            const currentConfig = configManager.getCurrentConfigName();
            const isCloudEnabled = configManager.isCloudStorageEnabled();

            if (existingService) {
                log(`🔍 发现现有存储服务`, 'info');
                log(`📋 当前配置: ${currentConfig}`, 'info');
                log(`☁️ 云存储支持: ${isCloudEnabled ? '是' : '否'}`, 'info');
            } else {
                log('❌ 未发现现有存储服务', 'warning');
            }

            updateSystemStatus();
        };

        // 切换配置
        window.switchConfig = async function() {
            if (!configManager) {
                log('❌ 请先初始化配置管理器', 'error');
                return;
            }

            try {
                const newConfig = document.getElementById('configSelect').value;
                log(`🔄 切换到配置: ${newConfig}`, 'info');
                
                storageService = await configManager.switchStorageConfig(newConfig);
                
                log('✅ 配置切换成功', 'success');
                updateSystemStatus();
                
            } catch (error) {
                log(`❌ 配置切换失败: ${error.message}`, 'error');
            }
        };

        // 重置系统
        window.resetSystem = function() {
            if (configManager) {
                configManager.resetStorageService();
            }
            
            configManager = null;
            storageService = null;
            isInitialized = false;
            
            log('🔄 系统已重置', 'warning');
            updateSystemStatus();
        };

        // 刷新状态
        window.refreshStatus = function() {
            updateSystemStatus();
            log('🔄 状态已刷新', 'info');
        };

        // 模拟检查用户身份状态
        async function checkUserIdentityStatus() {
            // 模拟异步检查
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 随机返回结果用于演示
            const hasIdentity = Math.random() > 0.7; // 30% 概率有身份
            log(`🔍 用户身份检查结果: ${hasIdentity ? '已设置' : '未设置'}`, hasIdentity ? 'success' : 'warning');
            
            return hasIdentity;
        }

        // 提示用户设置身份
        async function promptUserIdentitySetup() {
            return new Promise((resolve) => {
                const modal = document.getElementById('identityModal');
                modal.style.display = 'flex';
                
                // 设置全局回调
                window.setupIdentityResolve = resolve;
            });
        }

        // 设置身份
        window.setupIdentity = function() {
            const modal = document.getElementById('identityModal');
            modal.style.display = 'none';
            
            log('✅ 用户选择设置身份', 'success');
            window.setupIdentityResolve(true);
        };

        // 跳过身份设置
        window.skipIdentitySetup = function() {
            const modal = document.getElementById('identityModal');
            modal.style.display = 'none';
            
            log('⚠️ 用户选择跳过身份设置', 'warning');
            window.setupIdentityResolve(false);
        };

        // 更新系统状态显示
        function updateSystemStatus() {
            const statusCards = document.querySelectorAll('.status-card');

            // 配置管理器状态
            const configCard = statusCards[0];
            const configIndicator = configCard.querySelector('.status-indicator');
            const configText = configCard.querySelector('p');
            
            if (isInitialized && configManager) {
                configIndicator.className = 'status-indicator status-online';
                configText.innerHTML = '<span class="status-indicator status-online"></span>已初始化';
            } else {
                configIndicator.className = 'status-indicator status-offline';
                configText.innerHTML = '<span class="status-indicator status-offline"></span>未初始化';
            }

            // 存储服务状态
            const storageCard = statusCards[1];
            const storageIndicator = storageCard.querySelector('.status-indicator');
            const storageText = storageCard.querySelector('p');
            
            if (configManager && configManager.getCurrentStorageService()) {
                storageIndicator.className = 'status-indicator status-online';
                const configName = configManager.getCurrentConfigName() || '未知';
                storageText.innerHTML = `<span class="status-indicator status-online"></span>已创建 (${configName})`;
            } else {
                storageIndicator.className = 'status-indicator status-offline';
                storageText.innerHTML = '<span class="status-indicator status-offline"></span>未创建';
            }

            // 云存储支持状态
            const cloudCard = statusCards[2];
            const cloudIndicator = cloudCard.querySelector('.status-indicator');
            const cloudText = cloudCard.querySelector('p');
            
            if (configManager && configManager.isCloudStorageEnabled()) {
                cloudIndicator.className = 'status-indicator status-online';
                cloudText.innerHTML = '<span class="status-indicator status-online"></span>支持';
            } else if (configManager) {
                cloudIndicator.className = 'status-indicator status-warning';
                cloudText.innerHTML = '<span class="status-indicator status-warning"></span>不支持';
            } else {
                cloudIndicator.className = 'status-indicator status-offline';
                cloudText.innerHTML = '<span class="status-indicator status-offline"></span>未检测';
            }

            // 用户身份状态（模拟）
            const userCard = statusCards[3];
            const userText = userCard.querySelector('p');
            userText.innerHTML = '<span class="status-indicator status-warning"></span>模拟状态';
        }

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(message);
        }

        // 清空日志
        window.clearLog = function() {
            document.getElementById('logArea').innerHTML = '';
        };

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('🚀 存储服务检查演示页面已加载', 'info');
            updateSystemStatus();
        });
    </script>
</body>
</html>
