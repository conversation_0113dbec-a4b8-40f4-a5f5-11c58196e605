# 构建输出目录
dist/
build/
out/
target/

# 打包文件
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Node.js 相关
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.npm
.eslintcache
.node_repl_history
*.tgz
.yarn-integrity
.env.local
.env.development.local
.env.test.local
.env.production.local

# 包管理器锁文件（可选择性忽略）
# package-lock.json
# yarn.lock

# 编辑器和IDE
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db

# 日志文件
*.log
logs/
*.log.*

# 临时文件
*.tmp
*.temp
.cache/
.temp/

# 测试覆盖率报告
coverage/
*.lcov
.nyc_output/

# 环境变量文件
.env
.env.local
.env.development
.env.test
.env.production

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 备份文件
*.bak
*.backup
*.old

# 系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 调试和测试文件（项目特定）
*-test.html
*-debug.html
*-verification.html
test-*.html
debug-*.html
diagnostic*.html
*-fix-test.html
*-fix-verification.html
simple-*-test.html
comprehensive-*-test.html
final-*-test.html
quick-*-test.html
manual-*-test.html

# 调试脚本
*-debug.js
*-verification.js
*-fix.js
test-*.js
debug-*.js
fix-*.js
verification-*.js

# 服务器文件
server.py
test-server.py
start-server.bat
start-server.sh
*.py

# 临时HTML文件
isolated-test.html
step-by-step-test.html
eval-test.html
collision-debug.html
module-debug.html
touch-test.html
mobile-test.html

# 图标生成工具
create-*.html
generate-*.html
generate-*.js
create-*.py

# 部署相关临时文件
deployment-info.json
verify-deployment.html

# 独立部署目录
*-standalone/

# 文档草稿和临时文档
*修复说明.md
*修复报告.md
*测试说明.md
*调试说明.md
*验证说明.md
