# Git 忽略文件配置说明

## 概述

本项目已配置完整的 `.gitignore` 文件，用于忽略不应该被版本控制跟踪的文件和目录。这有助于保持仓库的整洁，避免提交不必要的文件。

## 忽略的文件类型

### 🏗️ 构建输出目录
```
dist/
build/
out/
target/
```
这些目录包含编译或构建后的文件，应该通过构建脚本重新生成，不需要版本控制。

### 📦 打包文件
```
*.zip
*.tar.gz
*.tar.bz2
*.rar
*.7z
```
压缩包文件通常是临时的分发文件，不应该存储在版本控制中。

### 🐍 Python 相关文件
```
__pycache__/
*.py[cod]
*$py.class
*.so
build/
dist/
*.egg-info/
```
Python 字节码文件、缓存目录和构建产物。

### 📦 Node.js 相关文件
```
node_modules/
npm-debug.log*
yarn-debug.log*
.npm
.eslintcache
```
Node.js 依赖包和调试日志文件。

### 💻 编辑器和 IDE 文件
```
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store
Thumbs.db
```
编辑器配置文件和系统生成的临时文件。

### 📝 日志和临时文件
```
*.log
logs/
*.tmp
*.temp
.cache/
```
运行时生成的日志和临时文件。

### 🧪 测试和调试文件（项目特定）
```
*-test.html
*-debug.html
*-verification.html
test-*.html
debug-*.html
diagnostic*.html
*-fix-test.html
*-fix-verification.html
```
项目开发过程中创建的各种测试和调试页面。

### 🔧 调试脚本
```
*-debug.js
*-verification.js
*-fix.js
test-*.js
debug-*.js
```
用于调试和验证的 JavaScript 脚本文件。

### 🌐 服务器和工具文件
```
server.py
test-server.py
start-server.bat
start-server.sh
```
本地开发服务器和启动脚本。

### 🎨 图标生成工具
```
create-*.html
generate-*.html
generate-*.js
create-*.py
```
用于生成图标和资源的工具文件。

## 已清理的文件

在配置 `.gitignore` 时，我们从版本控制中移除了以下类型的文件：

### 移除的构建文件
- `dist/` 整个构建输出目录（包含所有游戏的构建版本）
- `split-second-spark-static.zip` 静态部署包
- Python 构建脚本：`build-single-game.py`、`build-static.py`、`create-placeholder-icons.py`

### 移除的缓存文件
- `量子共鸣者/__pycache__/` Python 缓存目录

### 移除的测试文件
- 瞬光捕手项目的测试文件：
  - `debug-i18n.html`
  - `debug-modules.html`
  - `test-custom-levels.html`
  - `test-i18n.js`
  - `test-leaderboard.html`
  - `test-modern-effects.html`

- 量子共鸣者项目的大量测试和调试文件（50+ 个文件）

## 使用建议

### ✅ 应该提交的文件
- 源代码文件（`.js`、`.css`、`.html`）
- 配置文件（`manifest.json`、`package.json`）
- 文档文件（`.md`）
- 资源文件（图片、音频等，但不包括自动生成的）

### ❌ 不应该提交的文件
- 构建输出
- 依赖包目录
- 编辑器配置
- 临时文件和日志
- 测试和调试文件

### 🔄 重新生成构建文件
如果需要构建文件，可以使用项目中的构建脚本：
```bash
# 构建单个游戏
./build-single-game.sh

# 构建静态部署版本
./build-static.sh
```

### 📝 添加新的忽略规则
如果需要忽略新的文件类型，可以编辑 `.gitignore` 文件：
```bash
# 添加新的忽略规则
echo "新的忽略模式" >> .gitignore
git add .gitignore
git commit -m "添加新的忽略规则"
```

## 注意事项

1. **已跟踪的文件**：如果文件已经被 git 跟踪，仅添加到 `.gitignore` 不会自动忽略它们。需要使用 `git rm --cached` 命令先移除跟踪。

2. **全局忽略**：某些文件（如 `.DS_Store`）可以通过全局 `.gitignore` 配置在所有项目中忽略。

3. **强制添加**：如果确实需要添加被忽略的文件，可以使用 `git add -f` 强制添加。

4. **检查忽略状态**：使用 `git check-ignore -v <文件名>` 可以检查文件是否被忽略以及被哪个规则忽略。

## 维护

定期检查和更新 `.gitignore` 文件，确保：
- 新的构建产物被正确忽略
- 不再需要的忽略规则被移除
- 团队成员使用的新工具产生的文件被忽略

这样可以保持仓库的整洁和高效，提高团队协作效率。
