/**
 * 量子共鸣者 - 问题诊断脚本
 * 诊断得分、连击和频率无法调整的问题
 */

class ProblemDiagnosis {
    constructor() {
        this.issues = [];
        this.solutions = [];
    }

    /**
     * 运行完整诊断
     */
    async runDiagnosis() {
        console.log('🔍 开始问题诊断...');
        console.log('='.repeat(60));
        
        // 等待页面加载
        await this.waitForPageLoad();
        
        // 诊断1: 检查DOM元素
        this.diagnoseDOMElements();
        
        // 诊断2: 检查事件绑定
        this.diagnoseEventBindings();
        
        // 诊断3: 检查游戏控制器
        this.diagnoseGameController();
        
        // 诊断4: 检查输入管理器
        this.diagnoseInputManager();
        
        // 诊断5: 检查量子引擎
        this.diagnoseQuantumEngine();
        
        // 诊断6: 测试实际功能
        this.testActualFunctionality();
        
        // 输出诊断结果
        this.outputDiagnosisResults();
        
        // 提供解决方案
        this.provideSolutions();
    }

    /**
     * 等待页面加载
     */
    async waitForPageLoad() {
        return new Promise(resolve => {
            if (document.readyState === 'complete') {
                setTimeout(resolve, 2000);
            } else {
                window.addEventListener('load', () => {
                    setTimeout(resolve, 2000);
                });
            }
        });
    }

    /**
     * 诊断DOM元素
     */
    diagnoseDOMElements() {
        console.log('🔍 诊断DOM元素...');
        
        const requiredElements = [
            { id: 'current-score', name: '分数显示' },
            { id: 'current-combo', name: '连击显示' },
            { id: 'current-level', name: '关卡显示' },
            { id: 'current-time', name: '时间显示' },
            { id: 'frequency-slider', name: '频率滑块' },
            { id: 'frequency-input', name: '频率输入框' },
            { id: 'frequency-value', name: '频率显示' },
            { id: 'resonance-fill', name: '共鸣强度条' }
        ];
        
        requiredElements.forEach(({ id, name }) => {
            const element = document.getElementById(id);
            if (!element) {
                this.issues.push(`❌ 缺失DOM元素: ${name} (#${id})`);
            } else {
                console.log(`✅ 找到DOM元素: ${name} (#${id})`);
            }
        });
    }

    /**
     * 诊断事件绑定
     */
    diagnoseEventBindings() {
        console.log('🔍 诊断事件绑定...');
        
        // 检查频率滑块事件
        const frequencySlider = document.getElementById('frequency-slider');
        if (frequencySlider) {
            const hasInputEvent = this.hasEventListener(frequencySlider, 'input');
            if (!hasInputEvent) {
                this.issues.push('❌ 频率滑块缺少input事件监听器');
            } else {
                console.log('✅ 频率滑块有input事件监听器');
            }
        }
        
        // 检查频率输入框事件
        const frequencyInput = document.getElementById('frequency-input');
        if (frequencyInput) {
            const hasInputEvent = this.hasEventListener(frequencyInput, 'input');
            if (!hasInputEvent) {
                this.issues.push('❌ 频率输入框缺少input事件监听器');
            } else {
                console.log('✅ 频率输入框有input事件监听器');
            }
        }
        
        // 检查微调按钮事件
        const microButtons = ['freq-down-10', 'freq-down-1', 'freq-up-1', 'freq-up-10'];
        microButtons.forEach(id => {
            const button = document.getElementById(id);
            if (button) {
                const hasClickEvent = this.hasEventListener(button, 'click');
                if (!hasClickEvent) {
                    this.issues.push(`❌ 微调按钮 #${id} 缺少click事件监听器`);
                } else {
                    console.log(`✅ 微调按钮 #${id} 有click事件监听器`);
                }
            }
        });
    }

    /**
     * 检查元素是否有事件监听器
     */
    hasEventListener(element, eventType) {
        // 这是一个简化的检查，实际中很难准确检测
        // 我们通过尝试触发事件来测试
        try {
            const event = new Event(eventType);
            element.dispatchEvent(event);
            return true;
        } catch (error) {
            return false;
        }
    }

    /**
     * 诊断游戏控制器
     */
    diagnoseGameController() {
        console.log('🔍 诊断游戏控制器...');
        
        if (!window.gameController) {
            this.issues.push('❌ 全局gameController对象不存在');
            return;
        }
        
        const requiredMethods = [
            'updateScoreDisplay',
            'updateComboDisplay', 
            'updateTimeDisplay',
            'updateHUDDisplay'
        ];
        
        requiredMethods.forEach(method => {
            if (typeof gameController[method] !== 'function') {
                this.issues.push(`❌ gameController缺少方法: ${method}`);
            } else {
                console.log(`✅ gameController有方法: ${method}`);
            }
        });
        
        // 检查初始化状态
        if (!gameController.isInitialized) {
            this.issues.push('❌ gameController未初始化');
        } else {
            console.log('✅ gameController已初始化');
        }
    }

    /**
     * 诊断输入管理器
     */
    diagnoseInputManager() {
        console.log('🔍 诊断输入管理器...');
        
        if (!window.gameController || !gameController.inputManager) {
            this.issues.push('❌ inputManager不存在');
            return;
        }
        
        const inputManager = gameController.inputManager;
        const requiredMethods = [
            'setFrequency',
            'updateFrequencyDisplay',
            'adjustFrequency'
        ];
        
        requiredMethods.forEach(method => {
            if (typeof inputManager[method] !== 'function') {
                this.issues.push(`❌ inputManager缺少方法: ${method}`);
            } else {
                console.log(`✅ inputManager有方法: ${method}`);
            }
        });
        
        // 检查频率回调
        if (!inputManager.onFrequencyChange) {
            this.issues.push('❌ inputManager缺少onFrequencyChange回调');
        } else {
            console.log('✅ inputManager有onFrequencyChange回调');
        }
    }

    /**
     * 诊断量子引擎
     */
    diagnoseQuantumEngine() {
        console.log('🔍 诊断量子引擎...');
        
        if (!window.quantumEngine) {
            this.issues.push('❌ 全局quantumEngine对象不存在');
            return;
        }
        
        const requiredMethods = [
            'updateScore',
            'updateCombo',
            'getCurrentResonanceStrength'
        ];
        
        requiredMethods.forEach(method => {
            if (typeof quantumEngine[method] !== 'function') {
                this.issues.push(`❌ quantumEngine缺少方法: ${method}`);
            } else {
                console.log(`✅ quantumEngine有方法: ${method}`);
            }
        });
        
        // 检查回调设置
        if (!quantumEngine.onScoreUpdate) {
            this.issues.push('❌ quantumEngine缺少onScoreUpdate回调');
        } else {
            console.log('✅ quantumEngine有onScoreUpdate回调');
        }
        
        if (!quantumEngine.onComboUpdate) {
            this.issues.push('❌ quantumEngine缺少onComboUpdate回调');
        } else {
            console.log('✅ quantumEngine有onComboUpdate回调');
        }
    }

    /**
     * 测试实际功能
     */
    testActualFunctionality() {
        console.log('🔍 测试实际功能...');
        
        // 测试分数更新
        try {
            if (window.gameController && typeof gameController.updateScoreDisplay === 'function') {
                const scoreElement = document.getElementById('current-score');
                const oldValue = scoreElement ? scoreElement.textContent : '';
                
                gameController.updateScoreDisplay(99999, 100);
                
                const newValue = scoreElement ? scoreElement.textContent : '';
                if (oldValue === newValue) {
                    this.issues.push('❌ 分数更新功能不工作');
                } else {
                    console.log('✅ 分数更新功能正常');
                }
            }
        } catch (error) {
            this.issues.push(`❌ 分数更新测试失败: ${error.message}`);
        }
        
        // 测试连击更新
        try {
            if (window.gameController && typeof gameController.updateComboDisplay === 'function') {
                const comboElement = document.getElementById('current-combo');
                const oldValue = comboElement ? comboElement.textContent : '';
                
                gameController.updateComboDisplay(99);
                
                const newValue = comboElement ? comboElement.textContent : '';
                if (oldValue === newValue) {
                    this.issues.push('❌ 连击更新功能不工作');
                } else {
                    console.log('✅ 连击更新功能正常');
                }
            }
        } catch (error) {
            this.issues.push(`❌ 连击更新测试失败: ${error.message}`);
        }
        
        // 测试频率控制
        try {
            const frequencySlider = document.getElementById('frequency-slider');
            const frequencyValue = document.getElementById('frequency-value');
            
            if (frequencySlider && frequencyValue) {
                const oldValue = frequencyValue.textContent;
                
                frequencySlider.value = 880;
                frequencySlider.dispatchEvent(new Event('input'));
                
                setTimeout(() => {
                    const newValue = frequencyValue.textContent;
                    if (oldValue === newValue) {
                        this.issues.push('❌ 频率控制功能不工作');
                    } else {
                        console.log('✅ 频率控制功能正常');
                    }
                }, 100);
            }
        } catch (error) {
            this.issues.push(`❌ 频率控制测试失败: ${error.message}`);
        }
    }

    /**
     * 输出诊断结果
     */
    outputDiagnosisResults() {
        console.log('\n📋 问题诊断结果:');
        console.log('='.repeat(60));
        
        if (this.issues.length === 0) {
            console.log('🎉 没有发现问题！');
        } else {
            console.log(`❌ 发现 ${this.issues.length} 个问题:`);
            this.issues.forEach(issue => console.log(issue));
        }
        
        console.log('='.repeat(60));
    }

    /**
     * 提供解决方案
     */
    provideSolutions() {
        console.log('\n💡 解决方案建议:');
        console.log('='.repeat(60));
        
        if (this.issues.length === 0) {
            console.log('✅ 所有功能正常，无需修复');
            return;
        }
        
        // 根据问题提供解决方案
        this.issues.forEach(issue => {
            if (issue.includes('DOM元素')) {
                this.solutions.push('🔧 检查HTML文件中的元素ID是否正确');
            }
            if (issue.includes('事件监听器')) {
                this.solutions.push('🔧 重新绑定事件监听器');
            }
            if (issue.includes('gameController')) {
                this.solutions.push('🔧 重新初始化游戏控制器');
            }
            if (issue.includes('inputManager')) {
                this.solutions.push('🔧 重新初始化输入管理器');
            }
            if (issue.includes('quantumEngine')) {
                this.solutions.push('🔧 重新初始化量子引擎');
            }
            if (issue.includes('功能不工作')) {
                this.solutions.push('🔧 检查方法实现和回调设置');
            }
        });
        
        // 去重并输出解决方案
        const uniqueSolutions = [...new Set(this.solutions)];
        uniqueSolutions.forEach(solution => console.log(solution));
        
        console.log('\n🚀 建议执行快速修复脚本: window.quickFix.runAllFixes()');
        console.log('='.repeat(60));
    }
}

// 创建诊断实例
window.problemDiagnosis = new ProblemDiagnosis();

// 页面加载完成后自动运行诊断
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.problemDiagnosis) {
            problemDiagnosis.runDiagnosis();
        }
    }, 3000); // 等待3秒让所有脚本加载完成
});

console.log('🔍 问题诊断脚本已加载');
