/**
 * 量子共鸣者 - 量子引擎
 * 处理量子粒子的共鸣机制、连锁反应、能量传递等核心游戏逻辑
 */

class QuantumEngine {
    constructor() {
        this.quantumField = new Map(); // 量子场
        this.resonanceChains = []; // 共鸣链
        this.energyWaves = []; // 能量波
        this.activeResonances = new Set(); // 活跃的共鸣
        
        // 量子参数 - 支持动态难度调整
        this.fieldStrength = 1.0; // 量子场强度
        this.resonanceThreshold = 0.3; // 共鸣阈值（降低以便更容易激活粒子）
        this.chainDecayRate = 0.95; // 链式反应衰减率
        this.waveSpeed = 200; // 能量波传播速度
        this.maxChainLength = 10; // 最大链长度

        // 难度调整相关属性
        this.difficultyConfig = null;
        this.baseDifficulty = {
            fieldStrength: 1.0,
            resonanceThreshold: 0.3,
            chainDecayRate: 0.95,
            waveSpeed: 200,
            maxChainLength: 10
        };
        
        // 频率范围
        this.minFrequency = 20;
        this.maxFrequency = 20000;
        this.targetFrequency = 440; // 当前目标频率
        
        // 游戏状态
        this.score = 0;
        this.combo = 0;
        this.maxCombo = 0; // 最大连击数
        this.level = 1;
        this.isActive = false;
        this.currentResonanceStrength = 0; // 当前共鸣强度

        // 统计数据
        this.particlesActivated = 0; // 激活的粒子总数
        this.chainReactions = 0; // 连锁反应次数
        this.perfectHits = 0; // 完美击中次数
        this.totalHits = 0; // 总击中次数
        
        // 事件回调
        this.onParticleActivated = null;
        this.onChainReaction = null;
        this.onScoreUpdate = null;
        this.onComboUpdate = null;
        
        console.log('⚛️ 量子引擎已创建');
    }

    /**
     * 初始化量子引擎
     */
    init() {
        // 设置物理引擎回调
        physicsEngine.onResonance = (p1, p2, strength) => {
            this.handleResonance(p1, p2, strength);
        };

        physicsEngine.onCollision = (p1, p2) => {
            this.handleCollision(p1, p2);
        };

        // 应用难度配置
        this.applyDifficultyConfig();

        this.isActive = true;
        console.log('⚛️ 量子引擎已初始化');
    }

    /**
     * 应用难度配置
     */
    applyDifficultyConfig() {
        if (window.difficultyConfigManager && window.difficultyConfigManager.initialized) {
            this.difficultyConfig = window.difficultyConfigManager.getGameDifficultyConfig('quantum-resonator');

            if (this.difficultyConfig) {
                const quantumConfig = this.difficultyConfig.gameplay.quantum;

                // 应用量子物理参数
                this.fieldStrength = quantumConfig.fieldStrength;
                this.resonanceThreshold = quantumConfig.resonanceThreshold;
                this.chainDecayRate = quantumConfig.chainDecayRate;
                this.waveSpeed = quantumConfig.waveSpeed;
                this.maxChainLength = quantumConfig.maxChainLength;

                console.log(`🎯 量子引擎应用难度配置: ${this.difficultyConfig.name}`, {
                    场强度: this.fieldStrength,
                    共鸣阈值: this.resonanceThreshold,
                    链衰减率: this.chainDecayRate,
                    波速度: this.waveSpeed,
                    最大链长: this.maxChainLength
                });
            }
        } else {
            // 使用默认配置
            console.log('🎯 量子引擎使用默认难度配置');
        }
    }

    /**
     * 动态调整量子参数
     * @param {Object} adjustments - 调整参数
     */
    adjustQuantumParameters(adjustments) {
        if (adjustments.fieldStrength !== undefined) {
            this.fieldStrength = Math.max(0.1, Math.min(3.0, adjustments.fieldStrength));
        }

        if (adjustments.resonanceThreshold !== undefined) {
            this.resonanceThreshold = Math.max(0.05, Math.min(0.8, adjustments.resonanceThreshold));
        }

        if (adjustments.chainDecayRate !== undefined) {
            this.chainDecayRate = Math.max(0.8, Math.min(0.99, adjustments.chainDecayRate));
        }

        if (adjustments.waveSpeed !== undefined) {
            this.waveSpeed = Math.max(50, Math.min(500, adjustments.waveSpeed));
        }

        if (adjustments.maxChainLength !== undefined) {
            this.maxChainLength = Math.max(3, Math.min(20, adjustments.maxChainLength));
        }

        console.log('⚛️ 量子参数已动态调整:', {
            场强度: this.fieldStrength,
            共鸣阈值: this.resonanceThreshold,
            链衰减率: this.chainDecayRate,
            波速度: this.waveSpeed,
            最大链长: this.maxChainLength
        });
    }

    /**
     * 更新量子引擎
     * @param {number} deltaTime - 时间增量
     */
    update(deltaTime) {
        if (!this.isActive) return;
        
        // 更新能量波
        this.updateEnergyWaves(deltaTime);
        
        // 更新共鸣链
        this.updateResonanceChains(deltaTime);
        
        // 更新量子场
        this.updateQuantumField(deltaTime);
        
        // 检查连锁反应
        this.checkChainReactions();
        
        // 清理过期的共鸣
        this.cleanupResonances();
    }

    /**
     * 设置目标频率
     * @param {number} frequency - 目标频率
     */
    setTargetFrequency(frequency) {
        this.targetFrequency = MathUtils.clamp(frequency, this.minFrequency, this.maxFrequency);
    }

    /**
     * 激活粒子
     * @param {Object} particle - 粒子对象
     * @param {number} inputFrequency - 输入频率
     * @returns {boolean} 是否成功激活
     */
    activateParticle(particle, inputFrequency) {
        if (particle.isActive) return false;
        
        // 计算共鸣强度
        const resonanceStrength = MathUtils.calculateResonance(
            particle.frequency, 
            inputFrequency, 
            100
        );
        
        if (resonanceStrength < this.resonanceThreshold) {
            return false; // 共鸣强度不足
        }
        
        // 激活粒子
        particle.isActive = true;
        particle.resonanceStrength = resonanceStrength;
        particle.lastResonanceTime = Date.now();
        particle.energy = MathUtils.calculateQuantumEnergy(particle.frequency, resonanceStrength);

        // 更新当前共鸣强度
        this.updateCurrentResonanceStrength(resonanceStrength);

        // 创建能量波
        this.createEnergyWave(particle, resonanceStrength);

        // 播放音效
        if (audioEngine.isReady()) {
            audioEngine.playParticleActivation(particle.frequency, particle.energy);
        }

        // 更新统计数据
        this.particlesActivated++;
        this.totalHits++;

        // 判断是否为完美击中（共鸣强度 >= 0.9）
        if (resonanceStrength >= 0.9) {
            this.perfectHits++;
        }

        // 更新分数和连击
        this.updateScore(resonanceStrength);
        this.updateCombo(1);

        // 触发事件
        if (this.onParticleActivated) {
            this.onParticleActivated(particle, resonanceStrength);
        }

        // 触发粒子激活事件（用于教学提示系统）
        this.triggerParticleActivatedEvent(particle, resonanceStrength);

        console.log(`⚛️ 粒子激活: ${particle.id}, 频率: ${particle.frequency}Hz, 共鸣强度: ${resonanceStrength.toFixed(2)}`);
        return true;
    }

    /**
     * 触发粒子激活事件
     * @param {Object} particle - 激活的粒子
     * @param {number} resonanceStrength - 共鸣强度
     */
    triggerParticleActivatedEvent(particle, resonanceStrength) {
        // 创建自定义事件
        const event = new CustomEvent('particleActivated', {
            detail: {
                particle: particle,
                resonanceStrength: resonanceStrength,
                totalActivated: this.particlesActivated,
                score: this.score,
                combo: this.combo
            }
        });

        // 分发事件
        document.dispatchEvent(event);

        // 如果教学提示系统正在运行，通知它
        if (window.tutorialSystem && window.tutorialSystem.isRunning()) {
            console.log('📚 教学提示系统检测到粒子激活');
        }
    }

    /**
     * 创建能量波
     * @param {Object} sourceParticle - 源粒子
     * @param {number} strength - 强度
     */
    createEnergyWave(sourceParticle, strength) {
        const wave = {
            id: this.generateId(),
            x: sourceParticle.x,
            y: sourceParticle.y,
            z: sourceParticle.z,
            radius: 0,
            maxRadius: 150 * strength,
            speed: this.waveSpeed,
            strength: strength,
            frequency: sourceParticle.frequency,
            sourceId: sourceParticle.id,
            affectedParticles: new Set([sourceParticle.id]),
            age: 0,
            maxAge: 2.0,
            isActive: true
        };
        
        this.energyWaves.push(wave);
        return wave;
    }

    /**
     * 更新能量波
     * @param {number} deltaTime - 时间增量
     */
    updateEnergyWaves(deltaTime) {
        this.energyWaves = this.energyWaves.filter(wave => {
            if (!wave.isActive) return false;
            
            // 更新波的属性
            wave.age += deltaTime;
            wave.radius += wave.speed * deltaTime;
            wave.strength *= 0.99; // 强度衰减
            
            // 检查波与粒子的交互
            physicsEngine.particles.forEach(particle => {
                if (wave.affectedParticles.has(particle.id)) return;
                
                const distance = MathUtils.distance(wave.x, wave.y, particle.x, particle.y);
                
                // 检查波是否到达粒子
                if (distance <= wave.radius && distance >= wave.radius - wave.speed * deltaTime) {
                    this.waveHitParticle(wave, particle);
                    wave.affectedParticles.add(particle.id);
                }
            });
            
            // 检查波是否过期
            if (wave.age >= wave.maxAge || wave.radius >= wave.maxRadius || wave.strength < 0.1) {
                wave.isActive = false;
                return false;
            }
            
            return true;
        });
    }

    /**
     * 能量波击中粒子
     * @param {Object} wave - 能量波
     * @param {Object} particle - 粒子
     */
    waveHitParticle(wave, particle) {
        if (particle.isActive) return; // 已激活的粒子不受影响
        
        // 计算共鸣
        const resonance = MathUtils.calculateResonance(
            wave.frequency, 
            particle.frequency, 
            80
        );
        
        const effectiveStrength = wave.strength * resonance;
        
        if (effectiveStrength > this.resonanceThreshold * 0.7) {
            // 激活粒子
            this.activateParticle(particle, wave.frequency);
            
            // 创建连锁反应
            this.createChainReaction(wave, particle, effectiveStrength);
        } else {
            // 部分激活，增加粒子的共鸣强度
            particle.resonanceStrength = Math.min(1.0, particle.resonanceStrength + effectiveStrength * 0.5);
        }
    }

    /**
     * 创建连锁反应
     * @param {Object} sourceWave - 源能量波
     * @param {Object} targetParticle - 目标粒子
     * @param {number} strength - 强度
     */
    createChainReaction(sourceWave, targetParticle, strength) {
        const chain = {
            id: this.generateId(),
            sourceWaveId: sourceWave.id,
            particles: [sourceWave.sourceId, targetParticle.id],
            strength: strength * this.chainDecayRate,
            length: 2,
            isActive: true,
            createdAt: Date.now()
        };
        
        this.resonanceChains.push(chain);

        // 增加连锁反应统计
        this.chainReactions++;

        // 播放连锁反应音效
        if (audioEngine.isReady()) {
            const frequencies = chain.particles.map(id => {
                const p = physicsEngine.getParticle(id);
                return p ? p.frequency : 440;
            });
            audioEngine.playChainReaction(chain.length, frequencies);
        }

        // 触发事件
        if (this.onChainReaction) {
            this.onChainReaction(chain);
        }

        console.log(`🔗 连锁反应: 长度 ${chain.length}, 强度 ${strength.toFixed(2)}`);
    }

    /**
     * 更新共鸣链
     * @param {number} deltaTime - 时间增量
     */
    updateResonanceChains(deltaTime) {
        this.resonanceChains = this.resonanceChains.filter(chain => {
            if (!chain.isActive) return false;
            
            // 检查链是否可以继续扩展
            if (chain.length < this.maxChainLength && chain.strength > 0.3) {
                this.tryExtendChain(chain);
            }
            
            // 衰减强度
            chain.strength *= 0.98;
            
            // 检查链是否过期
            const age = (Date.now() - chain.createdAt) / 1000;
            if (age > 5.0 || chain.strength < 0.1) {
                chain.isActive = false;
                return false;
            }
            
            return true;
        });
    }

    /**
     * 尝试扩展连锁反应
     * @param {Object} chain - 连锁反应对象
     */
    tryExtendChain(chain) {
        const lastParticleId = chain.particles[chain.particles.length - 1];
        const lastParticle = physicsEngine.getParticle(lastParticleId);
        
        if (!lastParticle) return;
        
        // 查找附近未激活的粒子
        const nearbyParticles = physicsEngine.getNearbyParticles(lastParticle, 80);
        
        for (const particle of nearbyParticles) {
            if (particle.isActive || chain.particles.includes(particle.id)) continue;
            
            const resonance = MathUtils.calculateResonance(
                lastParticle.frequency, 
                particle.frequency, 
                60
            );
            
            const effectiveStrength = chain.strength * resonance;
            
            if (effectiveStrength > this.resonanceThreshold * 0.5) {
                // 扩展链
                chain.particles.push(particle.id);
                chain.length++;
                chain.strength = effectiveStrength * this.chainDecayRate;
                
                // 激活粒子
                this.activateParticle(particle, lastParticle.frequency);
                
                // 更新分数（连锁反应有额外奖励）
                this.updateScore(effectiveStrength * chain.length);
                this.updateCombo(1);
                
                break; // 每次只扩展一个粒子
            }
        }
    }

    /**
     * 更新量子场
     * @param {number} deltaTime - 时间增量
     */
    updateQuantumField(deltaTime) {
        // 计算场强度
        const activeParticles = physicsEngine.particles.filter(p => p.isActive);
        const fieldEnergy = activeParticles.reduce((sum, p) => sum + p.energy, 0);
        
        this.fieldStrength = Math.min(2.0, 1.0 + fieldEnergy * 0.01);
        
        // 更新场中的粒子
        physicsEngine.particles.forEach(particle => {
            if (particle.isActive) {
                // 激活的粒子会影响周围的量子场
                this.updateParticleField(particle, deltaTime);
            }
        });
    }

    /**
     * 更新粒子的量子场影响
     * @param {Object} particle - 粒子对象
     * @param {number} deltaTime - 时间增量
     */
    updateParticleField(particle, deltaTime) {
        const fieldKey = `${Math.floor(particle.x / 50)},${Math.floor(particle.y / 50)}`;
        
        if (!this.quantumField.has(fieldKey)) {
            this.quantumField.set(fieldKey, {
                energy: 0,
                frequency: 0,
                particleCount: 0,
                lastUpdate: Date.now()
            });
        }
        
        const field = this.quantumField.get(fieldKey);
        field.energy += particle.energy * deltaTime;
        field.frequency = (field.frequency * field.particleCount + particle.frequency) / (field.particleCount + 1);
        field.particleCount++;
        field.lastUpdate = Date.now();
    }

    /**
     * 检查连锁反应
     */
    checkChainReactions() {
        // 检查是否有新的连锁反应可能
        const activeParticles = physicsEngine.particles.filter(p => p.isActive);
        
        activeParticles.forEach(particle => {
            if (particle.resonanceStrength > 0.8) {
                // 高共鸣强度的粒子可能触发新的连锁反应
                const nearbyInactive = physicsEngine.getNearbyParticles(particle, 60)
                    .filter(p => !p.isActive);
                
                nearbyInactive.forEach(target => {
                    const resonance = MathUtils.calculateResonance(
                        particle.frequency, 
                        target.frequency, 
                        40
                    );
                    
                    if (resonance > 0.7) {
                        // 创建新的能量波
                        this.createEnergyWave(particle, resonance * 0.8);
                    }
                });
            }
        });
    }

    /**
     * 处理共鸣事件
     * @param {Object} p1 - 粒子1
     * @param {Object} p2 - 粒子2
     * @param {number} strength - 共鸣强度
     */
    handleResonance(p1, p2, strength) {
        const resonanceId = `${p1.id}-${p2.id}`;
        
        if (!this.activeResonances.has(resonanceId)) {
            this.activeResonances.add(resonanceId);
            
            // 播放共鸣音效
            if (audioEngine.isReady()) {
                const avgFreq = (p1.frequency + p2.frequency) / 2;
                audioEngine.playResonanceSound(avgFreq, strength);
            }
            
            // 增加粒子能量
            if (p1.isActive) p1.energy += strength * 0.5;
            if (p2.isActive) p2.energy += strength * 0.5;
            
            console.log(`🎵 量子共鸣: ${p1.id} <-> ${p2.id}, 强度: ${strength.toFixed(2)}`);
        }
    }

    /**
     * 处理碰撞事件
     * @param {Object} p1 - 粒子1
     * @param {Object} p2 - 粒子2
     */
    handleCollision(p1, p2) {
        // 碰撞可能导致频率变化
        if (p1.isActive && p2.isActive) {
            const energyTransfer = Math.min(p1.energy, p2.energy) * 0.1;
            p1.energy += energyTransfer;
            p2.energy += energyTransfer;
            
            // 轻微的频率调制
            p1.frequency += MathUtils.random(-5, 5);
            p2.frequency += MathUtils.random(-5, 5);
            
            // 限制频率范围
            p1.frequency = MathUtils.clamp(p1.frequency, this.minFrequency, this.maxFrequency);
            p2.frequency = MathUtils.clamp(p2.frequency, this.minFrequency, this.maxFrequency);
        }
    }

    /**
     * 清理过期的共鸣
     */
    cleanupResonances() {
        // 清理量子场中的过期数据
        const now = Date.now();
        for (const [key, field] of this.quantumField.entries()) {
            if (now - field.lastUpdate > 5000) { // 5秒过期
                this.quantumField.delete(key);
            }
        }
        
        // 清理活跃共鸣
        this.activeResonances.clear();
    }

    /**
     * 更新分数
     * @param {number} baseScore - 基础分数
     */
    updateScore(baseScore) {
        const multiplier = 1 + (this.combo * 0.1); // 连击倍数
        const levelBonus = this.level * 0.05; // 关卡奖励
        const finalScore = Math.floor(baseScore * 100 * multiplier * (1 + levelBonus));
        
        this.score += finalScore;
        
        if (this.onScoreUpdate) {
            this.onScoreUpdate(this.score, finalScore);
        }
    }

    /**
     * 更新连击
     * @param {number} increment - 增量
     */
    updateCombo(increment) {
        this.combo += increment;

        // 更新最大连击数
        this.maxCombo = Math.max(this.maxCombo, this.combo);

        if (this.onComboUpdate) {
            this.onComboUpdate(this.combo);
        }
    }

    /**
     * 重置连击
     */
    resetCombo() {
        this.combo = 0;
        if (this.onComboUpdate) {
            this.onComboUpdate(this.combo);
        }
    }

    /**
     * 生成唯一ID
     * @returns {string} 唯一ID
     */
    generateId() {
        return 'quantum_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    /**
     * 获取量子引擎统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        return {
            score: this.score,
            combo: this.combo,
            maxCombo: this.maxCombo,
            level: this.level,
            fieldStrength: this.fieldStrength,
            activeWaves: this.energyWaves.length,
            activeChains: this.resonanceChains.length,
            quantumFieldSize: this.quantumField.size,
            targetFrequency: this.targetFrequency,
            particlesActivated: this.particlesActivated,
            chainReactions: this.chainReactions,
            perfectHits: this.perfectHits,
            totalHits: this.totalHits,
            accuracy: this.totalHits > 0 ? (this.perfectHits / this.totalHits) : 0
        };
    }

    /**
     * 重置量子引擎
     */
    reset() {
        this.score = 0;
        this.combo = 0;
        this.maxCombo = 0;
        this.level = 1;
        this.particlesActivated = 0;
        this.chainReactions = 0;
        this.perfectHits = 0;
        this.totalHits = 0;
        this.energyWaves.length = 0;
        this.resonanceChains.length = 0;
        this.quantumField.clear();
        this.activeResonances.clear();
        this.fieldStrength = 1.0;
        this.targetFrequency = 440;

        console.log('⚛️ 量子引擎已重置');
    }

    /**
     * 清空量子引擎
     */
    clear() {
        // 清空所有动态数据，但保留设置
        this.energyWaves.length = 0;
        this.resonanceChains.length = 0;
        this.quantumField.clear();
        this.activeResonances.clear();

        console.log('⚛️ 量子引擎已清空');
    }

    /**
     * 销毁量子引擎
     */
    destroy() {
        this.reset();
        this.isActive = false;
        console.log('⚛️ 量子引擎已销毁');
    }

    /**
     * 获取当前共鸣强度
     * @returns {number} 共鸣强度 (0-1)
     */
    getCurrentResonanceStrength() {
        return this.currentResonanceStrength || 0;
    }

    /**
     * 更新当前共鸣强度
     * @param {number} strength - 共鸣强度
     */
    updateCurrentResonanceStrength(strength) {
        this.currentResonanceStrength = Math.max(0, Math.min(1, strength));
    }
}

// 导出类到全局作用域
window.QuantumEngine = QuantumEngine;

// 创建全局量子引擎实例
window.quantumEngine = new QuantumEngine();
