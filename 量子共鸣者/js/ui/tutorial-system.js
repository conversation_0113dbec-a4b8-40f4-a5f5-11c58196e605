/**
 * 量子共鸣者 - 教学提示系统
 * 负责游戏的新手引导和教学提示功能
 */

class TutorialSystem {
    constructor() {
        this.isInitialized = false;
        this.isActive = false;
        this.currentStep = 0;
        this.currentTutorial = null;
        
        // 教学步骤数据
        this.tutorialSteps = [];
        
        // UI元素
        this.elements = {
            overlay: null,
            tooltip: null,
            highlight: null,
            arrow: null,
            skipButton: null,
            nextButton: null,
            prevButton: null,
            progressBar: null
        };
        
        // 配置选项
        this.config = {
            showSkipButton: true,
            autoAdvance: false,
            highlightPadding: 10,
            tooltipOffset: 20,
            animationDuration: 300
        };
        
        // 事件回调
        this.callbacks = {
            onStart: null,
            onStep: null,
            onComplete: null,
            onSkip: null
        };
        
        console.log('📚 教学提示系统已创建');
    }

    /**
     * 初始化教学提示系统
     */
    init() {
        try {
            // 创建UI结构
            this.createTutorialUI();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 加载预定义教程
            this.loadPredefinedTutorials();
            
            this.isInitialized = true;
            console.log('✅ 教学提示系统初始化完成');
            return true;
        } catch (error) {
            console.error('❌ 教学提示系统初始化失败:', error);
            return false;
        }
    }

    /**
     * 创建教学UI结构
     */
    createTutorialUI() {
        // 创建遮罩层
        this.elements.overlay = document.createElement('div');
        this.elements.overlay.className = 'tutorial-overlay';
        this.elements.overlay.style.display = 'none';
        
        // 创建提示框
        this.elements.tooltip = document.createElement('div');
        this.elements.tooltip.className = 'tutorial-tooltip';
        
        // 创建高亮区域
        this.elements.highlight = document.createElement('div');
        this.elements.highlight.className = 'tutorial-highlight';
        
        // 创建指示箭头
        this.elements.arrow = document.createElement('div');
        this.elements.arrow.className = 'tutorial-arrow';
        
        // 创建控制按钮
        this.createControlButtons();
        
        // 创建进度条
        this.elements.progressBar = document.createElement('div');
        this.elements.progressBar.className = 'tutorial-progress';
        this.elements.progressBar.innerHTML = `
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
            <span class="progress-text">1 / 1</span>
        `;
        
        // 组装UI结构
        this.elements.overlay.appendChild(this.elements.highlight);
        this.elements.overlay.appendChild(this.elements.arrow);
        this.elements.overlay.appendChild(this.elements.tooltip);
        this.elements.overlay.appendChild(this.elements.progressBar);
        
        // 添加到页面
        document.body.appendChild(this.elements.overlay);
    }

    /**
     * 创建控制按钮
     */
    createControlButtons() {
        // 跳过按钮
        this.elements.skipButton = document.createElement('button');
        this.elements.skipButton.className = 'tutorial-btn skip-btn';
        this.elements.skipButton.innerHTML = '跳过教程';
        
        // 下一步按钮
        this.elements.nextButton = document.createElement('button');
        this.elements.nextButton.className = 'tutorial-btn next-btn primary';
        this.elements.nextButton.innerHTML = '下一步';
        
        // 上一步按钮
        this.elements.prevButton = document.createElement('button');
        this.elements.prevButton.className = 'tutorial-btn prev-btn';
        this.elements.prevButton.innerHTML = '上一步';
        this.elements.prevButton.style.display = 'none';
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 跳过按钮
        this.elements.skipButton.addEventListener('click', () => {
            this.skipTutorial();
        });
        
        // 下一步按钮
        this.elements.nextButton.addEventListener('click', () => {
            this.nextStep();
        });
        
        // 上一步按钮
        this.elements.prevButton.addEventListener('click', () => {
            this.prevStep();
        });
        
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (!this.isActive) return;
            
            switch (e.key) {
                case 'Escape':
                    this.skipTutorial();
                    break;
                case 'ArrowRight':
                case 'Space':
                    e.preventDefault();
                    this.nextStep();
                    break;
                case 'ArrowLeft':
                    e.preventDefault();
                    this.prevStep();
                    break;
            }
        });
        
        // 点击遮罩层外部区域继续
        this.elements.overlay.addEventListener('click', (e) => {
            if (e.target === this.elements.overlay) {
                this.nextStep();
            }
        });
    }

    /**
     * 加载预定义教程
     */
    loadPredefinedTutorials() {
        // 第一关教程步骤
        this.registerTutorial('level1', {
            name: '量子共鸣入门',
            description: '学习基础的量子共鸣操作',
            steps: [
                {
                    title: '欢迎来到量子共鸣者！',
                    content: '在这个游戏中，你将学会操控量子粒子，创造美妙的共鸣效果。让我们开始第一课吧！',
                    target: null,
                    position: 'center',
                    action: 'none'
                },
                {
                    title: '认识游戏界面',
                    content: '这是游戏的主要区域，量子粒子会在这里显示。你可以看到几个发光的粒子正在等待激活。',
                    target: '#game-canvas',
                    position: 'bottom',
                    action: 'highlight'
                },
                {
                    title: '频率控制面板',
                    content: '这是频率控制面板。通过调节频率滑块，你可以改变目标频率来激活不同的粒子。',
                    target: '.frequency-panel',
                    position: 'top',
                    action: 'highlight'
                },
                {
                    title: '点击激活粒子',
                    content: '现在试着点击画面中的任意一个粒子来激活它。当粒子的频率与你设定的目标频率接近时，它就会被激活！',
                    target: '#game-canvas',
                    position: 'top',
                    action: 'wait_for_click',
                    waitFor: 'particle_activated'
                },
                {
                    title: '观察共鸣效果',
                    content: '太棒了！你成功激活了一个粒子。注意观察粒子之间的连接线，当多个粒子被激活时会产生共鸣效果。',
                    target: '#game-canvas',
                    position: 'bottom',
                    action: 'highlight'
                },
                {
                    title: '查看得分和目标',
                    content: '你的得分会显示在这里。完成关卡需要达到目标分数并满足特定条件。',
                    target: '.score-info',
                    position: 'bottom',
                    action: 'highlight'
                },
                {
                    title: '继续探索',
                    content: '现在你已经掌握了基础操作！继续激活更多粒子，创造连锁反应，完成这个关卡吧！',
                    target: null,
                    position: 'center',
                    action: 'none'
                }
            ]
        });
    }

    /**
     * 注册教程
     * @param {string} id - 教程ID
     * @param {Object} tutorial - 教程数据
     */
    registerTutorial(id, tutorial) {
        if (!this.tutorials) {
            this.tutorials = new Map();
        }
        this.tutorials.set(id, tutorial);
        console.log(`📚 已注册教程: ${tutorial.name}`);
    }

    /**
     * 开始教程
     * @param {string} tutorialId - 教程ID
     * @param {Object} options - 选项
     */
    startTutorial(tutorialId, options = {}) {
        if (!this.isInitialized) {
            console.warn('⚠️ 教学提示系统未初始化');
            return false;
        }

        const tutorial = this.tutorials?.get(tutorialId);
        if (!tutorial) {
            console.error(`❌ 未找到教程: ${tutorialId}`);
            return false;
        }

        this.currentTutorial = tutorial;
        this.tutorialSteps = tutorial.steps;
        this.currentStep = 0;
        this.isActive = true;

        // 应用选项
        Object.assign(this.config, options);

        // 显示教程界面
        this.showTutorial();

        // 显示第一步
        this.showStep(0);

        // 触发开始回调
        if (this.callbacks.onStart) {
            this.callbacks.onStart(tutorial);
        }

        console.log(`📚 开始教程: ${tutorial.name}`);
        return true;
    }

    /**
     * 显示教程界面
     */
    showTutorial() {
        this.elements.overlay.style.display = 'block';
        this.elements.overlay.classList.add('tutorial-fade-in');

        setTimeout(() => {
            this.elements.overlay.classList.remove('tutorial-fade-in');
        }, this.config.animationDuration);
    }

    /**
     * 隐藏教程界面
     */
    hideTutorial() {
        this.elements.overlay.classList.add('tutorial-fade-out');

        setTimeout(() => {
            this.elements.overlay.style.display = 'none';
            this.elements.overlay.classList.remove('tutorial-fade-out');
        }, this.config.animationDuration);
    }

    /**
     * 显示指定步骤
     * @param {number} stepIndex - 步骤索引
     */
    showStep(stepIndex) {
        if (stepIndex < 0 || stepIndex >= this.tutorialSteps.length) {
            return;
        }

        this.currentStep = stepIndex;
        const step = this.tutorialSteps[stepIndex];

        // 更新提示框内容
        this.updateTooltip(step);

        // 更新高亮区域
        this.updateHighlight(step);

        // 更新箭头指示
        this.updateArrow(step);

        // 更新进度条
        this.updateProgress();

        // 更新按钮状态
        this.updateButtons();

        // 执行步骤动作
        this.executeStepAction(step);

        // 触发步骤回调
        if (this.callbacks.onStep) {
            this.callbacks.onStep(step, stepIndex);
        }

        console.log(`📚 显示教程步骤 ${stepIndex + 1}/${this.tutorialSteps.length}: ${step.title}`);
    }

    /**
     * 更新提示框内容
     * @param {Object} step - 步骤数据
     */
    updateTooltip(step) {
        this.elements.tooltip.innerHTML = `
            <div class="tooltip-header">
                <h3 class="tooltip-title">${step.title}</h3>
                ${this.config.showSkipButton ? '<button class="tooltip-close">×</button>' : ''}
            </div>
            <div class="tooltip-content">
                <p class="tooltip-text">${step.content}</p>
            </div>
            <div class="tooltip-footer">
                <div class="tooltip-buttons">
                    ${this.currentStep > 0 ? '<button class="tutorial-btn prev-btn">上一步</button>' : ''}
                    ${this.currentStep < this.tutorialSteps.length - 1 ? '<button class="tutorial-btn next-btn primary">下一步</button>' : '<button class="tutorial-btn next-btn primary">完成</button>'}
                </div>
                ${this.config.showSkipButton ? '<button class="tutorial-btn skip-btn">跳过教程</button>' : ''}
            </div>
        `;

        // 重新绑定按钮事件
        this.bindTooltipEvents();

        // 定位提示框
        this.positionTooltip(step);
    }

    /**
     * 绑定提示框事件
     */
    bindTooltipEvents() {
        const nextBtn = this.elements.tooltip.querySelector('.next-btn');
        const prevBtn = this.elements.tooltip.querySelector('.prev-btn');
        const skipBtn = this.elements.tooltip.querySelector('.skip-btn');
        const closeBtn = this.elements.tooltip.querySelector('.tooltip-close');

        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextStep());
        }
        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.prevStep());
        }
        if (skipBtn) {
            skipBtn.addEventListener('click', () => this.skipTutorial());
        }
        if (closeBtn) {
            closeBtn.addEventListener('click', () => this.skipTutorial());
        }
    }

    /**
     * 定位提示框
     * @param {Object} step - 步骤数据
     */
    positionTooltip(step) {
        if (!step.target) {
            // 居中显示
            this.elements.tooltip.style.position = 'fixed';
            this.elements.tooltip.style.top = '50%';
            this.elements.tooltip.style.left = '50%';
            this.elements.tooltip.style.transform = 'translate(-50%, -50%)';
            return;
        }

        const targetElement = document.querySelector(step.target);
        if (!targetElement) {
            console.warn(`⚠️ 未找到目标元素: ${step.target}`);
            return;
        }

        const targetRect = targetElement.getBoundingClientRect();
        const tooltipRect = this.elements.tooltip.getBoundingClientRect();

        let top, left;

        switch (step.position) {
            case 'top':
                top = targetRect.top - tooltipRect.height - this.config.tooltipOffset;
                left = targetRect.left + (targetRect.width - tooltipRect.width) / 2;
                break;
            case 'bottom':
                top = targetRect.bottom + this.config.tooltipOffset;
                left = targetRect.left + (targetRect.width - tooltipRect.width) / 2;
                break;
            case 'left':
                top = targetRect.top + (targetRect.height - tooltipRect.height) / 2;
                left = targetRect.left - tooltipRect.width - this.config.tooltipOffset;
                break;
            case 'right':
                top = targetRect.top + (targetRect.height - tooltipRect.height) / 2;
                left = targetRect.right + this.config.tooltipOffset;
                break;
            default:
                top = targetRect.bottom + this.config.tooltipOffset;
                left = targetRect.left + (targetRect.width - tooltipRect.width) / 2;
        }

        // 确保提示框在视窗内
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;

        if (left < 0) left = 10;
        if (left + tooltipRect.width > viewportWidth) left = viewportWidth - tooltipRect.width - 10;
        if (top < 0) top = 10;
        if (top + tooltipRect.height > viewportHeight) top = viewportHeight - tooltipRect.height - 10;

        this.elements.tooltip.style.position = 'fixed';
        this.elements.tooltip.style.top = `${top}px`;
        this.elements.tooltip.style.left = `${left}px`;
        this.elements.tooltip.style.transform = 'none';
    }

    /**
     * 更新高亮区域
     * @param {Object} step - 步骤数据
     */
    updateHighlight(step) {
        if (!step.target || step.action !== 'highlight') {
            this.elements.highlight.style.display = 'none';
            return;
        }

        const targetElement = document.querySelector(step.target);
        if (!targetElement) {
            this.elements.highlight.style.display = 'none';
            return;
        }

        const rect = targetElement.getBoundingClientRect();
        const padding = this.config.highlightPadding;

        this.elements.highlight.style.display = 'block';
        this.elements.highlight.style.position = 'fixed';
        this.elements.highlight.style.top = `${rect.top - padding}px`;
        this.elements.highlight.style.left = `${rect.left - padding}px`;
        this.elements.highlight.style.width = `${rect.width + padding * 2}px`;
        this.elements.highlight.style.height = `${rect.height + padding * 2}px`;
    }

    /**
     * 更新箭头指示
     * @param {Object} step - 步骤数据
     */
    updateArrow(step) {
        if (!step.target) {
            this.elements.arrow.style.display = 'none';
            return;
        }

        const targetElement = document.querySelector(step.target);
        if (!targetElement) {
            this.elements.arrow.style.display = 'none';
            return;
        }

        const rect = targetElement.getBoundingClientRect();
        this.elements.arrow.style.display = 'block';
        this.elements.arrow.style.position = 'fixed';

        // 根据位置调整箭头方向和位置
        switch (step.position) {
            case 'top':
                this.elements.arrow.className = 'tutorial-arrow arrow-down';
                this.elements.arrow.style.top = `${rect.top - 20}px`;
                this.elements.arrow.style.left = `${rect.left + rect.width / 2 - 10}px`;
                break;
            case 'bottom':
                this.elements.arrow.className = 'tutorial-arrow arrow-up';
                this.elements.arrow.style.top = `${rect.bottom + 5}px`;
                this.elements.arrow.style.left = `${rect.left + rect.width / 2 - 10}px`;
                break;
            case 'left':
                this.elements.arrow.className = 'tutorial-arrow arrow-right';
                this.elements.arrow.style.top = `${rect.top + rect.height / 2 - 10}px`;
                this.elements.arrow.style.left = `${rect.left - 20}px`;
                break;
            case 'right':
                this.elements.arrow.className = 'tutorial-arrow arrow-left';
                this.elements.arrow.style.top = `${rect.top + rect.height / 2 - 10}px`;
                this.elements.arrow.style.left = `${rect.right + 5}px`;
                break;
            default:
                this.elements.arrow.style.display = 'none';
        }
    }

    /**
     * 更新进度条
     */
    updateProgress() {
        const progress = ((this.currentStep + 1) / this.tutorialSteps.length) * 100;
        const progressFill = this.elements.progressBar.querySelector('.progress-fill');
        const progressText = this.elements.progressBar.querySelector('.progress-text');

        if (progressFill) {
            progressFill.style.width = `${progress}%`;
        }
        if (progressText) {
            progressText.textContent = `${this.currentStep + 1} / ${this.tutorialSteps.length}`;
        }
    }

    /**
     * 更新按钮状态
     */
    updateButtons() {
        // 这个方法在updateTooltip中已经处理了按钮状态
        // 这里可以添加额外的按钮状态逻辑
    }

    /**
     * 执行步骤动作
     * @param {Object} step - 步骤数据
     */
    executeStepAction(step) {
        switch (step.action) {
            case 'wait_for_click':
                this.waitForUserAction(step.waitFor);
                break;
            case 'highlight':
                // 高亮效果已在updateHighlight中处理
                break;
            case 'none':
            default:
                // 无特殊动作
                break;
        }
    }

    /**
     * 等待用户动作
     * @param {string} actionType - 动作类型
     */
    waitForUserAction(actionType) {
        switch (actionType) {
            case 'particle_activated':
                this.waitForParticleActivation();
                break;
            default:
                console.warn(`⚠️ 未知的等待动作类型: ${actionType}`);
        }
    }

    /**
     * 等待粒子激活
     */
    waitForParticleActivation() {
        // 监听粒子激活事件
        const onParticleActivated = (event) => {
            console.log('📚 教学提示系统检测到粒子激活事件', event.detail);

            // 移除事件监听器
            document.removeEventListener('particleActivated', onParticleActivated);

            // 延迟进入下一步，让用户看到激活效果
            setTimeout(() => {
                if (this.isActive) {
                    this.nextStep();
                }
            }, 1500); // 延迟1.5秒让用户看到效果
        };

        // 添加事件监听器
        document.addEventListener('particleActivated', onParticleActivated);

        // 备用检查机制（防止事件丢失）
        const checkActivation = () => {
            if (!this.isActive) return; // 教程已结束

            if (window.quantumEngine && window.quantumEngine.particlesActivated > 0) {
                // 粒子已激活，移除事件监听器并进入下一步
                document.removeEventListener('particleActivated', onParticleActivated);
                setTimeout(() => {
                    if (this.isActive) {
                        this.nextStep();
                    }
                }, 1000);
                return;
            }
            // 继续检查
            setTimeout(checkActivation, 500);
        };

        // 启动备用检查
        setTimeout(checkActivation, 2000); // 2秒后开始备用检查
    }

    /**
     * 下一步
     */
    nextStep() {
        if (this.currentStep < this.tutorialSteps.length - 1) {
            this.showStep(this.currentStep + 1);
        } else {
            this.completeTutorial();
        }
    }

    /**
     * 上一步
     */
    prevStep() {
        if (this.currentStep > 0) {
            this.showStep(this.currentStep - 1);
        }
    }

    /**
     * 跳过教程
     */
    skipTutorial() {
        this.isActive = false;
        this.hideTutorial();

        // 触发跳过回调
        if (this.callbacks.onSkip) {
            this.callbacks.onSkip(this.currentTutorial);
        }

        console.log('📚 教程已跳过');
    }

    /**
     * 完成教程
     */
    completeTutorial() {
        this.isActive = false;
        this.hideTutorial();

        // 触发完成回调
        if (this.callbacks.onComplete) {
            this.callbacks.onComplete(this.currentTutorial);
        }

        // 显示完成通知
        if (window.notificationSystem) {
            window.notificationSystem.success('恭喜！你已完成教程学习', {
                title: '教程完成',
                duration: 3000
            });
        }

        console.log('📚 教程已完成');
    }

    /**
     * 设置回调函数
     * @param {string} event - 事件名称
     * @param {Function} callback - 回调函数
     */
    on(event, callback) {
        if (this.callbacks.hasOwnProperty(event)) {
            this.callbacks[event] = callback;
        }
    }

    /**
     * 检查是否正在进行教程
     * @returns {boolean}
     */
    isRunning() {
        return this.isActive;
    }

    /**
     * 获取当前教程信息
     * @returns {Object|null}
     */
    getCurrentTutorial() {
        return this.currentTutorial;
    }

    /**
     * 获取当前步骤信息
     * @returns {Object|null}
     */
    getCurrentStep() {
        if (this.currentStep >= 0 && this.currentStep < this.tutorialSteps.length) {
            return this.tutorialSteps[this.currentStep];
        }
        return null;
    }
}

// 创建全局实例
window.tutorialSystem = new TutorialSystem();

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', () => {
    if (window.tutorialSystem && !window.tutorialSystem.isInitialized) {
        window.tutorialSystem.init();
    }
});
