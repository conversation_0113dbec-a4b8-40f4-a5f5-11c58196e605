/**
 * 量子共鸣者 - 手动UI测试脚本
 * 用于手动验证所有修复的UI功能
 */

// 等待页面加载完成后运行测试
document.addEventListener('DOMContentLoaded', () => {
    console.log('🚀 页面加载完成，开始手动UI测试...');
    
    setTimeout(() => {
        runManualTests();
    }, 1000);
});

function runManualTests() {
    console.log('='.repeat(50));
    console.log('🧪 开始手动UI功能测试');
    console.log('='.repeat(50));
    
    // 测试1: 检查主菜单按钮是否存在
    testMainMenuButtons();
    
    // 测试2: 检查设置面板功能
    testSettingsPanel();
    
    // 测试3: 检查应用程序初始化
    testAppInitialization();
    
    console.log('='.repeat(50));
    console.log('✅ 手动测试完成！请查看上面的测试结果。');
    console.log('='.repeat(50));
}

function testMainMenuButtons() {
    console.log('\n🔘 测试主菜单按钮...');
    
    const buttons = [
        { id: 'start-game-btn', name: '开始游戏' },
        { id: 'level-editor-btn', name: '关卡编辑器' },
        { id: 'achievements-btn', name: '成就系统' },
        { id: 'leaderboard-btn', name: '排行榜' },
        { id: 'settings-btn', name: '设置' }
    ];
    
    buttons.forEach(button => {
        const element = document.getElementById(button.id);
        if (element) {
            console.log(`✅ ${button.name} 按钮存在 (ID: ${button.id})`);
            
            // 检查是否有点击事件监听器
            const hasClickHandler = element.onclick || 
                                  element.addEventListener || 
                                  element.hasAttribute('onclick');
            
            if (hasClickHandler) {
                console.log(`   ✅ 按钮有点击处理程序`);
            } else {
                console.log(`   ⚠️  按钮可能缺少点击处理程序`);
            }
        } else {
            console.log(`❌ ${button.name} 按钮不存在 (ID: ${button.id})`);
        }
    });
}

function testSettingsPanel() {
    console.log('\n⚙️ 测试设置面板...');
    
    // 检查设置面板容器
    const settingsScreen = document.getElementById('settings-screen');
    if (settingsScreen) {
        console.log('✅ 设置面板容器存在');
        
        // 检查设置控件
        const sliders = settingsScreen.querySelectorAll('.setting-slider');
        const checkboxes = settingsScreen.querySelectorAll('.setting-checkbox');
        const selects = settingsScreen.querySelectorAll('.setting-select');
        
        console.log(`   📊 滑块数量: ${sliders.length}`);
        console.log(`   ☑️  复选框数量: ${checkboxes.length}`);
        console.log(`   📋 下拉选择数量: ${selects.length}`);
        
        // 检查重要按钮
        const resetBtn = document.getElementById('reset-settings-btn');
        const saveBtn = document.getElementById('save-settings-btn');
        
        if (resetBtn) {
            console.log('✅ 重置设置按钮存在');
        } else {
            console.log('❌ 重置设置按钮不存在');
        }
        
        if (saveBtn) {
            console.log('✅ 保存设置按钮存在');
        } else {
            console.log('❌ 保存设置按钮不存在');
        }
        
    } else {
        console.log('❌ 设置面板容器不存在');
    }
}

function testAppInitialization() {
    console.log('\n🚀 测试应用程序初始化...');
    
    // 检查全局应用对象
    if (window.app) {
        console.log('✅ 全局应用对象存在');
        
        // 检查关键方法
        const methods = ['startGame', 'showSettings', 'openLevelEditor', 'openAchievements', 'openLeaderboard'];
        methods.forEach(method => {
            if (typeof window.app[method] === 'function') {
                console.log(`   ✅ ${method} 方法存在`);
            } else {
                console.log(`   ❌ ${method} 方法不存在`);
            }
        });
        
    } else {
        console.log('❌ 全局应用对象不存在');
    }
    
    // 检查设置面板对象
    if (window.settingsPanel) {
        console.log('✅ 设置面板对象存在');
    } else {
        console.log('❌ 设置面板对象不存在');
    }
    
    // 检查UI管理器
    if (window.uiManager) {
        console.log('✅ UI管理器存在');
    } else {
        console.log('❌ UI管理器不存在');
    }
}

// 提供手动测试按钮功能
function testButtonClick(buttonId) {
    console.log(`\n🖱️ 手动测试按钮点击: ${buttonId}`);
    const button = document.getElementById(buttonId);
    if (button) {
        try {
            button.click();
            console.log('✅ 按钮点击成功');
        } catch (error) {
            console.log('❌ 按钮点击失败:', error.message);
        }
    } else {
        console.log('❌ 按钮不存在');
    }
}

// 导出测试函数供控制台使用
window.testButtonClick = testButtonClick;
window.runManualTests = runManualTests;

console.log('📝 手动测试脚本已加载');
console.log('💡 可以在控制台中使用以下命令:');
console.log('   - runManualTests() : 运行所有测试');
console.log('   - testButtonClick("button-id") : 测试特定按钮');
