/**
 * 量子共鸣者 - 教学提示系统样式
 * 为教学引导提供现代化的视觉效果
 */

/* 教程遮罩层 */
.tutorial-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 10000;
    pointer-events: auto;
    backdrop-filter: blur(2px);
}

/* 淡入淡出动画 */
.tutorial-fade-in {
    animation: tutorialFadeIn 0.3s ease-out;
}

.tutorial-fade-out {
    animation: tutorialFadeOut 0.3s ease-out;
}

@keyframes tutorialFadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes tutorialFadeOut {
    from {
        opacity: 1;
    }
    to {
        opacity: 0;
    }
}

/* 高亮区域 */
.tutorial-highlight {
    position: fixed;
    border: 3px solid #00d4ff;
    border-radius: 8px;
    background: rgba(0, 212, 255, 0.1);
    box-shadow: 
        0 0 20px rgba(0, 212, 255, 0.5),
        inset 0 0 20px rgba(0, 212, 255, 0.1);
    animation: tutorialPulse 2s infinite;
    pointer-events: none;
    z-index: 10001;
}

@keyframes tutorialPulse {
    0%, 100% {
        box-shadow: 
            0 0 20px rgba(0, 212, 255, 0.5),
            inset 0 0 20px rgba(0, 212, 255, 0.1);
    }
    50% {
        box-shadow: 
            0 0 30px rgba(0, 212, 255, 0.8),
            inset 0 0 30px rgba(0, 212, 255, 0.2);
    }
}

/* 提示框 */
.tutorial-tooltip {
    position: fixed;
    max-width: 400px;
    min-width: 300px;
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
    border: 2px solid #00d4ff;
    border-radius: 12px;
    box-shadow: 
        0 10px 30px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(0, 212, 255, 0.3);
    z-index: 10002;
    animation: tooltipSlideIn 0.3s ease-out;
    font-family: 'Microsoft YaHei', sans-serif;
}

@keyframes tooltipSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8) translateY(-20px);
    }
    to {
        opacity: 1;
        transform: scale(1) translateY(0);
    }
}

/* 提示框头部 */
.tooltip-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px 12px;
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
}

.tooltip-title {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: #00d4ff;
    text-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.tooltip-close {
    background: none;
    border: none;
    color: #888;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.tooltip-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
}

/* 提示框内容 */
.tooltip-content {
    padding: 16px 20px;
}

.tooltip-text {
    margin: 0;
    font-size: 16px;
    line-height: 1.6;
    color: #e0e0e0;
}

/* 提示框底部 */
.tooltip-footer {
    padding: 12px 20px 16px;
    border-top: 1px solid rgba(0, 212, 255, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 10px;
}

.tooltip-buttons {
    display: flex;
    gap: 10px;
}

/* 教程按钮 */
.tutorial-btn {
    padding: 8px 16px;
    border: 2px solid transparent;
    border-radius: 6px;
    background: rgba(255, 255, 255, 0.1);
    color: #e0e0e0;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    outline: none;
}

.tutorial-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.tutorial-btn.primary {
    background: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    color: #fff;
    border-color: #00d4ff;
    box-shadow: 0 4px 15px rgba(0, 212, 255, 0.3);
}

.tutorial-btn.primary:hover {
    background: linear-gradient(135deg, #00e6ff 0%, #00b3e6 100%);
    box-shadow: 0 6px 20px rgba(0, 212, 255, 0.4);
    transform: translateY(-2px);
}

.tutorial-btn.skip-btn {
    background: rgba(255, 255, 255, 0.05);
    color: #888;
    font-size: 12px;
}

.tutorial-btn.skip-btn:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ccc;
}

/* 指示箭头 */
.tutorial-arrow {
    position: fixed;
    width: 20px;
    height: 20px;
    z-index: 10001;
    animation: arrowBounce 1.5s infinite;
}

.tutorial-arrow::before {
    content: '';
    position: absolute;
    width: 0;
    height: 0;
    border-style: solid;
}

/* 箭头方向 */
.arrow-up::before {
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 15px solid #00d4ff;
    top: 0;
    left: 0;
}

.arrow-down::before {
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-top: 15px solid #00d4ff;
    bottom: 0;
    left: 0;
}

.arrow-left::before {
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-right: 15px solid #00d4ff;
    top: 0;
    right: 0;
}

.arrow-right::before {
    border-top: 10px solid transparent;
    border-bottom: 10px solid transparent;
    border-left: 15px solid #00d4ff;
    top: 0;
    left: 0;
}

@keyframes arrowBounce {
    0%, 100% {
        transform: translateY(0);
        filter: drop-shadow(0 0 5px rgba(0, 212, 255, 0.5));
    }
    50% {
        transform: translateY(-5px);
        filter: drop-shadow(0 0 10px rgba(0, 212, 255, 0.8));
    }
}

/* 进度条 */
.tutorial-progress {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(26, 26, 46, 0.9);
    border: 1px solid rgba(0, 212, 255, 0.3);
    border-radius: 20px;
    padding: 8px 16px;
    display: flex;
    align-items: center;
    gap: 10px;
    z-index: 10003;
    backdrop-filter: blur(10px);
}

.progress-bar {
    width: 100px;
    height: 6px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #00d4ff 0%, #0099cc 100%);
    border-radius: 3px;
    transition: width 0.3s ease;
    box-shadow: 0 0 10px rgba(0, 212, 255, 0.5);
}

.progress-text {
    font-size: 12px;
    color: #00d4ff;
    font-weight: 500;
    min-width: 40px;
    text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .tutorial-tooltip {
        max-width: 90vw;
        min-width: 280px;
        margin: 0 20px;
    }
    
    .tooltip-header {
        padding: 12px 16px 8px;
    }
    
    .tooltip-title {
        font-size: 16px;
    }
    
    .tooltip-content {
        padding: 12px 16px;
    }
    
    .tooltip-text {
        font-size: 14px;
    }
    
    .tooltip-footer {
        padding: 8px 16px 12px;
        flex-direction: column;
        align-items: stretch;
    }
    
    .tooltip-buttons {
        justify-content: center;
    }
    
    .tutorial-progress {
        top: 10px;
        right: 10px;
        padding: 6px 12px;
    }
    
    .progress-bar {
        width: 80px;
    }
}

/* 暗色主题适配 */
@media (prefers-color-scheme: dark) {
    .tutorial-tooltip {
        background: linear-gradient(135deg, #0f0f1a 0%, #1a1a2e 100%);
    }
    
    .tooltip-text {
        color: #f0f0f0;
    }
}
