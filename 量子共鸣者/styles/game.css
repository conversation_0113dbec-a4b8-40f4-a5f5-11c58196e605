/* 量子共鸣者 - 游戏界面样式 */

/* 游戏屏幕布局 */
#game-screen {
    flex-direction: column;
    padding: 0;
    background: var(--bg-gradient);
}

/* 游戏HUD样式 */
.game-hud {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 1rem;
    background: linear-gradient(180deg, rgba(13, 20, 33, 0.9) 0%, transparent 100%);
    backdrop-filter: blur(10px);
}

.hud-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
}

.level-info,
.score-info,
.combo-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.25rem;
}

.level-label,
.score-label,
.combo-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-transform: uppercase;
    letter-spacing: 0.1em;
}

.level-number,
.score-value,
.combo-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    text-shadow: var(--glow-primary);
}

.combo-value {
    color: var(--accent-color);
    text-shadow: var(--glow-secondary);
}

.hud-controls {
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

.control-btn {
    width: 40px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-small);
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.control-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-light);
}

.control-btn .btn-icon {
    font-size: 1.2rem;
}

/* 游戏画布容器 */
.game-canvas-container {
    position: relative;
    flex: 1;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.game-canvas,
.ui-canvas,
.audio-visualizer-canvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    cursor: crosshair;
}

.ui-canvas {
    pointer-events: none;
    z-index: 5;
}

.audio-visualizer-canvas {
    z-index: 0;
    opacity: 0.6;
    pointer-events: none;
    mix-blend-mode: screen;
}

/* 频率控制面板 */
.frequency-panel {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 10;
    padding: 1rem;
    background: linear-gradient(0deg, rgba(13, 20, 33, 0.95) 0%, transparent 100%);
    backdrop-filter: blur(15px);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.frequency-controls {
    display: flex;
    align-items: center;
    gap: 2rem;
    margin-bottom: 1rem;
}

.frequency-slider-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.frequency-input-group {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.frequency-fine-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.freq-btn {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 32px;
    height: 28px;
}

.freq-btn:hover {
    background: var(--primary-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.freq-btn:active {
    transform: translateY(0);
}

.frequency-input {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-primary);
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
    width: 80px;
    text-align: center;
}

.frequency-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(0, 255, 136, 0.2);
}

.frequency-presets {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

.preset-btn {
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 0.25rem 0.75rem;
    border-radius: 12px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all var(--transition-fast);
}

.preset-btn:hover {
    background: var(--primary-color);
    color: var(--text-primary);
    border-color: var(--primary-color);
}

.preset-btn.active {
    background: var(--secondary-color);
    color: var(--text-primary);
    border-color: var(--secondary-color);
}

.frequency-label {
    font-size: 0.9rem;
    color: var(--text-secondary);
    min-width: 60px;
}

.frequency-slider {
    flex: 1;
    height: 6px;
    background: var(--bg-tertiary);
    border-radius: 3px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
}

.frequency-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--glow-primary);
    transition: all var(--transition-fast);
}

.frequency-slider::-webkit-slider-thumb:hover {
    transform: scale(1.1);
    box-shadow: var(--glow-primary), var(--shadow-medium);
}

.frequency-slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: var(--glow-primary);
    transition: all var(--transition-fast);
}

.frequency-value {
    font-size: 0.9rem;
    color: var(--text-primary);
    font-weight: 600;
    min-width: 80px;
    text-align: right;
    font-family: var(--font-mono);
}

.resonance-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.5rem;
    min-width: 120px;
}

.resonance-meter {
    width: 100px;
    height: 8px;
    background: var(--bg-tertiary);
    border-radius: 4px;
    overflow: hidden;
    position: relative;
}

.resonance-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--error-color));
    width: 0%;
    transition: width var(--transition-fast);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 184, 148, 0.5);
}

.resonance-label {
    font-size: 0.8rem;
    color: var(--text-secondary);
    text-align: center;
}

/* 音频可视化器 */
.audio-visualizer {
    height: 60px;
    border-radius: var(--border-radius-small);
    overflow: hidden;
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.visualizer-canvas {
    width: 100%;
    height: 100%;
    display: block;
}

/* 暂停屏幕样式 */
.pause-container {
    text-align: center;
    background: rgba(26, 26, 46, 0.95);
    padding: 3rem;
    border-radius: var(--border-radius-large);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-heavy);
    max-width: 400px;
    width: 90%;
}

.pause-title {
    font-size: 2rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 2rem;
    text-shadow: var(--glow-primary);
}

.pause-menu {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.pause-btn {
    padding: 1rem 2rem;
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius);
    color: var(--text-primary);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-medium);
    backdrop-filter: blur(10px);
}

.pause-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: var(--primary-color);
    box-shadow: var(--shadow-light);
    transform: translateY(-2px);
}

.pause-btn.primary {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
    border-color: var(--primary-color);
    box-shadow: var(--glow-primary);
}

.pause-btn.primary:hover {
    background: linear-gradient(45deg, var(--primary-light), var(--secondary-color));
    box-shadow: var(--glow-primary), var(--shadow-medium);
}

/* 设置屏幕样式 */
.settings-container {
    background: rgba(26, 26, 46, 0.95);
    padding: 2rem;
    border-radius: var(--border-radius-large);
    border: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    box-shadow: var(--shadow-heavy);
    max-width: 600px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.settings-title {
    font-size: 1.8rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 2rem;
    text-align: center;
    text-shadow: var(--glow-primary);
}

.settings-content {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.settings-section {
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    padding-bottom: 1.5rem;
}

.settings-section:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

.section-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.setting-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 1rem;
    margin-bottom: 1rem;
}

.setting-item:last-child {
    margin-bottom: 0;
}

.setting-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    min-width: 120px;
}

.setting-slider {
    flex: 1;
    height: 4px;
    background: var(--bg-tertiary);
    border-radius: 2px;
    outline: none;
    cursor: pointer;
    -webkit-appearance: none;
    appearance: none;
    max-width: 200px;
}

.setting-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    box-shadow: var(--glow-primary);
}

.setting-slider::-moz-range-thumb {
    width: 16px;
    height: 16px;
    background: var(--primary-color);
    border-radius: 50%;
    cursor: pointer;
    border: none;
    box-shadow: var(--glow-primary);
}

.setting-value {
    font-size: 0.8rem;
    color: var(--text-primary);
    font-weight: 600;
    min-width: 60px;
    text-align: right;
    font-family: var(--font-mono);
}

.setting-select {
    background: var(--bg-tertiary);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: var(--border-radius-small);
    color: var(--text-primary);
    padding: 0.5rem;
    font-size: 0.9rem;
    cursor: pointer;
    min-width: 100px;
}

.setting-checkbox {
    width: 20px;
    height: 20px;
    accent-color: var(--primary-color);
    cursor: pointer;
}

.settings-footer {
    display: flex;
    justify-content: space-between;
    gap: 1rem;
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.settings-btn {
    flex: 1;
    padding: 0.8rem 1.5rem;
    border-radius: var(--border-radius);
    font-size: 0.9rem;
    font-weight: 500;
    cursor: pointer;
    transition: all var(--transition-medium);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.settings-btn.primary {
    background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
    color: var(--text-primary);
    border-color: var(--primary-color);
    box-shadow: var(--glow-primary);
}

.settings-btn.secondary {
    background: rgba(255, 255, 255, 0.1);
    color: var(--text-secondary);
    border-color: rgba(255, 255, 255, 0.2);
}

.settings-btn:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.settings-btn.primary:hover {
    background: linear-gradient(45deg, var(--primary-light), var(--secondary-color));
    box-shadow: var(--glow-primary), var(--shadow-medium);
}

.settings-btn.secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    color: var(--text-primary);
    border-color: var(--secondary-color);
}
