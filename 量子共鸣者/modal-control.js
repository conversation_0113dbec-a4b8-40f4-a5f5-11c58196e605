/**
 * 模态框控制脚本
 * 提供基本的模态框显示和隐藏功能
 */

(function() {
    'use strict';

    console.log('📱 模态框控制脚本加载...');

    // 等待DOM加载完成
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initModalControl);
    } else {
        initModalControl();
    }

    function initModalControl() {
        console.log('📱 初始化模态框控制...');

        // 确保模态框初始状态正确
        const modals = document.querySelectorAll('.modal');
        modals.forEach(modal => {
            // 确保模态框默认隐藏
            if (!modal.classList.contains('active')) {
                modal.style.display = '';
                modal.style.opacity = '';
                modal.style.visibility = '';
            }

            // 添加点击背景关闭功能
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    hideModal(modal.id);
                }
            });

            // 添加关闭按钮事件
            const closeButton = modal.querySelector('.modal-close');
            if (closeButton) {
                closeButton.addEventListener('click', function() {
                    hideModal(modal.id);
                });
            }
        });

        console.log('✅ 模态框控制初始化完成');
    }

    // 显示模态框
    function showModal(modalId, options = {}) {
        const modal = document.getElementById(modalId);
        if (!modal) {
            console.error(`❌ 模态框不存在: ${modalId}`);
            return;
        }

        console.log(`📱 显示模态框: ${modalId}`);

        // 设置内容
        if (options.title) {
            const titleElement = modal.querySelector('.modal-title');
            if (titleElement) {
                titleElement.textContent = options.title;
            }
        }

        if (options.content) {
            const bodyElement = modal.querySelector('.modal-body');
            if (bodyElement) {
                bodyElement.innerHTML = options.content;
            }
        }

        if (options.footer) {
            const footerElement = modal.querySelector('.modal-footer');
            if (footerElement) {
                footerElement.innerHTML = options.footer;
            }
        }

        // 显示模态框
        modal.classList.add('active');
        
        // 阻止页面滚动
        document.body.style.overflow = 'hidden';

        // 触发显示事件
        modal.dispatchEvent(new CustomEvent('modalShown', {
            detail: { modalId, options }
        }));
    }

    // 隐藏模态框
    function hideModal(modalId) {
        const modal = modalId ? document.getElementById(modalId) : document.querySelector('.modal.active');
        if (!modal) {
            console.warn('⚠️ 没有找到要隐藏的模态框');
            return;
        }

        console.log(`📱 隐藏模态框: ${modal.id || '未知'}`);

        // 隐藏模态框
        modal.classList.remove('active');

        // 恢复页面滚动
        const activeModals = document.querySelectorAll('.modal.active');
        if (activeModals.length === 0) {
            document.body.style.overflow = '';
        }

        // 触发隐藏事件
        modal.dispatchEvent(new CustomEvent('modalHidden', {
            detail: { modalId: modal.id }
        }));
    }

    // 隐藏所有模态框
    function hideAllModals() {
        const activeModals = document.querySelectorAll('.modal.active');
        activeModals.forEach(modal => {
            hideModal(modal.id);
        });
    }

    // 提供全局函数
    window.showModal = showModal;
    window.hideModal = hideModal;
    window.closeModal = hideModal; // 别名
    window.hideAllModals = hideAllModals;

    // 键盘事件处理
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape') {
            const activeModal = document.querySelector('.modal.active');
            if (activeModal) {
                hideModal(activeModal.id);
            }
        }
    });

    console.log('📱 模态框控制脚本加载完成');
    console.log('💡 可以使用 showModal(id, options) 显示模态框');
    console.log('💡 可以使用 hideModal(id) 或 closeModal(id) 隐藏模态框');
})();
