/**
 * 量子共鸣者 - 综合修复测试脚本
 * 验证所有修复的功能是否正常工作
 */

class ComprehensiveFixTest {
    constructor() {
        this.testResults = [];
        this.passedTests = 0;
        this.failedTests = 0;
        this.totalTests = 0;
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 开始综合修复测试...');
        console.log('='.repeat(60));

        // 等待页面完全加载
        await this.waitForPageLoad();

        // 测试1: HUD显示修复
        await this.testHUDDisplayFix();

        // 测试2: 频率滑块精度控制
        await this.testFrequencySliderPrecision();

        // 测试3: 控制台错误修复
        await this.testConsoleErrorFix();

        // 测试4: 游戏数据更新机制
        await this.testGameDataUpdates();

        // 测试5: 综合游戏功能
        await this.testComprehensiveGameplay();

        // 输出测试结果
        this.outputTestResults();
    }

    /**
     * 等待页面加载完成
     */
    async waitForPageLoad() {
        return new Promise(resolve => {
            if (document.readyState === 'complete') {
                setTimeout(resolve, 1000); // 额外等待1秒确保所有脚本加载
            } else {
                window.addEventListener('load', () => {
                    setTimeout(resolve, 1000);
                });
            }
        });
    }

    /**
     * 测试HUD显示修复
     */
    async testHUDDisplayFix() {
        console.log('📊 测试1: HUD显示修复');
        
        try {
            // 检查关键DOM元素
            const elements = [
                'current-score',
                'current-combo', 
                'current-level',
                'frequency-value',
                'resonance-fill'
            ];

            let elementsFound = 0;
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    elementsFound++;
                    console.log(`  ✅ 找到元素: #${id}`);
                } else {
                    console.log(`  ❌ 缺失元素: #${id}`);
                }
            });

            // 测试分数显示更新
            if (window.gameController && typeof gameController.updateScoreDisplay === 'function') {
                gameController.updateScoreDisplay(12345, 100);
                const scoreElement = document.getElementById('current-score');
                if (scoreElement && scoreElement.textContent.includes('12,345')) {
                    console.log('  ✅ 分数显示更新正常');
                    this.addTestResult('分数显示更新', true);
                } else {
                    console.log('  ❌ 分数显示更新失败');
                    this.addTestResult('分数显示更新', false);
                }
            }

            // 测试连击显示更新
            if (window.gameController && typeof gameController.updateComboDisplay === 'function') {
                gameController.updateComboDisplay(15);
                const comboElement = document.getElementById('current-combo');
                if (comboElement && comboElement.textContent === '15') {
                    console.log('  ✅ 连击显示更新正常');
                    this.addTestResult('连击显示更新', true);
                } else {
                    console.log('  ❌ 连击显示更新失败');
                    this.addTestResult('连击显示更新', false);
                }
            }

            this.addTestResult('HUD元素检查', elementsFound === elements.length);

        } catch (error) {
            console.error('  ❌ HUD显示测试失败:', error);
            this.addTestResult('HUD显示修复', false);
        }
    }

    /**
     * 测试频率滑块精度控制
     */
    async testFrequencySliderPrecision() {
        console.log('🎚️ 测试2: 频率滑块精度控制');
        
        try {
            // 检查频率控制元素
            const frequencySlider = document.getElementById('frequency-slider');
            const frequencyInput = document.getElementById('frequency-input');
            const frequencyValue = document.getElementById('frequency-value');

            if (frequencySlider) {
                // 检查步长精度
                const step = parseFloat(frequencySlider.step);
                if (step === 0.1) {
                    console.log('  ✅ 频率滑块步长精度正确 (0.1)');
                    this.addTestResult('频率滑块精度', true);
                } else {
                    console.log(`  ❌ 频率滑块步长精度错误: ${step}`);
                    this.addTestResult('频率滑块精度', false);
                }

                // 测试频率设置
                frequencySlider.value = 440.5;
                frequencySlider.dispatchEvent(new Event('input'));
                
                setTimeout(() => {
                    if (frequencyValue && frequencyValue.textContent.includes('440.5')) {
                        console.log('  ✅ 频率精确设置正常');
                        this.addTestResult('频率精确设置', true);
                    } else {
                        console.log('  ❌ 频率精确设置失败');
                        this.addTestResult('频率精确设置', false);
                    }
                }, 100);
            }

            // 检查微调按钮
            const fineTuneButtons = [
                'freq-down-10',
                'freq-down-1', 
                'freq-up-1',
                'freq-up-10'
            ];

            let buttonsFound = 0;
            fineTuneButtons.forEach(id => {
                if (document.getElementById(id)) {
                    buttonsFound++;
                }
            });

            this.addTestResult('频率微调按钮', buttonsFound === fineTuneButtons.length);

            // 检查预设按钮
            const presetButtons = document.querySelectorAll('.preset-btn');
            this.addTestResult('频率预设按钮', presetButtons.length > 0);

        } catch (error) {
            console.error('  ❌ 频率滑块测试失败:', error);
            this.addTestResult('频率滑块精度控制', false);
        }
    }

    /**
     * 测试控制台错误修复
     */
    async testConsoleErrorFix() {
        console.log('🔧 测试3: 控制台错误修复');
        
        try {
            // 检查关键全局对象
            const globalObjects = [
                'MathUtils',
                'QuantumEngine',
                'GameController',
                'quantumEngine',
                'gameController'
            ];

            let objectsFound = 0;
            globalObjects.forEach(objName => {
                if (typeof window[objName] !== 'undefined') {
                    objectsFound++;
                    console.log(`  ✅ 全局对象存在: ${objName}`);
                } else {
                    console.log(`  ❌ 全局对象缺失: ${objName}`);
                }
            });

            this.addTestResult('全局对象检查', objectsFound === globalObjects.length);

            // 测试量子引擎方法
            if (window.quantumEngine) {
                const hasResonanceMethod = typeof quantumEngine.getCurrentResonanceStrength === 'function';
                this.addTestResult('量子引擎方法', hasResonanceMethod);
                
                if (hasResonanceMethod) {
                    const resonance = quantumEngine.getCurrentResonanceStrength();
                    console.log(`  ✅ 共鸣强度获取正常: ${resonance}`);
                }
            }

        } catch (error) {
            console.error('  ❌ 控制台错误测试失败:', error);
            this.addTestResult('控制台错误修复', false);
        }
    }

    /**
     * 测试游戏数据更新机制
     */
    async testGameDataUpdates() {
        console.log('⚙️ 测试4: 游戏数据更新机制');
        
        try {
            if (window.gameController) {
                // 测试时间更新
                gameController.gameTime = 125.5; // 2分5.5秒
                if (typeof gameController.updateTimeDisplay === 'function') {
                    gameController.updateTimeDisplay();
                    console.log('  ✅ 时间更新方法存在');
                    this.addTestResult('时间更新方法', true);
                } else {
                    console.log('  ❌ 时间更新方法缺失');
                    this.addTestResult('时间更新方法', false);
                }

                // 测试HUD更新
                if (typeof gameController.updateHUDDisplay === 'function') {
                    gameController.updateHUDDisplay();
                    console.log('  ✅ HUD更新方法存在');
                    this.addTestResult('HUD更新方法', true);
                } else {
                    console.log('  ❌ HUD更新方法缺失');
                    this.addTestResult('HUD更新方法', false);
                }

                // 测试共鸣强度显示
                if (typeof gameController.updateResonanceDisplay === 'function') {
                    gameController.updateResonanceDisplay();
                    console.log('  ✅ 共鸣强度更新方法存在');
                    this.addTestResult('共鸣强度更新', true);
                } else {
                    console.log('  ❌ 共鸣强度更新方法缺失');
                    this.addTestResult('共鸣强度更新', false);
                }
            }

        } catch (error) {
            console.error('  ❌ 游戏数据更新测试失败:', error);
            this.addTestResult('游戏数据更新机制', false);
        }
    }

    /**
     * 测试综合游戏功能
     */
    async testComprehensiveGameplay() {
        console.log('🎮 测试5: 综合游戏功能');
        
        try {
            // 检查游戏初始化
            if (window.gameController && gameController.isInitialized) {
                console.log('  ✅ 游戏控制器已初始化');
                this.addTestResult('游戏初始化', true);
            } else {
                console.log('  ❌ 游戏控制器未初始化');
                this.addTestResult('游戏初始化', false);
            }

            // 检查量子引擎回调
            if (window.quantumEngine) {
                const hasCallbacks = quantumEngine.onScoreUpdate && quantumEngine.onComboUpdate;
                this.addTestResult('量子引擎回调', !!hasCallbacks);
                
                if (hasCallbacks) {
                    console.log('  ✅ 量子引擎回调已设置');
                } else {
                    console.log('  ❌ 量子引擎回调未设置');
                }
            }

            // 检查输入管理器
            if (window.gameController && gameController.inputManager) {
                console.log('  ✅ 输入管理器已创建');
                this.addTestResult('输入管理器', true);
            } else {
                console.log('  ❌ 输入管理器未创建');
                this.addTestResult('输入管理器', false);
            }

        } catch (error) {
            console.error('  ❌ 综合游戏功能测试失败:', error);
            this.addTestResult('综合游戏功能', false);
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(testName, passed) {
        this.testResults.push({ name: testName, passed });
        this.totalTests++;
        if (passed) {
            this.passedTests++;
        } else {
            this.failedTests++;
        }
    }

    /**
     * 输出测试结果
     */
    outputTestResults() {
        console.log('\n📋 综合修复测试结果:');
        console.log('='.repeat(60));
        
        this.testResults.forEach(result => {
            const status = result.passed ? '✅ 通过' : '❌ 失败';
            console.log(`${status} ${result.name}`);
        });
        
        console.log('='.repeat(60));
        console.log(`📊 测试统计:`);
        console.log(`   总测试数: ${this.totalTests}`);
        console.log(`   通过: ${this.passedTests}`);
        console.log(`   失败: ${this.failedTests}`);
        console.log(`   成功率: ${((this.passedTests / this.totalTests) * 100).toFixed(1)}%`);
        
        if (this.failedTests === 0) {
            console.log('🎉 所有测试通过！修复成功！');
        } else {
            console.log('⚠️ 部分测试失败，需要进一步检查');
        }
        
        console.log('='.repeat(60));
    }
}

// 创建测试实例
window.comprehensiveFixTest = new ComprehensiveFixTest();

// 页面加载完成后自动运行测试
document.addEventListener('DOMContentLoaded', () => {
    setTimeout(() => {
        if (window.comprehensiveFixTest) {
            comprehensiveFixTest.runAllTests();
        }
    }, 3000); // 等待3秒让所有系统初始化完成
});

console.log('🧪 综合修复测试脚本已加载');
