# 量子共鸣者 - 教学提示系统

## 概述

教学提示系统是为量子共鸣者游戏设计的新手引导功能，旨在帮助新玩家快速理解游戏机制和操作方法。系统提供了直观的步骤引导、UI高亮、交互检测等功能。

## 功能特性

### 🎯 核心功能
- **步骤式引导**: 将复杂的游戏机制分解为简单的步骤
- **智能高亮**: 自动高亮需要操作的UI元素
- **交互检测**: 实时检测用户操作，自动推进教程进度
- **视觉指示**: 提供箭头指示和动画效果
- **进度显示**: 实时显示教程进度和剩余步骤

### 🎨 用户体验
- **现代化UI**: 采用量子主题的视觉设计
- **响应式布局**: 支持不同屏幕尺寸
- **流畅动画**: 平滑的过渡和反馈效果
- **键盘支持**: 支持键盘快捷键操作
- **跳过选项**: 允许用户随时跳过教程

### ⚙️ 配置灵活性
- **可配置步骤**: 支持自定义教程内容和流程
- **条件触发**: 支持基于用户行为的条件触发
- **多语言支持**: 预留国际化接口
- **主题适配**: 支持不同的视觉主题

## 系统架构

### 文件结构
```
量子共鸣者/
├── js/ui/tutorial-system.js     # 教学提示系统核心类
├── styles/tutorial.css          # 教学提示样式文件
├── tutorial-test.html           # 教学提示测试页面
└── docs/tutorial-system.md      # 系统说明文档
```

### 核心类: TutorialSystem

#### 主要属性
- `isInitialized`: 系统初始化状态
- `isActive`: 教程运行状态
- `currentStep`: 当前步骤索引
- `tutorialSteps`: 教程步骤数据
- `elements`: UI元素引用
- `config`: 配置选项
- `callbacks`: 事件回调函数

#### 主要方法
- `init()`: 初始化系统
- `startTutorial(tutorialId)`: 开始指定教程
- `showStep(stepIndex)`: 显示指定步骤
- `nextStep()`: 进入下一步
- `prevStep()`: 返回上一步
- `skipTutorial()`: 跳过教程
- `completeTutorial()`: 完成教程

## 教程配置

### 教程数据结构
```javascript
{
    id: 'tutorial_id',
    name: '教程名称',
    description: '教程描述',
    steps: [
        {
            title: '步骤标题',
            content: '步骤内容说明',
            target: '#target-element',  // 目标元素选择器
            position: 'bottom',         // 提示框位置
            action: 'highlight',        // 执行动作
            waitFor: 'user_action'      // 等待条件
        }
        // ... 更多步骤
    ]
}
```

### 步骤配置选项

#### position (提示框位置)
- `'top'`: 在目标元素上方
- `'bottom'`: 在目标元素下方
- `'left'`: 在目标元素左侧
- `'right'`: 在目标元素右侧
- `'center'`: 屏幕中央

#### action (执行动作)
- `'none'`: 无特殊动作
- `'highlight'`: 高亮目标元素
- `'wait_for_click'`: 等待用户点击

#### waitFor (等待条件)
- `'particle_activated'`: 等待粒子激活
- `'user_click'`: 等待用户点击
- `'timer'`: 等待指定时间

## 集成方式

### 1. 关卡配置
在关卡配置中添加教程设置：
```javascript
{
    // ... 其他关卡配置
    tutorial: {
        enabled: true,           // 启用教程
        tutorialId: 'level1',    // 教程ID
        autoStart: true,         // 自动开始
        showOnFirstPlay: true    // 首次游玩时显示
    }
}
```

### 2. 游戏控制器集成
游戏控制器会自动检查关卡的教程配置，并在适当时机启动教程：
```javascript
// 在 startLevel 方法中
this.checkAndStartTutorial(levelId);
```

### 3. 事件监听
系统监听游戏事件来自动推进教程：
```javascript
// 监听粒子激活事件
document.addEventListener('particleActivated', onParticleActivated);
```

## 使用方法

### 基本使用
```javascript
// 初始化系统
window.tutorialSystem.init();

// 开始教程
window.tutorialSystem.startTutorial('level1');

// 设置回调
window.tutorialSystem.on('onComplete', (tutorial) => {
    console.log('教程完成:', tutorial.name);
});
```

### 自定义教程
```javascript
// 注册新教程
window.tutorialSystem.registerTutorial('custom_tutorial', {
    name: '自定义教程',
    description: '这是一个自定义教程',
    steps: [
        {
            title: '欢迎',
            content: '欢迎使用自定义教程！',
            target: null,
            position: 'center',
            action: 'none'
        }
        // ... 更多步骤
    ]
});
```

## 测试和调试

### 测试页面
访问 `tutorial-test.html` 可以测试教学提示系统的功能：
- 模拟游戏界面
- 测试教程流程
- 查看系统日志
- 验证交互逻辑

### 调试信息
系统会在控制台输出详细的调试信息：
```
📚 教学提示系统已创建
✅ 教学提示系统初始化完成
📚 已注册教程: 量子共鸣入门
📚 开始教程: 量子共鸣入门
📚 显示教程步骤 1/7: 欢迎来到量子共鸣者！
```

## 样式定制

### CSS变量
系统使用CSS变量来支持主题定制：
```css
:root {
    --tutorial-primary-color: #00d4ff;
    --tutorial-background: rgba(26, 26, 46, 0.9);
    --tutorial-border: rgba(0, 212, 255, 0.3);
    --tutorial-shadow: rgba(0, 0, 0, 0.5);
}
```

### 响应式设计
系统支持移动设备和不同屏幕尺寸：
```css
@media (max-width: 768px) {
    .tutorial-tooltip {
        max-width: 90vw;
        min-width: 280px;
    }
}
```

## 性能优化

### 延迟加载
教程系统采用延迟初始化，只在需要时加载：
```javascript
// 延迟启动教程，确保游戏界面已完全加载
setTimeout(() => {
    window.tutorialSystem.startTutorial(tutorialId);
}, 1000);
```

### 事件清理
系统会自动清理事件监听器，避免内存泄漏：
```javascript
// 移除事件监听器
document.removeEventListener('particleActivated', onParticleActivated);
```

## 扩展开发

### 添加新的等待条件
```javascript
waitForUserAction(actionType) {
    switch (actionType) {
        case 'particle_activated':
            this.waitForParticleActivation();
            break;
        case 'custom_action':
            this.waitForCustomAction();
            break;
        // ... 添加新的等待条件
    }
}
```

### 自定义动画效果
```css
@keyframes customAnimation {
    from { opacity: 0; transform: scale(0.8); }
    to { opacity: 1; transform: scale(1); }
}

.tutorial-custom {
    animation: customAnimation 0.3s ease-out;
}
```

## 最佳实践

1. **简洁明了**: 每个步骤的说明应该简洁明了，避免信息过载
2. **循序渐进**: 按照游戏的自然流程设计教程步骤
3. **及时反馈**: 在用户完成操作后及时给予反馈
4. **可跳过性**: 始终提供跳过选项，尊重用户选择
5. **测试验证**: 在不同设备和浏览器上测试教程效果

## 故障排除

### 常见问题
1. **教程不显示**: 检查tutorialSystem是否正确初始化
2. **高亮不准确**: 确认目标元素选择器是否正确
3. **步骤不推进**: 检查事件监听器是否正常工作
4. **样式异常**: 确认CSS文件是否正确加载

### 调试方法
```javascript
// 检查系统状态
console.log('Tutorial System:', window.tutorialSystem);
console.log('Is Initialized:', window.tutorialSystem.isInitialized);
console.log('Is Running:', window.tutorialSystem.isRunning());
```

---

*本文档最后更新时间: 2025-08-02*
