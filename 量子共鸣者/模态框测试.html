<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>模态框修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #1a1a2e;
            color: #fff;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #2d5a27;
            border: 1px solid #4caf50;
        }
        .warning {
            background: #5a4a27;
            border: 1px solid #ff9800;
        }
        .error {
            background: #5a2727;
            border: 1px solid #f44336;
        }
        .info {
            background: #27405a;
            border: 1px solid #2196f3;
        }
        button {
            background: #4caf50;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
        }
        button:hover {
            background: #45a049;
        }
        .modal-info {
            background: #333;
            padding: 10px;
            margin: 5px 0;
            border-radius: 3px;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
    <!-- 引入模态框样式 -->
    <link rel="stylesheet" href="styles/ui-components.css">
</head>
<body>
    <div class="test-container">
        <h1>📱 模态框修复测试</h1>
        
        <div class="test-result info">
            <h3>📋 测试说明</h3>
            <p>此页面用于测试模态框的显示状态修复是否成功。</p>
            <p>点击下方按钮进行各项测试。</p>
        </div>

        <div style="margin: 20px 0;">
            <button onclick="testModalStates()">🔍 检查模态框状态</button>
            <button onclick="showTestModal()">📱 显示测试模态框</button>
            <button onclick="hideTestModal()">❌ 隐藏测试模态框</button>
            <button onclick="simulateModalIssue()">⚠️ 模拟模态框异常</button>
            <button onclick="fixModalIssue()">🔧 修复模态框异常</button>
            <button onclick="clearResults()">🧹 清除结果</button>
        </div>

        <div id="results"></div>
    </div>

    <!-- 测试模态框 -->
    <div id="test-modal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">测试模态框</h3>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>这是一个测试模态框，用于验证模态框功能是否正常工作。</p>
                <p>模态框应该：</p>
                <ul>
                    <li>默认隐藏（opacity: 0, visibility: hidden）</li>
                    <li>有 active 类时显示（opacity: 1, visibility: visible）</li>
                    <li>点击背景或关闭按钮时隐藏</li>
                    <li>按 ESC 键时隐藏</li>
                </ul>
            </div>
            <div class="modal-footer">
                <button class="btn" onclick="hideModal('test-modal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 引入修复脚本 -->
    <script src="screen-state-fix.js"></script>
    <script src="screen-state-debug.js"></script>
    <script src="modal-control.js"></script>

    <script>
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
        }

        function testModalStates() {
            addResult('<h3>🔍 开始检查模态框状态...</h3>', 'info');
            
            const modals = document.querySelectorAll('.modal');
            let passCount = 0;
            let totalCount = modals.length;

            modals.forEach((modal, index) => {
                const modalId = modal.id || `modal-${index}`;
                const computedStyle = window.getComputedStyle(modal);
                const hasActiveClass = modal.classList.contains('active');
                
                addResult(`<div class="modal-info">
                    模态框ID: ${modalId}<br>
                    CSS类: ${modal.className}<br>
                    是否有active类: ${hasActiveClass}<br>
                    内联display: "${modal.style.display}"<br>
                    内联opacity: "${modal.style.opacity}"<br>
                    内联visibility: "${modal.style.visibility}"<br>
                    计算display: ${computedStyle.display}<br>
                    计算opacity: ${computedStyle.opacity}<br>
                    计算visibility: ${computedStyle.visibility}
                </div>`, 'info');

                // 检查状态是否正确
                const shouldBeVisible = hasActiveClass;
                const isActuallyVisible = (
                    computedStyle.opacity !== '0' && 
                    computedStyle.visibility !== 'hidden'
                );

                if (shouldBeVisible === isActuallyVisible) {
                    addResult(`✅ ${modalId}: 状态正确 (${shouldBeVisible ? '显示' : '隐藏'})`, 'success');
                    passCount++;
                } else {
                    addResult(`❌ ${modalId}: 状态异常 (期望: ${shouldBeVisible ? '显示' : '隐藏'}, 实际: ${isActuallyVisible ? '显示' : '隐藏'})`, 'error');
                }
            });

            const resultType = passCount === totalCount ? 'success' : 'warning';
            addResult(`<h4>📊 测试结果: ${passCount}/${totalCount} 通过</h4>`, resultType);
        }

        function showTestModal() {
            addResult('📱 显示测试模态框...', 'info');
            showModal('test-modal', {
                title: '测试模态框',
                content: '<p>模态框显示成功！</p><p>现在应该可以看到模态框，并且背景有遮罩效果。</p>'
            });
            
            setTimeout(() => {
                testModalStates();
            }, 100);
        }

        function hideTestModal() {
            addResult('❌ 隐藏测试模态框...', 'info');
            hideModal('test-modal');
            
            setTimeout(() => {
                testModalStates();
            }, 100);
        }

        function simulateModalIssue() {
            addResult('⚠️ 模拟模态框异常状态...', 'warning');
            
            const modal = document.getElementById('test-modal');
            if (modal) {
                // 模拟异常：设置内联样式但没有active类
                modal.style.display = 'flex';
                modal.style.opacity = '1';
                modal.style.visibility = 'visible';
                modal.classList.remove('active');
                
                addResult('⚠️ 已模拟异常：模态框有显示样式但没有active类', 'warning');
                
                setTimeout(() => {
                    testModalStates();
                }, 100);
            }
        }

        function fixModalIssue() {
            addResult('🔧 修复模态框异常...', 'info');
            
            // 调用全局修复函数
            if (typeof fixAllScreenStates === 'function') {
                fixAllScreenStates();
                addResult('✅ 已调用修复函数', 'success');
            } else {
                addResult('❌ 修复函数不可用', 'error');
            }
            
            setTimeout(() => {
                testModalStates();
            }, 100);
        }

        // 页面加载时自动检查
        window.addEventListener('load', () => {
            setTimeout(() => {
                addResult('<h3>🚀 页面加载完成，自动检查模态框状态...</h3>', 'info');
                testModalStates();
            }, 500);
        });
    </script>
</body>
</html>
