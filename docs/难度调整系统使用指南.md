# 游戏难度调整系统使用指南

## 🚀 快速开始

### 1. 基础集成

在游戏主页面中引入难度配置系统：

```html
<!-- 在 index.html 中添加 -->
<script src="js/config/difficulty-config.js"></script>
```

### 2. 初始化系统

```javascript
// 在游戏初始化时调用
async function initializeGame() {
    // 初始化难度配置管理器
    await difficultyConfigManager.init();
    
    // 设置默认难度（可选）
    await difficultyConfigManager.setDifficulty('normal');
    
    console.log('✅ 难度系统初始化完成');
}
```

### 3. 在游戏中应用配置

```javascript
// 在游戏引擎中获取并应用难度配置
function applyDifficultyToGame() {
    const config = difficultyConfigManager.getGameDifficultyConfig('spark-catcher');
    
    // 应用配置到游戏参数
    this.lives = config.gameplay.player.lives;
    this.sparkSpawnRate = config.gameplay.sparks.spawnRate;
    this.perfectWindow = config.gameplay.timing.perfectWindow;
    
    console.log(`🎯 应用难度配置: ${config.name}`);
}
```

## 🎮 游戏特定配置

### 瞬光捕手配置示例

```javascript
// 获取瞬光捕手的困难难度配置
const sparkConfig = difficultyConfigManager.getGameDifficultyConfig('spark-catcher', 'hard');

// 应用到游戏引擎
gameEngine.levelConfig = {
    sparkSpawnRate: sparkConfig.gameplay.sparks.spawnRate,    // 1040ms
    sparkSpeed: sparkConfig.gameplay.sparks.speed,           // 1.54倍
    sparkCount: sparkConfig.gameplay.sparks.maxCount,        // 4个
    perfectWindow: sparkConfig.gameplay.timing.perfectWindow, // 61ms
    goodWindow: sparkConfig.gameplay.timing.goodWindow       // 127ms
};

// 应用玩家属性
gameEngine.lives = sparkConfig.gameplay.player.lives;        // 2条生命
gameEngine.scoreMultiplier = sparkConfig.gameplay.player.scoreMultiplier; // 1.3倍
```

### 量子共鸣者配置示例

```javascript
// 获取量子共鸣者的专家难度配置
const quantumConfig = difficultyConfigManager.getGameDifficultyConfig('quantum-resonator', 'expert');

// 应用到量子引擎
quantumEngine.fieldStrength = quantumConfig.gameplay.quantum.fieldStrength;           // 1.44
quantumEngine.resonanceThreshold = quantumConfig.gameplay.quantum.resonanceThreshold; // 0.16
quantumEngine.chainDecayRate = quantumConfig.gameplay.quantum.chainDecayRate;         // 0.904
quantumEngine.waveSpeed = quantumConfig.gameplay.quantum.waveSpeed;                   // 310
quantumEngine.maxChainLength = quantumConfig.gameplay.quantum.maxChainLength;         // 14

// 应用关卡设置
levelManager.timeLimit = quantumConfig.level.timeLimit;     // 84秒
levelManager.targetScore = quantumConfig.level.targetScore; // 3200分
```

### 时空织梦者配置示例

```javascript
// 获取时空织梦者的大师难度配置
const timeConfig = difficultyConfigManager.getGameDifficultyConfig('temporal-weaver', 'master');

// 应用到时间引擎
timeEngine.maxTimelineLength = timeConfig.gameplay.timeControl.maxRewindSteps;  // 50步
timeEngine.rewindSpeed = timeConfig.gameplay.timeControl.rewindSpeed;          // -1.5倍
timeEngine.fastForwardSpeed = timeConfig.gameplay.timeControl.fastForwardSpeed; // 4.0倍

// 应用梦境编织参数
dreamWeaver.maxConnections = timeConfig.gameplay.dreamWeaving.maxConnections;   // 35个
dreamWeaver.weavingSpeed = timeConfig.gameplay.dreamWeaving.weavingSpeed;       // 1.6倍
dreamWeaver.complexityFactor = timeConfig.gameplay.dreamWeaving.complexityFactor; // 2.0
```

## 🔧 动态难度调整

### 启用自适应难度

```javascript
// 在瞬光捕手中启用动态难度调整
gameEngine.setAdaptiveDifficulty(true);

// 在游戏过程中记录玩家表现
function onPlayerAction(hit, accuracy) {
    // hit: 是否击中目标 (boolean)
    // accuracy: 操作准确度 (0-1)
    gameEngine.recordPlayerPerformance(hit, accuracy);
}

// 获取当前难度统计
const stats = gameEngine.getDifficultyStats();
console.log('玩家表现统计:', {
    平均准确率: (stats.averageAccuracy * 100).toFixed(1) + '%',
    连续击中: stats.consecutiveHits,
    连续失误: stats.consecutiveMisses,
    当前配置: stats.currentConfig
});
```

### 手动调整难度参数

```javascript
// 量子共鸣者手动调整示例
quantumEngine.adjustQuantumParameters({
    fieldStrength: 1.5,        // 增强量子场
    resonanceThreshold: 0.2,   // 降低共鸣阈值
    waveSpeed: 250            // 调整波速
});

// 时空织梦者手动调整示例
timeEngine.adjustTimeParameters({
    maxTimelineLength: 800,    // 增加时间线长度
    rewindSpeed: 2.5,         // 加快倒流速度
    fastForwardSpeed: 3.5     // 调整快进速度
});
```

## 🎨 用户界面集成

### 难度选择界面

```javascript
// 创建难度选择UI
function createDifficultySelector() {
    const difficulties = difficultyConfigManager.getAvailableDifficulties();
    const currentDifficulty = difficultyConfigManager.getCurrentDifficulty();
    
    const selectorHTML = difficulties.map(diff => `
        <div class="difficulty-option ${diff.key === currentDifficulty ? 'selected' : ''}" 
             data-difficulty="${diff.key}"
             style="border-color: ${diff.color}">
            <h3 style="color: ${diff.color}">${diff.name}</h3>
            <p>${diff.description}</p>
            <div class="difficulty-multiplier">难度倍数: ${diff.multiplier}x</div>
        </div>
    `).join('');
    
    document.getElementById('difficulty-selector').innerHTML = selectorHTML;
}

// 处理难度选择
document.addEventListener('click', async (e) => {
    if (e.target.closest('.difficulty-option')) {
        const difficulty = e.target.closest('.difficulty-option').dataset.difficulty;
        await difficultyConfigManager.setDifficulty(difficulty);
        
        // 更新UI显示
        createDifficultySelector();
        
        // 重新应用配置到当前游戏
        applyDifficultyToGame();
    }
});
```

### 难度信息显示

```javascript
// 显示当前难度信息
function displayDifficultyInfo(gameType) {
    const config = difficultyConfigManager.getGameDifficultyConfig(gameType);
    const difficultyScore = difficultyConfigManager.calculateDifficultyScore(gameType);
    
    const infoHTML = `
        <div class="difficulty-info">
            <h3>当前难度: ${config.name}</h3>
            <p>${config.description}</p>
            <div class="difficulty-score">
                <span>难度评分: ${difficultyScore}/100</span>
                <div class="score-bar">
                    <div class="score-fill" style="width: ${difficultyScore}%; background-color: ${config.color}"></div>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('difficulty-info').innerHTML = infoHTML;
}
```

## 📊 智能推荐系统

### 基于玩家数据的难度推荐

```javascript
// 获取玩家统计数据
async function getPlayerStats() {
    if (window.playerManager) {
        const player = playerManager.getCurrentPlayer();
        return player.stats;
    }
    return null;
}

// 获取难度推荐
async function getDifficultyRecommendation(gameType) {
    const playerStats = await getPlayerStats();
    
    if (playerStats) {
        const recommended = difficultyConfigManager.getDifficultyRecommendation(playerStats, gameType);
        
        console.log(`🎯 为 ${gameType} 推荐难度: ${recommended}`);
        
        // 显示推荐信息
        showRecommendationDialog(recommended, playerStats);
        
        return recommended;
    }
    
    return 'beginner'; // 默认推荐新手难度
}

// 显示推荐对话框
function showRecommendationDialog(recommended, stats) {
    const recommendedInfo = difficultyConfigManager.difficultyLevels[recommended];
    
    const dialogHTML = `
        <div class="recommendation-dialog">
            <h3>🎯 难度推荐</h3>
            <p>基于您的游戏表现，我们推荐您使用 <strong style="color: ${recommendedInfo.color}">${recommendedInfo.name}</strong> 难度。</p>
            <div class="player-stats">
                <p>游戏次数: ${stats.totalGames}</p>
                <p>最佳分数: ${stats.bestScore}</p>
                <p>平均分数: ${Math.round(stats.totalScore / stats.totalGames)}</p>
                <p>游戏时长: ${Math.round(stats.totalPlayTime / (1000 * 60))} 分钟</p>
            </div>
            <div class="dialog-buttons">
                <button onclick="applyRecommendedDifficulty('${recommended}')">应用推荐</button>
                <button onclick="closeRecommendationDialog()">稍后决定</button>
            </div>
        </div>
    `;
    
    document.getElementById('recommendation-dialog').innerHTML = dialogHTML;
    document.getElementById('recommendation-dialog').style.display = 'block';
}
```

## 🔄 配置管理

### 导出和导入配置

```javascript
// 导出当前难度配置
function exportCurrentConfig(gameType) {
    const config = difficultyConfigManager.exportDifficultyConfig(gameType);
    
    // 创建下载链接
    const blob = new Blob([config], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${gameType}-difficulty-config.json`;
    a.click();
    
    console.log('✅ 配置已导出');
}

// 导入配置文件
function importConfig(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        const configData = difficultyConfigManager.importDifficultyConfig(e.target.result);
        
        if (configData) {
            // 应用导入的配置
            applyImportedConfig(configData);
            console.log('✅ 配置已导入并应用');
        } else {
            console.error('❌ 配置文件格式错误');
        }
    };
    reader.readAsText(file);
}
```

### 创建自定义难度

```javascript
// 创建自定义难度配置
async function createCustomDifficulty() {
    const customConfig = {
        description: '我的个人定制难度',
        gameplay: {
            player: {
                lives: 4,
                scoreMultiplier: 1.2
            },
            sparks: {
                spawnRate: 1200,
                speed: 1.1,
                maxCount: 3
            },
            timing: {
                perfectWindow: 80,
                goodWindow: 160
            }
        },
        userExperience: {
            showHints: true,
            visualFeedback: {
                particleEffects: true,
                screenShake: true,
                slowMotion: false
            }
        }
    };
    
    const success = await difficultyConfigManager.createCustomDifficulty('我的配置', customConfig);
    
    if (success) {
        console.log('✅ 自定义难度创建成功');
    } else {
        console.error('❌ 自定义难度创建失败');
    }
}
```

## 🎯 事件监听

### 监听难度变更事件

```javascript
// 监听难度变更事件
window.addEventListener('difficultyChanged', (event) => {
    const { oldDifficulty, newDifficulty, difficultyInfo } = event.detail;
    
    console.log(`🎯 难度已变更: ${oldDifficulty} → ${newDifficulty}`);
    
    // 更新UI显示
    updateDifficultyUI(newDifficulty, difficultyInfo);
    
    // 重新应用配置到所有游戏
    applyDifficultyToAllGames();
    
    // 显示变更通知
    showDifficultyChangeNotification(difficultyInfo);
});

// 显示难度变更通知
function showDifficultyChangeNotification(difficultyInfo) {
    const notification = document.createElement('div');
    notification.className = 'difficulty-notification';
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${difficultyInfo.color};
        color: white;
        padding: 15px;
        border-radius: 8px;
        z-index: 1000;
        animation: slideIn 0.3s ease-out;
    `;
    notification.innerHTML = `
        <strong>难度已更改</strong><br>
        当前难度: ${difficultyInfo.name}
    `;
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        notification.remove();
    }, 3000);
}
```

## 🔍 调试和测试

### 调试工具

```javascript
// 调试工具函数
const DifficultyDebugger = {
    // 显示所有难度配置
    showAllConfigs(gameType) {
        const difficulties = ['beginner', 'easy', 'normal', 'hard', 'expert', 'master'];
        
        difficulties.forEach(diff => {
            const config = difficultyConfigManager.getGameDifficultyConfig(gameType, diff);
            console.log(`${diff.toUpperCase()}:`, config);
        });
    },
    
    // 测试难度评分
    testDifficultyScoring(gameType) {
        const difficulties = ['beginner', 'easy', 'normal', 'hard', 'expert', 'master'];
        
        difficulties.forEach(diff => {
            const score = difficultyConfigManager.calculateDifficultyScore(gameType, diff);
            console.log(`${diff}: ${score}/100`);
        });
    },
    
    // 模拟玩家数据测试推荐
    testRecommendation() {
        const mockPlayerStats = [
            { totalGames: 5, bestScore: 1000, averageScore: 800, totalPlayTime: 300000 },
            { totalGames: 50, bestScore: 5000, averageScore: 3500, totalPlayTime: 1800000 },
            { totalGames: 200, bestScore: 15000, averageScore: 12000, totalPlayTime: 7200000 }
        ];
        
        mockPlayerStats.forEach((stats, index) => {
            const recommendation = difficultyConfigManager.getDifficultyRecommendation(stats, 'spark-catcher');
            console.log(`玩家${index + 1} 推荐难度: ${recommendation}`, stats);
        });
    }
};

// 在控制台中使用调试工具
// DifficultyDebugger.showAllConfigs('spark-catcher');
// DifficultyDebugger.testDifficultyScoring('quantum-resonator');
// DifficultyDebugger.testRecommendation();
```

## 📝 最佳实践

### 1. 初始化顺序
```javascript
// 正确的初始化顺序
async function initializeGameSystem() {
    // 1. 初始化存储服务
    await storageService.init();
    
    // 2. 初始化难度配置管理器
    await difficultyConfigManager.init();
    
    // 3. 初始化游戏引擎
    await gameEngine.init();
    
    // 4. 应用难度配置
    gameEngine.applyDifficultyConfig();
}
```

### 2. 性能优化
```javascript
// 缓存配置以避免重复计算
let cachedConfig = null;
let cachedDifficulty = null;

function getOptimizedConfig(gameType) {
    const currentDifficulty = difficultyConfigManager.getCurrentDifficulty();
    
    if (cachedConfig && cachedDifficulty === currentDifficulty) {
        return cachedConfig;
    }
    
    cachedConfig = difficultyConfigManager.getGameDifficultyConfig(gameType);
    cachedDifficulty = currentDifficulty;
    
    return cachedConfig;
}
```

### 3. 错误处理
```javascript
// 安全的配置应用
function safeApplyDifficultyConfig(gameType) {
    try {
        const config = difficultyConfigManager.getGameDifficultyConfig(gameType);
        
        if (config && config.gameplay) {
            // 应用配置
            applyConfigToGame(config);
        } else {
            console.warn('⚠️ 配置无效，使用默认设置');
            applyDefaultConfig();
        }
    } catch (error) {
        console.error('❌ 应用难度配置失败:', error);
        applyDefaultConfig();
    }
}
```

---

通过以上指南，您可以完整地集成和使用游戏难度调整系统。系统设计为模块化和可扩展的，可以根据具体需求进行定制和扩展。
