/**
 * Split-Second Spark 版本信息模块
 * 自动生成于: 2025-08-07 14:36:37
 */

// 版本信息对象
const VERSION_INFO = {
  "version": "1.0.0",
  "build_time": "2025-08-07T14:36:37.908481",
  "build_timestamp": 1754577397,
  "build_date": "2025-08-07",
  "build_time_formatted": "2025-08-07 14:36:37",
  "git": {
    "commit_hash": "f150089450c9d2266b43bb734c23f3241f60554f",
    "commit_hash_short": "f1500894",
    "branch": "main",
    "commit_date": "2025-08-07 13:30:31 +0000",
    "commit_message": "修复 EdgeOneStorageImpl 文件加载 404 错误",
    "tag": null,
    "is_dirty": true,
    "remote_url": "*****************:aier/split-second-spark.git"
  },
  "build_environment": {
    "platform": "Linux-6.8.12-4-pve-x86_64-with-glibc2.36",
    "system": "Linux",
    "release": "6.8.12-4-pve",
    "version": "#1 SMP PREEMPT_DYNAMIC PMX 6.8.12-4 (2024-11-06T15:04Z)",
    "machine": "x86_64",
    "processor": "",
    "python_version": "3.11.2",
    "python_implementation": "CPython",
    "hostname": "dev",
    "user": "root",
    "build_tool": "Python Version Generator",
    "cwd": "/root/workspace/git.atjog.com/aier/split-second-spark"
  },
  "project": {
    "name": "Split-Second Spark",
    "description": "捕捉决定性瞬间，引燃无限可能",
    "type": "Web Game Collection",
    "games": [
      "时空织梦者",
      "瞬光捕手",
      "量子共鸣者"
    ]
  },
  "meta": {
    "generator": "VersionGenerator",
    "generator_version": "1.0.0",
    "format_version": "1.0",
    "encoding": "utf-8"
  }
};

/**
 * 获取版本信息
 * @returns {Object} 完整的版本信息对象
 */
function getVersionInfo() {
    return VERSION_INFO;
}

/**
 * 获取版本号
 * @returns {string} 版本号
 */
function getVersion() {
    return VERSION_INFO.version;
}

/**
 * 获取构建时间
 * @returns {string} 格式化的构建时间
 */
function getBuildTime() {
    return VERSION_INFO.build_time_formatted;
}

/**
 * 获取Git提交哈希
 * @returns {string|null} Git提交哈希
 */
function getCommitHash() {
    return VERSION_INFO.git.commit_hash;
}

/**
 * 获取短Git提交哈希
 * @returns {string|null} 短Git提交哈希
 */
function getCommitHashShort() {
    return VERSION_INFO.git.commit_hash_short;
}

/**
 * 获取Git分支名
 * @returns {string|null} Git分支名
 */
function getBranch() {
    return VERSION_INFO.git.branch;
}

/**
 * 检查是否为开发版本（工作区有未提交更改）
 * @returns {boolean} 是否为开发版本
 */
function isDevelopmentBuild() {
    return VERSION_INFO.git.is_dirty;
}

/**
 * 格式化版本信息为字符串
 * @returns {string} 格式化的版本信息
 */
function formatVersionString() {
    const version = VERSION_INFO.version;
    const commit = VERSION_INFO.git.commit_hash_short;
    const buildTime = VERSION_INFO.build_date;
    const isDev = VERSION_INFO.git.is_dirty ? '-dev' : '';

    if (commit) {
        return `v${version}${isDev} (${commit}) - ${buildTime}`;
    } else {
        return `v${version}${isDev} - ${buildTime}`;
    }
}

/**
 * 在控制台显示版本信息
 */
function logVersionInfo() {
    console.group('🎮 Split-Second Spark 版本信息');
    console.log('版本号:', getVersion());
    console.log('构建时间:', getBuildTime());
    console.log('Git提交:', getCommitHash() || 'N/A');
    console.log('Git分支:', getBranch() || 'N/A');
    console.log('开发版本:', isDevelopmentBuild() ? '是' : '否');
    console.log('完整信息:', VERSION_INFO);
    console.groupEnd();
}

// 导出函数（支持ES6模块和CommonJS）
if (typeof module !== 'undefined' && module.exports) {
    // CommonJS
    module.exports = {
        VERSION_INFO,
        getVersionInfo,
        getVersion,
        getBuildTime,
        getCommitHash,
        getCommitHashShort,
        getBranch,
        isDevelopmentBuild,
        formatVersionString,
        logVersionInfo
    };
} else if (typeof window !== 'undefined') {
    // 浏览器全局变量
    window.VersionInfo = {
        VERSION_INFO,
        getVersionInfo,
        getVersion,
        getBuildTime,
        getCommitHash,
        getCommitHashShort,
        getBranch,
        isDevelopmentBuild,
        formatVersionString,
        logVersionInfo
    };
}

// 自动在控制台显示版本信息（仅在浏览器环境）
if (typeof window !== 'undefined' && window.console) {
    // 延迟执行，确保页面加载完成
    setTimeout(() => {
        logVersionInfo();
    }, 100);
}
