/**
 * Split-Second Spark - 主应用程序
 * 应用程序的入口点和主要控制逻辑
 */

class SplitSecondSparkApp {
    constructor() {
        this.isInitialized = false;
        this.loadingProgress = 0;
        this.loadingSteps = [
            { name: 'loading.initializing', weight: 10 },
            { name: 'loading.loading_games', weight: 30 },
            { name: 'loading.preparing', weight: 40 },
            { name: 'loading.complete', weight: 20 }
        ];
        this.currentStepIndex = 0;
        
        // 绑定事件处理器
        this.handleResize = this.handleResize.bind(this);
        this.handleVisibilityChange = this.handleVisibilityChange.bind(this);
    }

    /**
     * 初始化应用程序
     */
    async init() {
        try {
            console.log('🚀 启动 Split-Second Spark 应用程序...');
            
            // 显示加载屏幕
            this.showLoadingScreen();
            
            // 初始化各个服务
            await this.initServices();
            
            // 初始化UI组件
            await this.initUI();
            
            // 绑定事件监听器
            this.bindEventListeners();
            
            // 完成初始化
            await this.completeInitialization();
            
            this.isInitialized = true;
            console.log('✅ Split-Second Spark 应用程序初始化完成');
            
        } catch (error) {
            console.error('❌ 应用程序初始化失败:', error);
            this.showErrorScreen(error);
        }
    }

    /**
     * 显示加载屏幕
     */
    showLoadingScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const mainScreen = document.getElementById('main-screen');
        
        if (loadingScreen) {
            loadingScreen.classList.add('active');
        }
        
        if (mainScreen) {
            mainScreen.classList.remove('active');
        }
        
        // 开始加载动画
        this.startLoadingAnimation();
    }

    /**
     * 开始加载动画
     */
    startLoadingAnimation() {
        const progressFill = document.querySelector('.progress-fill');
        const loadingText = document.querySelector('.loading-text');
        
        if (!progressFill || !loadingText) return;
        
        // 更新加载进度
        const updateProgress = () => {
            if (this.currentStepIndex < this.loadingSteps.length) {
                const step = this.loadingSteps[this.currentStepIndex];
                
                // 更新文本
                if (typeof i18nService !== 'undefined') {
                    loadingText.textContent = i18nService.t(step.name);
                } else {
                    loadingText.textContent = step.name;
                }
                
                // 更新进度条
                this.loadingProgress += step.weight;
                progressFill.style.width = `${Math.min(this.loadingProgress, 100)}%`;
                
                this.currentStepIndex++;
                
                // 继续下一步
                if (this.currentStepIndex < this.loadingSteps.length) {
                    setTimeout(updateProgress, 800 + Math.random() * 400);
                }
            }
        };
        
        // 开始更新进度
        setTimeout(updateProgress, 500);
    }

    /**
     * 初始化服务
     */
    async initServices() {
        console.log('🔧 初始化服务...');

        // 首先检查并等待 EdgeOne 存储初始化
        await this.initEdgeOneStorageIfAvailable();

        // 初始化存储服务
        if (typeof storageService !== 'undefined') {
            console.log('🔧 正在初始化 storageService...');
            await storageService.init();
            console.log('✅ storageService 初始化成功');

            // 检查是否可以升级到 EdgeOne 存储
            await this.checkStorageUpgrade();
        }

        // 初始化设置管理器
        if (typeof settingsManager !== 'undefined') {
            await settingsManager.init();
        }

        // 初始化国际化服务
        if (typeof i18nService !== 'undefined') {
            await i18nService.init();
        }

        // 初始化游戏启动器
        if (typeof gameLauncher !== 'undefined') {
            await gameLauncher.init();
        }

        console.log('✅ 服务初始化完成');
    }

    /**
     * 初始化 EdgeOne 存储（如果可用）
     */
    async initEdgeOneStorageIfAvailable() {
        try {
            // 检查是否有 EdgeOne 存储初始化器
            if (typeof window !== 'undefined' && typeof window.initEdgeOneStorage === 'function') {
                console.log('🌐 检测到 EdgeOne 存储初始化器，尝试初始化云存储...');

                // 获取 EdgeOne 存储配置
                const edgeOneConfig = this.getEdgeOneStorageConfig();

                // 初始化 EdgeOne 存储
                const success = await window.initEdgeOneStorage(edgeOneConfig);

                if (success) {
                    console.log('✅ EdgeOne 云存储初始化成功');

                    // 显示存储状态信息
                    if (typeof window.edgeOneInitializer !== 'undefined' && window.edgeOneInitializer.getStatus) {
                        const status = window.edgeOneInitializer.getStatus();
                        console.log('📊 EdgeOne 存储状态:', status);
                    }
                } else {
                    console.log('⚠️ EdgeOne 云存储初始化失败，将使用本地存储');
                }

                return success;
            } else {
                console.log('📝 EdgeOne 存储初始化器未找到，跳过云存储初始化');
                return false;
            }
        } catch (error) {
            console.warn('⚠️ EdgeOne 存储初始化检查失败:', error);
            return false;
        }
    }

    /**
     * 获取 EdgeOne 存储配置
     * @returns {Object} EdgeOne 存储配置
     */
    getEdgeOneStorageConfig() {
        return {
            customConfig: {
                testConnection: true,
                timeout: 15000,
                enableCache: true,
                enableFallback: true,
                retryCount: 3,
                retryDelay: 1500
            }
        };
    }

    /**
     * 检查存储升级
     */
    async checkStorageUpgrade() {
        try {
            if (typeof storageService !== 'undefined' && storageService.checkForEdgeOneUpgrade) {
                const canUpgrade = await storageService.checkForEdgeOneUpgrade();
                if (canUpgrade) {
                    console.log('🚀 检测到可以升级到 EdgeOne 存储');
                    const upgraded = await storageService.upgradeToEdgeOne();
                    if (upgraded) {
                        console.log('✅ 成功升级到 EdgeOne 存储');
                    } else {
                        console.warn('⚠️ EdgeOne 存储升级失败');
                    }
                }
            }
        } catch (error) {
            console.warn('⚠️ 存储升级检查失败:', error);
        }
    }

    /**
     * 初始化UI组件
     */
    async initUI() {
        console.log('🎨 初始化UI组件...');
        
        // 初始化模态框管理器
        if (typeof modalManager !== 'undefined') {
            modalManager.init();
        }
        
        // 初始化游戏卡片
        this.initGameCards();
        
        // 初始化控制按钮
        this.initControlButtons();
        
        console.log('✅ UI组件初始化完成');
    }

    /**
     * 初始化游戏卡片
     */
    initGameCards() {
        const gameCards = document.querySelectorAll('.game-card');

        gameCards.forEach(card => {
            const gameId = card.getAttribute('data-game');
            const playBtn = card.querySelector('.play-btn');
            const previewBtn = card.querySelector('.preview-btn');
            
            // 绑定播放按钮事件
            if (playBtn) {
                playBtn.addEventListener('click', async (e) => {
                    e.stopPropagation();
                    try {
                        if (typeof gameLauncher !== 'undefined') {
                            await gameLauncher.launchGame(gameId);
                        }
                    } catch (error) {
                        console.error('❌ 启动游戏失败:', error);
                        this.showNotification('启动游戏失败', 'error');
                    }
                });
            }
            
            // 绑定预览按钮事件
            if (previewBtn) {
                previewBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    if (typeof modalManager !== 'undefined') {
                        modalManager.showGamePreview(gameId);
                    }
                });
            }
            
            // 绑定卡片点击事件（显示预览）
            card.addEventListener('click', () => {
                if (typeof modalManager !== 'undefined') {
                    modalManager.showGamePreview(gameId);
                }
            });
            
            // 添加悬停效果
            this.addCardHoverEffects(card);
        });
    }

    /**
     * 添加卡片悬停效果
     * @param {HTMLElement} card - 游戏卡片元素
     */
    addCardHoverEffects(card) {
        const particles = card.querySelectorAll('.particle');
        const sparks = card.querySelectorAll('.spark');
        const electrons = card.querySelectorAll('.electron');
        
        card.addEventListener('mouseenter', () => {
            // 加速粒子动画
            particles.forEach(particle => {
                particle.style.animationDuration = '1.5s';
            });
            
            sparks.forEach(spark => {
                spark.style.animationDuration = '1s';
            });
            
            electrons.forEach(electron => {
                electron.style.animationDuration = '2s';
            });
        });
        
        card.addEventListener('mouseleave', () => {
            // 恢复正常动画速度
            particles.forEach(particle => {
                particle.style.animationDuration = '3s';
            });
            
            sparks.forEach(spark => {
                spark.style.animationDuration = '2s';
            });
            
            electrons.forEach(electron => {
                electron.style.animationDuration = '3s';
            });
        });
    }

    /**
     * 初始化控制按钮
     */
    initControlButtons() {
        // 语言切换按钮
        const languageBtn = document.querySelector('[data-action="toggle-language"]');
        if (languageBtn) {
            languageBtn.addEventListener('click', async () => {
                if (typeof i18nService !== 'undefined') {
                    const newLanguage = await i18nService.toggleLanguage();
                    this.updateLanguageButton(languageBtn, newLanguage);
                }
            });
        }
        
        // 设置按钮
        const settingsBtn = document.querySelector('[data-action="show-settings"]');
        if (settingsBtn) {
            settingsBtn.addEventListener('click', () => {
                if (typeof modalManager !== 'undefined') {
                    modalManager.showSettings();
                }
            });
        }
    }

    /**
     * 更新语言按钮
     * @param {HTMLElement} button - 语言按钮
     * @param {string} language - 当前语言
     */
    updateLanguageButton(button, language) {
        const textElement = button.querySelector('.btn-text');
        if (textElement) {
            textElement.textContent = language === 'zh-CN' ? '中文' : 'English';
        }
    }

    /**
     * 绑定事件监听器
     */
    bindEventListeners() {
        // 窗口大小变化
        window.addEventListener('resize', this.handleResize);
        
        // 页面可见性变化
        document.addEventListener('visibilitychange', this.handleVisibilityChange);
        
        // 语言变更事件
        window.addEventListener('languageChanged', (e) => {
            this.handleLanguageChange(e.detail.language);
        });
        
        // 设置变更事件
        window.addEventListener('settingChanged', (e) => {
            this.handleSettingChange(e.detail);
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });
    }

    /**
     * 处理窗口大小变化
     */
    handleResize() {
        // 更新视口高度变量（用于移动设备）
        const vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    /**
     * 处理页面可见性变化
     */
    handleVisibilityChange() {
        if (document.hidden) {
            console.log('📱 应用程序进入后台');
            // 可以在这里暂停动画或减少资源使用
        } else {
            console.log('📱 应用程序回到前台');
            // 恢复动画或资源使用
        }
    }

    /**
     * 处理语言变更
     * @param {string} language - 新语言
     */
    handleLanguageChange(language) {
        console.log(`🌍 语言已变更为: ${language}`);
        
        // 更新语言按钮显示
        const languageBtn = document.querySelector('[data-action="toggle-language"]');
        if (languageBtn) {
            this.updateLanguageButton(languageBtn, language);
        }
    }

    /**
     * 处理设置变更
     * @param {Object} detail - 设置变更详情
     */
    handleSettingChange(detail) {
        const { key, newValue } = detail;
        console.log(`⚙️ 设置已变更 [${key}]: ${newValue}`);
        
        // 根据设置变更执行相应操作
        switch (key) {
            case 'visual_effects':
                this.updateVisualEffects(newValue);
                break;
            case 'reduce_motion':
                this.updateMotionSettings(newValue);
                break;
        }
    }

    /**
     * 处理键盘快捷键
     * @param {KeyboardEvent} e - 键盘事件
     */
    handleKeyboardShortcuts(e) {
        // Ctrl/Cmd + , 打开设置
        if ((e.ctrlKey || e.metaKey) && e.key === ',') {
            e.preventDefault();
            if (typeof modalManager !== 'undefined') {
                modalManager.showSettings();
            }
        }
        
        // Ctrl/Cmd + L 切换语言
        if ((e.ctrlKey || e.metaKey) && e.key === 'l') {
            e.preventDefault();
            if (typeof i18nService !== 'undefined') {
                i18nService.toggleLanguage();
            }
        }
    }

    /**
     * 更新视觉效果
     * @param {string} level - 效果级别
     */
    updateVisualEffects(level) {
        const cards = document.querySelectorAll('.game-card');
        cards.forEach(card => {
            if (level === 'low') {
                card.style.transition = 'none';
            } else {
                card.style.transition = '';
            }
        });
    }

    /**
     * 更新动画设置
     * @param {boolean} reduceMotion - 是否减少动画
     */
    updateMotionSettings(reduceMotion) {
        if (reduceMotion) {
            document.documentElement.style.setProperty('--animation-duration', '0s');
        } else {
            document.documentElement.style.removeProperty('--animation-duration');
        }
    }

    /**
     * 完成初始化
     */
    async completeInitialization() {
        // 等待最后的加载步骤完成
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 隐藏加载屏幕，显示主界面
        this.showMainScreen();
        
        // 执行初始化后的任务
        this.postInitializationTasks();
    }

    /**
     * 显示主界面
     */
    showMainScreen() {
        const loadingScreen = document.getElementById('loading-screen');
        const mainScreen = document.getElementById('main-screen');
        
        if (loadingScreen) {
            loadingScreen.classList.remove('active');
        }
        
        if (mainScreen) {
            mainScreen.classList.add('active');
        }
        
        // 触发主界面显示事件
        window.dispatchEvent(new CustomEvent('mainScreenShown'));
    }

    /**
     * 初始化后的任务
     */
    postInitializationTasks() {
        // 预加载游戏资源
        this.preloadGameAssets();
        
        // 检查更新
        this.checkForUpdates();
        
        // 发送分析数据
        this.sendAnalytics();
    }

    /**
     * 预加载游戏资源
     */
    async preloadGameAssets() {
        try {
            console.log('📦 开始预加载游戏资源...');
            
            // 这里可以预加载游戏的关键资源
            // 例如图片、音频文件等
            
            console.log('✅ 游戏资源预加载完成');
        } catch (error) {
            console.warn('⚠️ 游戏资源预加载失败:', error);
        }
    }

    /**
     * 检查更新
     */
    async checkForUpdates() {
        try {
            // 这里可以实现版本检查逻辑
            console.log('🔄 检查应用程序更新...');
        } catch (error) {
            console.warn('⚠️ 检查更新失败:', error);
        }
    }

    /**
     * 发送分析数据
     */
    async sendAnalytics() {
        try {
            if (typeof settingsManager !== 'undefined' && 
                settingsManager.get('analytics_enabled')) {
                // 发送应用启动事件
                console.log('📊 发送分析数据...');
            }
        } catch (error) {
            console.warn('⚠️ 发送分析数据失败:', error);
        }
    }

    /**
     * 显示通知
     * @param {string} message - 消息内容
     * @param {string} type - 通知类型
     */
    showNotification(message, type = 'info') {
        if (typeof modalManager !== 'undefined') {
            modalManager.showNotification(message, type);
        } else {
            console.log(`📢 ${type.toUpperCase()}: ${message}`);
        }
    }

    /**
     * 显示错误屏幕
     * @param {Error} error - 错误对象
     */
    showErrorScreen(error) {
        const loadingScreen = document.getElementById('loading-screen');
        if (loadingScreen) {
            const loadingText = loadingScreen.querySelector('.loading-text');
            if (loadingText) {
                loadingText.textContent = `初始化失败: ${error.message}`;
                loadingText.style.color = 'var(--warning-color)';
            }
        }
    }
}

// 创建应用程序实例
const app = new SplitSecondSparkApp();

// 当DOM加载完成时启动应用程序
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => app.init());
} else {
    app.init();
}

// 导出应用程序实例
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { SplitSecondSparkApp, app };
} else {
    window.app = app;
}
