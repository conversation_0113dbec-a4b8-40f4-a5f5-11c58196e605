/**
 * Split-Second Spark - 存储配置
 * 定义各种存储方案的配置选项
 */

/**
 * 存储配置类
 */
class StorageConfig {
    constructor() {
        this.configs = {
            // 基础本地存储配置
            local: {
                dbName: 'SplitSecondSparkDB',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['indexeddb', 'localstorage', 'memory']
            },

            // Firebase 云存储配置
            firebase: {
                dbName: 'SplitSecondSparkDB',
                dbVersion: 1,
                enableCloudSync: true,
                syncInterval: 5 * 60 * 1000, // 5分钟同步一次
                adapterPriority: ['hybrid', 'indexeddb', 'localstorage', 'memory'],
                cloudConfig: {
                    type: 'firebase',
                    config: {
                        // Firebase 配置 - 需要用户提供实际的配置信息
                        apiKey: "your-api-key",
                        authDomain: "your-project.firebaseapp.com",
                        projectId: "your-project-id",
                        storageBucket: "your-project.appspot.com",
                        messagingSenderId: "123456789",
                        appId: "your-app-id"
                    }
                }
            },

            // 高性能本地存储配置
            performance: {
                dbName: 'SplitSecondSparkDB_Performance',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['indexeddb'], // 仅使用 IndexedDB 以获得最佳性能
                cacheSize: 1000, // 缓存大小
                compressionEnabled: true // 启用数据压缩
            },

            // 隐私模式配置（仅内存存储）
            privacy: {
                dbName: 'SplitSecondSparkDB_Privacy',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['memory'], // 仅使用内存存储，不保存到磁盘
                autoCleanup: true // 自动清理数据
            },

            // 开发调试配置
            debug: {
                dbName: 'SplitSecondSparkDB_Debug',
                dbVersion: 1,
                enableCloudSync: false,
                adapterPriority: ['indexeddb', 'localstorage', 'memory'],
                verboseLogging: true, // 详细日志
                debugMode: true // 调试模式
            }
        };

        // 当前使用的配置
        this.currentConfig = 'local';
        
        console.log('📋 存储配置管理器已初始化');
    }

    /**
     * 获取指定配置
     * @param {string} configName - 配置名称
     * @returns {Object} 配置对象
     */
    getConfig(configName = null) {
        const name = configName || this.currentConfig;
        
        if (!this.configs[name]) {
            console.warn(`⚠️ 配置 "${name}" 不存在，使用默认配置`);
            return this.configs.local;
        }
        
        return { ...this.configs[name] };
    }

    /**
     * 设置当前配置
     * @param {string} configName - 配置名称
     */
    setConfig(configName) {
        if (!this.configs[configName]) {
            throw new Error(`配置 "${configName}" 不存在`);
        }
        
        this.currentConfig = configName;
        console.log(`📋 已切换到配置: ${configName}`);
    }

    /**
     * 添加自定义配置
     * @param {string} name - 配置名称
     * @param {Object} config - 配置对象
     */
    addConfig(name, config) {
        this.configs[name] = { ...config };
        console.log(`📋 已添加自定义配置: ${name}`);
    }

    /**
     * 获取所有可用配置
     * @returns {Array} 配置名称列表
     */
    getAvailableConfigs() {
        return Object.keys(this.configs);
    }

    /**
     * 根据环境自动选择配置
     * 注意：EdgeOne 存储检测现在需要异步操作，此方法不再检测 EdgeOne
     */
    autoSelectConfig() {
        // 注意：EdgeOne 存储检测已移至 createStorageService 方法中进行异步检测
        // 这里只进行同步的环境检测

        // 检测是否在隐私模式下
        if (this.isPrivateMode()) {
            this.setConfig('privacy');
            return 'privacy';
        }

        // 检测是否在开发环境
        if (this.isDevelopmentMode()) {
            this.setConfig('debug');
            return 'debug';
        }

        // 检测是否支持高性能存储
        if (this.supportsHighPerformanceStorage()) {
            this.setConfig('performance');
            return 'performance';
        }

        // 默认使用本地存储
        this.setConfig('local');
        return 'local';
    }

    /**
     * 检测是否在隐私模式下
     */
    isPrivateMode() {
        try {
            // 尝试使用 localStorage
            localStorage.setItem('test', 'test');
            localStorage.removeItem('test');
            return false;
        } catch (e) {
            return true;
        }
    }

    /**
     * 检测是否在开发环境
     */
    isDevelopmentMode() {
        return location.hostname === 'localhost' || 
               location.hostname === '127.0.0.1' ||
               location.protocol === 'file:' ||
               localStorage.getItem('debug') === 'true';
    }

    /**
     * 检测是否支持高性能存储
     */
    supportsHighPerformanceStorage() {
        return 'indexedDB' in window && 
               'serviceWorker' in navigator &&
               window.performance && 
               window.performance.memory;
    }

    /**
     * 创建存储服务实例
     * @param {string} configName - 配置名称（可选）
     * @returns {Promise<Object>} 存储服务实例
     */
    async createStorageService(configName = null) {
        // 首先尝试检测 EdgeOne 云存储（基于 HTTP 接口）
        console.log('🔍 检测 EdgeOne 云存储可用性...');

        try {
            // 执行基础的 EdgeOne 存储检查
            const isBasicallyAvailable = await this.isEdgeOneStorageBasicallyAvailable();

            if (isBasicallyAvailable) {
                console.log('🌐 EdgeOne 存储基础检查通过，进行详细验证...');

                // 执行详细的可用性检查
                const availabilityResult = await this.isEdgeOneStorageAvailable(true);

                if (availabilityResult.available) {
                    console.log('✅ EdgeOne 云存储验证通过，使用云存储方案');
                    console.log('📊 EdgeOne 存储详情:', availabilityResult.details);

                    // 使用基础存储服务，它已经支持 EdgeOne 云存储
                    const { default: StorageService } = await import('../utils/storage.js');
                    const storageService = new StorageService();
                    await storageService.init();

                    console.log('✅ EdgeOne 云存储服务已创建');
                    return storageService;
                } else {
                    console.warn('⚠️ EdgeOne 云存储详细验证失败:', availabilityResult.reason);
                    console.warn('📊 失败详情:', availabilityResult.details);
                    console.log('🔄 回退到本地存储配置');
                }
            } else {
                console.log('📝 EdgeOne 存储基础检查未通过，使用本地存储配置');
            }
        } catch (error) {
            console.warn('⚠️ EdgeOne 云存储检测过程中发生错误:', error);
            console.log('🔄 回退到本地存储配置');
        }

        // 如果没有 EdgeOne 云存储，使用原有逻辑
        const config = this.getConfig(configName);

        // 根据配置选择存储服务类型
        let StorageServiceClass;

        if (config.enableCloudSync) {
            // 使用增强存储服务（支持云同步）
            const { default: EnhancedStorageService } = await import('../utils/enhanced-storage.js');
            StorageServiceClass = EnhancedStorageService;
        } else {
            // 使用基础存储服务
            const { default: StorageService } = await import('../utils/storage.js');
            StorageServiceClass = StorageService;
        }

        const storageService = new StorageServiceClass(config);
        await storageService.init();

        console.log(`✅ 存储服务已创建，配置: ${configName || this.currentConfig}`);
        return storageService;
    }

    /**
     * 获取存储方案推荐
     * 注意：EdgeOne 存储检测现在需要异步操作，推荐中不再包含 EdgeOne
     */
    getRecommendations() {
        const recommendations = [];

        // 注意：EdgeOne 云存储检测已改为异步 HTTP 接口检测
        // 推荐列表中不再包含 EdgeOne，需要通过 createStorageService 方法进行检测

        // 基于用户环境给出推荐
        if (this.isPrivateMode()) {
            recommendations.push({
                config: 'privacy',
                reason: '检测到隐私模式，推荐使用内存存储以保护隐私',
                priority: 'high'
            });
        }

        if (this.isDevelopmentMode()) {
            recommendations.push({
                config: 'debug',
                reason: '检测到开发环境，推荐使用调试配置以便调试',
                priority: 'medium'
            });
        }

        if (this.supportsHighPerformanceStorage()) {
            recommendations.push({
                config: 'performance',
                reason: '设备支持高性能存储，推荐使用性能优化配置',
                priority: 'medium'
            });
        }

        // 检测网络连接
        if (navigator.onLine && 'serviceWorker' in navigator) {
            recommendations.push({
                config: 'firebase',
                reason: '网络连接良好，可以考虑使用云存储同步数据',
                priority: 'low'
            });
        }

        // 如果没有特殊推荐，推荐默认配置
        if (recommendations.length === 0) {
            recommendations.push({
                config: 'local',
                reason: '推荐使用标准本地存储配置',
                priority: 'medium'
            });
        }

        return recommendations;
    }

    /**
     * 显示配置信息
     */
    displayConfigInfo(configName = null) {
        const config = this.getConfig(configName);
        const name = configName || this.currentConfig;
        
        console.group(`📋 存储配置信息: ${name}`);
        console.log('数据库名称:', config.dbName);
        console.log('数据库版本:', config.dbVersion);
        console.log('云同步:', config.enableCloudSync ? '启用' : '禁用');
        console.log('适配器优先级:', config.adapterPriority);
        
        if (config.cloudConfig) {
            console.log('云存储类型:', config.cloudConfig.type);
        }
        
        if (config.syncInterval) {
            console.log('同步间隔:', `${config.syncInterval / 1000}秒`);
        }
        
        console.groupEnd();
    }
}

/**
 * 存储配置管理器单例
 */
class StorageConfigManager {
    constructor() {
        if (StorageConfigManager.instance) {
            return StorageConfigManager.instance;
        }

        this.config = new StorageConfig();
        this.currentStorageService = null;
        this.currentConfigName = null;
        this.isInitialized = false;
        StorageConfigManager.instance = this;
    }

    static getInstance() {
        if (!StorageConfigManager.instance) {
            StorageConfigManager.instance = new StorageConfigManager();
        }
        return StorageConfigManager.instance;
    }

    getConfig(name) {
        return this.config.getConfig(name);
    }

    setConfig(name) {
        return this.config.setConfig(name);
    }

    async createStorageService(configName = null) {
        const selectedConfig = configName || this.autoSelectConfig();
        const storageService = await this.config.createStorageService(selectedConfig);

        // 保存当前存储服务和配置信息
        this.currentStorageService = storageService;
        this.currentConfigName = selectedConfig;
        this.isInitialized = true;

        console.log(`✅ 存储服务已创建，配置: ${selectedConfig}`);
        return storageService;
    }

    /**
     * 验证 EdgeOne 云存储并创建存储服务
     * @param {boolean} performConnectivityTest - 是否执行连接测试
     * @returns {Promise<Object>} 包含存储服务和验证结果的对象
     */
    async createStorageServiceWithEdgeOneValidation(performConnectivityTest = true) {
        const result = {
            storageService: null,
            edgeOneValidation: null,
            configUsed: null,
            success: false
        };

        try {
            // 尝试检测 EdgeOne 存储（基于 HTTP 接口）
            console.log('🔍 检测 EdgeOne 云存储（HTTP 接口）...');

            const isBasicallyAvailable = await this.config.isEdgeOneStorageBasicallyAvailable();

            if (isBasicallyAvailable) {
                console.log('🌐 EdgeOne 存储基础检查通过，进行详细验证...');

                const validationResult = await this.config.isEdgeOneStorageAvailable(performConnectivityTest);
                result.edgeOneValidation = validationResult;

                if (validationResult.available) {
                    console.log('✅ EdgeOne 云存储验证通过');
                    result.configUsed = 'edgeone';
                } else {
                    console.warn('⚠️ EdgeOne 云存储验证失败，使用备用配置');
                    result.configUsed = this.autoSelectConfig();
                }
            } else {
                console.log('📝 EdgeOne 存储基础检查未通过，使用标准配置选择');
                result.configUsed = this.autoSelectConfig();
            }

            // 创建存储服务
            result.storageService = await this.createStorageService(result.configUsed);
            result.success = true;

            return result;

        } catch (error) {
            console.error('❌ 创建存储服务失败:', error);
            result.error = error.message;
            return result;
        }
    }

    autoSelectConfig() {
        return this.config.autoSelectConfig();
    }

    getRecommendations() {
        return this.config.getRecommendations();
    }

    displayConfigInfo(configName) {
        return this.config.displayConfigInfo(configName);
    }

    /**
     * 获取当前存储服务
     * @returns {Object|null} 当前存储服务实例
     */
    getCurrentStorageService() {
        return this.currentStorageService;
    }

    /**
     * 获取当前配置信息
     * @returns {Object|null} 当前配置对象
     */
    getCurrentConfig() {
        if (!this.currentConfigName) {
            return null;
        }
        return this.config.getConfig(this.currentConfigName);
    }

    /**
     * 获取当前配置名称
     * @returns {string|null} 当前配置名称
     */
    getCurrentConfigName() {
        return this.currentConfigName;
    }

    /**
     * 检查存储服务是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isStorageServiceInitialized() {
        return this.isInitialized && this.currentStorageService !== null;
    }

    /**
     * 检查当前配置是否支持云存储
     * @returns {boolean} 是否支持云存储
     */
    isCloudStorageEnabled() {
        // 注意：EdgeOne 云存储检测已改为异步 HTTP 接口检测
        // 此方法不再检测 EdgeOne，只检查配置中的云存储选项

        const currentConfig = this.getCurrentConfig();
        if (!currentConfig) {
            return false;
        }

        return currentConfig.enableCloudSync ||
               (currentConfig.cloudConfig && currentConfig.cloudConfig.type) ||
               (currentConfig.adapters && currentConfig.adapters.some(adapter =>
                   adapter.type === 'firebase' ||
                   adapter.type === 'user-cloud' ||
                   adapter.type === 'hybrid'
               ));
    }

    /**
     * 基于 HTTP 接口的 EdgeOne 云存储可用性检查
     * @param {boolean} performConnectivityTest - 是否执行连接测试
     * @param {string} baseUrl - API 基础 URL，默认使用相对路径
     * @returns {Promise<Object>} 详细的可用性检查结果
     */
    async isEdgeOneStorageAvailable(performConnectivityTest = false, baseUrl = '') {
        const result = {
            available: false,
            reason: '',
            details: {},
            timestamp: Date.now(),
            checkMethod: 'http_interface'
        };

        try {
            // 1. 基础环境检查
            if (typeof window === 'undefined') {
                result.reason = '非浏览器环境';
                return result;
            }

            if (typeof fetch === 'undefined') {
                result.reason = 'fetch API 不可用';
                return result;
            }

            // 2. 构建测试 URL
            const testUrl = baseUrl ? `${baseUrl}/storage/get` : '/storage/get';
            const testKey = '__deployment_test__';
            const fullUrl = `${testUrl}?key=${encodeURIComponent(testKey)}`;

            result.details.testUrl = fullUrl;
            result.details.testKey = testKey;

            console.log(`🔍 EdgeOne 存储接口检测: ${fullUrl}`);

            // 3. 执行基础可用性检查
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5秒超时

            try {
                const response = await fetch(fullUrl, {
                    method: 'GET',
                    signal: controller.signal,
                    headers: {
                        'Accept': 'application/json',
                        'Cache-Control': 'no-cache'
                    }
                });

                clearTimeout(timeoutId);

                result.details.httpStatus = response.status;
                result.details.httpStatusText = response.statusText;

                // 4. 检查 HTTP 状态码
                if (response.status !== 200) {
                    result.reason = `HTTP 状态码错误: ${response.status} ${response.statusText}`;
                    result.details.checkResult = 'http_error';
                    return result;
                }

                // 5. 解析响应内容
                let responseData;
                try {
                    responseData = await response.json();
                    result.details.responseData = responseData;
                } catch (parseError) {
                    result.reason = `响应解析失败: ${parseError.message}`;
                    result.details.parseError = parseError.message;
                    result.details.checkResult = 'parse_error';
                    return result;
                }

                // 6. 验证响应格式
                if (!responseData || typeof responseData !== 'object') {
                    result.reason = '响应格式无效：不是有效的 JSON 对象';
                    result.details.checkResult = 'invalid_format';
                    return result;
                }

                // 7. 检查必要的 API 字段
                const requiredFields = ['success', 'message'];
                const missingFields = [];

                for (const field of requiredFields) {
                    if (!(field in responseData)) {
                        missingFields.push(field);
                    }
                }

                if (missingFields.length > 0) {
                    result.reason = `响应缺少必要字段: ${missingFields.join(', ')}`;
                    result.details.missingFields = missingFields;
                    result.details.checkResult = 'missing_fields';
                    return result;
                }

                // 8. 验证 API 响应结构
                result.details.apiFields = {
                    success: typeof responseData.success,
                    message: typeof responseData.message,
                    found: typeof responseData.found,
                    data: typeof responseData.data
                };

                // 9. 执行连接测试（可选）
                if (performConnectivityTest) {
                    try {
                        const connectivityResult = await this.performEdgeOneConnectivityTest(baseUrl);
                        result.details.connectivityTest = connectivityResult;

                        if (!connectivityResult.success) {
                            result.reason = `连接测试失败: ${connectivityResult.error}`;
                            result.details.checkResult = 'connectivity_failed';
                            return result;
                        }
                    } catch (connectivityError) {
                        result.reason = `连接测试异常: ${connectivityError.message}`;
                        result.details.connectivityError = connectivityError.message;
                        result.details.checkResult = 'connectivity_error';
                        return result;
                    }
                }

                // 10. 所有检查通过
                result.available = true;
                result.reason = 'EdgeOne 存储接口检查通过';
                result.details.checkResult = 'success';

                console.log('✅ EdgeOne 存储接口检查通过');
                return result;

            } catch (fetchError) {
                clearTimeout(timeoutId);

                if (fetchError.name === 'AbortError') {
                    result.reason = '请求超时（5秒）';
                    result.details.checkResult = 'timeout';
                } else {
                    result.reason = `网络请求失败: ${fetchError.message}`;
                    result.details.fetchError = fetchError.message;
                    result.details.checkResult = 'network_error';
                }
                return result;
            }

        } catch (error) {
            result.reason = `检查过程中发生异常: ${error.message}`;
            result.details.exception = error.message;
            result.details.checkResult = 'exception';
            return result;
        }
    }

    /**
     * 执行 EdgeOne 存储连接测试
     * @param {string} baseUrl - API 基础 URL
     * @returns {Promise<Object>} 连接测试结果
     */
    async performEdgeOneConnectivityTest(baseUrl = '') {
        const testResult = {
            success: false,
            operations: {},
            error: null
        };

        try {
            const testKey = `connectivity_test_${Date.now()}`;
            const testValue = { message: '连接测试', timestamp: Date.now() };
            const apiBase = baseUrl || '';

            // 测试 PUT 操作
            try {
                const putResponse = await fetch(`${apiBase}/storage/put`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ key: testKey, value: testValue })
                });

                testResult.operations.put = {
                    status: putResponse.status,
                    success: putResponse.status === 200
                };

                if (putResponse.status !== 200) {
                    testResult.error = `PUT 操作失败: ${putResponse.status}`;
                    return testResult;
                }
            } catch (putError) {
                testResult.operations.put = { success: false, error: putError.message };
                testResult.error = `PUT 操作异常: ${putError.message}`;
                return testResult;
            }

            // 测试 GET 操作
            try {
                const getResponse = await fetch(`${apiBase}/storage/get?key=${encodeURIComponent(testKey)}`, {
                    method: 'GET',
                    headers: { 'Accept': 'application/json' }
                });

                testResult.operations.get = {
                    status: getResponse.status,
                    success: getResponse.status === 200
                };

                if (getResponse.status === 200) {
                    const getData = await getResponse.json();
                    testResult.operations.get.dataMatch = JSON.stringify(getData.data) === JSON.stringify(testValue);
                }
            } catch (getError) {
                testResult.operations.get = { success: false, error: getError.message };
                testResult.error = `GET 操作异常: ${getError.message}`;
                return testResult;
            }

            // 测试 DELETE 操作
            try {
                const deleteResponse = await fetch(`${apiBase}/storage/delete`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({ key: testKey })
                });

                testResult.operations.delete = {
                    status: deleteResponse.status,
                    success: deleteResponse.status === 200
                };
            } catch (deleteError) {
                testResult.operations.delete = { success: false, error: deleteError.message };
                // DELETE 失败不影响整体测试结果
            }

            // 检查所有关键操作是否成功
            const putSuccess = testResult.operations.put?.success;
            const getSuccess = testResult.operations.get?.success;
            const dataMatch = testResult.operations.get?.dataMatch;

            if (putSuccess && getSuccess && dataMatch) {
                testResult.success = true;
            } else {
                testResult.error = '数据一致性验证失败';
            }

            return testResult;

        } catch (error) {
            testResult.error = `连接测试异常: ${error.message}`;
            return testResult;
        }
    }

    /**
     * 简化的 EdgeOne 存储可用性检查（基于 HTTP 接口）
     * @param {string} baseUrl - API 基础 URL，默认使用相对路径
     * @returns {Promise<boolean>} EdgeOne 云存储是否基本可用
     */
    async isEdgeOneStorageBasicallyAvailable(baseUrl = '') {
        try {
            const testUrl = baseUrl ? `${baseUrl}/storage/get` : '/storage/get';
            const testKey = '__deployment_test__';
            const fullUrl = `${testUrl}?key=${encodeURIComponent(testKey)}`;

            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 3000); // 3秒快速检查

            const response = await fetch(fullUrl, {
                method: 'GET',
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'Cache-Control': 'no-cache'
                }
            });

            clearTimeout(timeoutId);

            if (response.status !== 200) {
                return false;
            }

            const data = await response.json();
            return data && typeof data === 'object' && 'success' in data && 'message' in data;

        } catch (error) {
            console.warn('⚠️ EdgeOne 存储基础检查失败:', error.message);
            return false;
        }
    }

    /**
     * 获取 EdgeOne 云存储信息（基于 HTTP 接口检测）
     * @param {string} baseUrl - API 基础 URL，默认使用相对路径
     * @returns {Promise<Object|null>} EdgeOne 云存储信息
     */
    async getEdgeOneStorageInfo(baseUrl = '') {
        try {
            // 执行基础检查
            const isBasicallyAvailable = await this.isEdgeOneStorageBasicallyAvailable(baseUrl);

            if (!isBasicallyAvailable) {
                return {
                    available: false,
                    reason: 'EdgeOne 存储基础检查未通过',
                    checkMethod: 'http_interface',
                    timestamp: Date.now()
                };
            }

            // 执行详细检查
            const detailedResult = await this.isEdgeOneStorageAvailable(false, baseUrl);

            return {
                available: detailedResult.available,
                reason: detailedResult.reason,
                checkMethod: 'http_interface',
                details: detailedResult.details,
                timestamp: detailedResult.timestamp,

                // HTTP 接口信息
                interfaceInfo: {
                    baseUrl: baseUrl || '相对路径',
                    testEndpoint: '/storage/get',
                    supportedOperations: ['GET', 'PUT', 'DELETE', 'LIST']
                }
            };

        } catch (error) {
            return {
                available: false,
                reason: `获取存储信息失败: ${error.message}`,
                checkMethod: 'http_interface',
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    /**
     * 获取 EdgeOne 云存储详细信息（包含连接测试）
     * @param {boolean} includeConnectivityTest - 是否包含连接测试
     * @param {string} baseUrl - API 基础 URL，默认使用相对路径
     * @returns {Promise<Object|null>} EdgeOne 云存储详细信息
     */
    async getEdgeOneStorageDetailedInfo(includeConnectivityTest = false, baseUrl = '') {
        try {
            // 执行完整的可用性检查
            const availabilityResult = await this.isEdgeOneStorageAvailable(includeConnectivityTest, baseUrl);

            return {
                available: availabilityResult.available,
                reason: availabilityResult.reason,
                checkMethod: 'http_interface',
                details: availabilityResult.details,
                timestamp: availabilityResult.timestamp,

                // 检查配置信息
                checkConfig: {
                    baseUrl: baseUrl || '相对路径',
                    connectivityTest: includeConnectivityTest,
                    timeout: '5秒（基础检查）/ 3秒（快速检查）'
                },

                // 接口能力信息
                capabilities: {
                    httpInterface: true,
                    asyncOperations: true,
                    connectivityTesting: includeConnectivityTest,
                    errorHandling: true,
                    timeoutControl: true
                }
            };

        } catch (error) {
            return {
                available: false,
                reason: `获取详细信息失败: ${error.message}`,
                checkMethod: 'http_interface',
                error: error.message,
                timestamp: Date.now()
            };
        }
    }

    /**
     * 重置存储服务
     */
    resetStorageService() {
        if (this.currentStorageService && typeof this.currentStorageService.destroy === 'function') {
            this.currentStorageService.destroy();
        }

        this.currentStorageService = null;
        this.currentConfigName = null;
        this.isInitialized = false;

        console.log('🔄 存储服务已重置');
    }

    /**
     * 切换存储配置
     * @param {string} configName 新的配置名称
     * @returns {Promise<Object>} 新的存储服务实例
     */
    async switchStorageConfig(configName) {
        console.log(`🔄 切换存储配置: ${this.currentConfigName} → ${configName}`);

        // 重置当前服务
        this.resetStorageService();

        // 创建新的存储服务
        return await this.createStorageService(configName);
    }
}

// 导出配置类
if (typeof window !== 'undefined') {
    window.StorageConfig = StorageConfig;
    window.StorageConfigManager = StorageConfigManager;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        StorageConfig,
        StorageConfigManager
    };
}
