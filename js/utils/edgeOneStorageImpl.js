/**
 * EdgeOne 存储服务实现类
 * 
 * 功能说明：
 * - 通过 HTTP 请求调用 Cloudflare Functions 存储 API 端点
 * - 实现完整的 CRUD 操作（创建、读取、更新、删除）
 * - 提供统一的接口封装，简化存储操作
 * - 支持错误处理和重试机制
 * - 支持批量操作和缓存功能
 * 
 * 使用方式：
 * const storage = new EdgeOneStorageImpl('https://your-domain.com');
 * await storage.put('key', 'value');
 * const value = await storage.get('key');
 * await storage.delete('key');
 * const keys = await storage.list();
 */

class EdgeOneStorageImpl {
  /**
   * 构造函数
   * @param {string|Object} baseUrl - API 基础 URL（可选），如果不提供则使用相对路径 '/storage/'
   * @param {Object} options - 配置选项
   */
  constructor(baseUrl, options = {}) {
    // 处理参数重载：如果第一个参数是对象，则视为 options
    if (typeof baseUrl === 'object' && baseUrl !== null) {
      options = baseUrl;
      baseUrl = undefined;
    }

    // 设置 API 基础 URL
    if (baseUrl) {
      // 移除 URL 末尾的斜杠
      this.baseUrl = baseUrl.replace(/\/$/, '');
    } else {
      // 使用相对路径，默认调用本地的 functions/storage/ 端点
      this.baseUrl = '';
      this.useRelativePath = true;
    }

    // 配置选项
    this.options = {
      timeout: options.timeout || 30000,        // 请求超时时间（毫秒）
      retryCount: options.retryCount || 3,      // 重试次数
      retryDelay: options.retryDelay || 1000,   // 重试延迟（毫秒）
      enableCache: options.enableCache || false, // 是否启用缓存
      cacheTimeout: options.cacheTimeout || 300000, // 缓存超时时间（毫秒）
      enableFallback: options.enableFallback !== false, // 是否启用本地存储降级，默认启用
      ...options
    };

    // 本地缓存（如果启用）
    this.cache = new Map();

    // 请求计数器（用于调试）
    this.requestCount = 0;

    // 存储方式标识
    this.storageMode = 'remote'; // 'remote' | 'local'
    this.isAvailable = null; // null | true | false
    this.initializationPromise = null; // 初始化 Promise，用于跟踪异步初始化状态

    // 如果启用降级机制，在构造时进行可用性检测
    if (this.options.enableFallback) {
      console.log('[EdgeOneStorage] 🚀 启动异步初始化检测...');
      console.log('[EdgeOneStorage] 🔧 配置信息:', {
        enableFallback: this.options.enableFallback,
        useRelativePath: this.useRelativePath,
        baseUrl: this.baseUrl,
        timeout: this.options.timeout
      });

      // 立即启动异步初始化，不阻塞构造函数
      this.initializationPromise = this._performInitialization();
    } else {
      console.log('[EdgeOneStorage] ⚠️ 降级功能已禁用，将始终使用远程存储');
      this.isAvailable = true; // 假设远程存储可用
    }
  }

  /**
   * 执行初始化检测的包装方法
   * @private
   */
  async _performInitialization() {
    try {
      console.log('[EdgeOneStorage] 🔄 开始执行初始化检测...');

      // 使用 setTimeout 确保异步执行
      await new Promise(resolve => setTimeout(resolve, 0));

      await this._initializeStorage();
      console.log('[EdgeOneStorage] ✅ 初始化检测完成');
      return true;
    } catch (error) {
      console.warn('[EdgeOneStorage] ❌ 初始化检测失败:', error.message);
      console.warn('[EdgeOneStorage] ❌ 错误详情:', error);
      // 确保初始化失败时设置为本地存储模式
      this.storageMode = 'local';
      this.isAvailable = false;
      return false;
    }
  }

  /**
   * 初始化存储服务，检测 Functions 部署状态并选择存储方式
   * @private
   */
  async _initializeStorage() {
    try {
      console.log('[EdgeOneStorage] 🔍 开始检测 Cloudflare Functions 部署状态...');
      console.log('[EdgeOneStorage] 🔍 当前配置:', {
        enableFallback: this.options.enableFallback,
        useRelativePath: this.useRelativePath,
        baseUrl: this.baseUrl,
        timeout: this.options.timeout
      });

      // 检测 Functions 是否已部署并生效
      console.log('[EdgeOneStorage] 🔍 调用 _checkFunctionsDeployment()...');
      const functionsAvailable = await this._checkFunctionsDeployment();
      console.log('[EdgeOneStorage] 🔍 检测结果:', functionsAvailable);

      if (functionsAvailable) {
        console.log('[EdgeOneStorage] ✅ Cloudflare Functions 已部署并生效，使用远程存储');
        this.storageMode = 'remote';
        this.isAvailable = true;
      } else {
        console.warn('[EdgeOneStorage] ⚠️ Cloudflare Functions 未部署或不可用，切换到本地存储模式');
        this.storageMode = 'local';
        this.isAvailable = false;
      }

      console.log('[EdgeOneStorage] 🔍 最终状态:', {
        storageMode: this.storageMode,
        isAvailable: this.isAvailable
      });

    } catch (error) {
      console.error('[EdgeOneStorage] ❌ 存储服务初始化失败:', error);
      console.error('[EdgeOneStorage] ❌ 错误堆栈:', error.stack);

      if (this.options.enableFallback) {
        console.warn('[EdgeOneStorage] 🔄 初始化失败，默认使用本地存储模式');
        this.storageMode = 'local';
        this.isAvailable = false;
      } else {
        // 如果没有启用降级，重新抛出错误
        throw error;
      }
    }
  }

  /**
   * 检测 Cloudflare Functions 是否已部署并生效
   * @returns {Promise<boolean>} Functions 是否可用
   * @private
   */
  async _checkFunctionsDeployment() {
    try {
      // 构建测试 URL
      let testUrl;
      if (this.useRelativePath) {
        // 使用相对路径测试本地 Functions
        testUrl = '/storage/get?key=__deployment_test__';
      } else {
        // 使用完整 URL 测试远程 Functions
        testUrl = `${this.baseUrl}/storage/get?key=__deployment_test__`;
      }

      console.log(`[EdgeOneStorage] 🔍 测试 Functions 端点: ${testUrl}`);
      console.log(`[EdgeOneStorage] 🔍 使用相对路径: ${this.useRelativePath || false}`);
      console.log(`[EdgeOneStorage] 🔍 基础 URL: ${this.baseUrl || '(空)'}`);

      // 发送测试请求，使用 5 秒超时
      const controller = new AbortController();
      const timeoutId = setTimeout(() => {
        console.warn('[EdgeOneStorage] ⏰ 请求超时，取消请求');
        controller.abort();
      }, 5000);

      console.log('[EdgeOneStorage] 🔍 发送 HTTP 请求...');
      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
        },
        signal: controller.signal
      });

      clearTimeout(timeoutId);
      console.log(`[EdgeOneStorage] 📡 收到响应: ${response.status} ${response.statusText}`);

      // 检查 HTTP 响应状态码是否为 200
      if (!response.ok) {
        console.warn(`[EdgeOneStorage] ❌ Functions 响应异常: ${response.status} ${response.statusText}`);
        console.warn(`[EdgeOneStorage] ❌ 响应头:`, Object.fromEntries(response.headers.entries()));
        return false;
      }

      // 验证响应是否为有效的 JSON 格式
      console.log('[EdgeOneStorage] 🔍 解析 JSON 响应...');
      const data = await response.json();
      console.log('[EdgeOneStorage] 📄 响应数据:', data);

      // 检查 JSON 对象是否包含预期的 API 字段
      const hasExpectedFields = typeof data === 'object' && data !== null &&
          ('success' in data || 'message' in data || 'found' in data);

      console.log(`[EdgeOneStorage] 🔍 包含预期字段: ${hasExpectedFields}`);
      console.log(`[EdgeOneStorage] 🔍 字段检查: success=${('success' in data)}, message=${('message' in data)}, found=${('found' in data)}`);

      if (hasExpectedFields) {
        console.log('[EdgeOneStorage] ✅ Functions 已部署并生效');
        return true;
      } else {
        console.warn('[EdgeOneStorage] ⚠️ Functions 响应格式不符合预期:', data);
        return false;
      }

    } catch (error) {
      // 详细的错误处理和日志
      console.error('[EdgeOneStorage] ❌ Functions 检测异常:', error);

      if (error.name === 'AbortError') {
        console.warn('[EdgeOneStorage] ⏰ Functions 检测超时（5秒）');
      } else if (error.message.includes('fetch')) {
        console.warn('[EdgeOneStorage] 🌐 网络请求失败:', error.message);
      } else if (error instanceof SyntaxError) {
        console.warn('[EdgeOneStorage] 📄 JSON 解析失败:', error.message);
      } else {
        console.warn('[EdgeOneStorage] ❓ 未知错误:', error.message);
      }

      return false;
    }
  }

  /**
   * 检查远程存储服务可用性（基于 Functions 部署检测）
   * @param {boolean} forceRecheck - 是否强制重新检测
   * @returns {Promise<boolean>} 是否可用
   */
  async checkAvailability(forceRecheck = false) {
    // 如果已经检测过且不强制重新检测，直接返回结果
    if (this.isAvailable !== null && !forceRecheck) {
      return this.isAvailable;
    }

    try {
      console.log('[EdgeOneStorage] 开始可用性检测...');

      // 检测 Functions 是否部署并生效
      const functionsDeployed = await this._checkFunctionsDeployment();
      this.isAvailable = functionsDeployed;

      console.log(`[EdgeOneStorage] 可用性检测结果: ${this.isAvailable ? '✅ 可用' : '❌ 不可用'}`);
      return this.isAvailable;

    } catch (error) {
      console.error('[EdgeOneStorage] 可用性检测失败:', error);
      this.isAvailable = false;
      return false;
    }
  }

  /**
   * 本地存储适配器 - 存储数据
   * @param {string} key - 键名
   * @param {any} value - 值
   * @private
   */
  _localStoragePut(key, value) {
    try {
      const stringValue = typeof value === 'string' ? value : JSON.stringify(value);
      localStorage.setItem(`edgeone_storage_${key}`, stringValue);
      return {
        success: true,
        message: '数据存储成功（本地存储）',
        key: key,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`本地存储失败: ${error.message}`);
    }
  }

  /**
   * 本地存储适配器 - 获取数据
   * @param {string} key - 键名
   * @returns {any} 存储的值
   * @private
   */
  _localStorageGet(key) {
    try {
      const value = localStorage.getItem(`edgeone_storage_${key}`);
      if (value === null) {
        return null;
      }

      // 尝试解析 JSON
      try {
        return JSON.parse(value);
      } catch (e) {
        return value;
      }
    } catch (error) {
      console.error(`[EdgeOneStorage] 本地存储获取失败 (key: ${key}):`, error);
      return null;
    }
  }

  /**
   * 本地存储适配器 - 删除数据
   * @param {string} key - 键名
   * @private
   */
  _localStorageDelete(key) {
    try {
      const existed = localStorage.getItem(`edgeone_storage_${key}`) !== null;
      localStorage.removeItem(`edgeone_storage_${key}`);
      return {
        success: true,
        message: existed ? '数据删除成功（本地存储）' : '键不存在（本地存储）',
        key: key,
        deleted: existed,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`本地存储删除失败: ${error.message}`);
    }
  }

  /**
   * 本地存储适配器 - 列出所有键
   * @param {Object} options - 查询选项
   * @returns {Object} 键列表
   * @private
   */
  _localStorageList(options = {}) {
    try {
      const prefix = options.prefix || '';
      const limit = options.limit || 100;
      const storagePrefix = 'edgeone_storage_';

      const keys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && key.startsWith(storagePrefix)) {
          const actualKey = key.substring(storagePrefix.length);
          if (!prefix || actualKey.startsWith(prefix)) {
            keys.push(actualKey);
          }
        }
      }

      // 应用限制
      const limitedKeys = keys.slice(0, limit);

      return {
        success: true,
        message: `成功获取键列表（本地存储），共找到 ${limitedKeys.length} 个键`,
        keys: limitedKeys,
        count: limitedKeys.length,
        hasMore: keys.length > limit,
        cursor: null,
        filter: {
          prefix: prefix || null,
          limit: limit
        },
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      throw new Error(`本地存储列表获取失败: ${error.message}`);
    }
  }

  /**
   * 发送 HTTP 请求的通用方法（不使用降级机制）
   * @param {string} endpoint - API 端点
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   * @private
   */
  async _requestWithoutFallback(endpoint, options = {}) {
    // 构建请求 URL
    let url;
    if (this.useRelativePath) {
      // 使用相对路径调用本地端点
      url = `/storage/${endpoint}`;
    } else {
      // 使用完整 URL 调用远程端点
      url = `${this.baseUrl}/storage/${endpoint}`;
    }

    this.requestCount++;

    // 设置默认请求选项
    const requestOptions = {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        ...options.headers
      },
      ...options
    };

    // 添加请求超时
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.options.timeout);
    requestOptions.signal = controller.signal;

    let lastError;
    
    // 重试机制
    for (let attempt = 0; attempt <= this.options.retryCount; attempt++) {
      try {
        console.log(`[EdgeOneStorage] 发送请求 #${this.requestCount} (尝试 ${attempt + 1}/${this.options.retryCount + 1}): ${url}`);
        
        const response = await fetch(url, requestOptions);
        clearTimeout(timeoutId);

        // 检查响应状态
        if (!response.ok) {
          throw new Error(`HTTP 错误: ${response.status} ${response.statusText}`);
        }

        // 解析 JSON 响应
        const data = await response.json();
        
        // 检查业务逻辑错误
        if (!data.success) {
          throw new Error(data.message || '操作失败');
        }

        console.log(`[EdgeOneStorage] 请求成功: ${data.message}`);
        return data;

      } catch (error) {
        lastError = error;
        console.error(`[EdgeOneStorage] 请求失败 (尝试 ${attempt + 1}): ${error.message}`);

        // 如果是最后一次尝试，直接抛出错误
        if (attempt === this.options.retryCount) {
          break;
        }

        // 等待重试延迟
        await new Promise(resolve => setTimeout(resolve, this.options.retryDelay));
      }
    }

    clearTimeout(timeoutId);
    throw new Error(`请求失败，已重试 ${this.options.retryCount} 次: ${lastError.message}`);
  }

  /**
   * 发送 HTTP 请求的通用方法（支持降级机制）
   * @param {string} endpoint - API 端点
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   */
  async _request(endpoint, options = {}) {
    // 如果启用降级机制，确保初始化检测已完成
    if (this.options.enableFallback) {
      // 如果初始化还在进行中，等待完成
      if (this.initializationPromise) {
        console.log('[EdgeOneStorage] 等待初始化检测完成...');
        try {
          await this.initializationPromise;
          this.initializationPromise = null; // 清除 Promise，避免重复等待
        } catch (error) {
          console.warn('[EdgeOneStorage] 初始化检测失败:', error.message);
        }
      }

      // 如果仍未确定存储模式，进行同步检测
      if (this.isAvailable === null) {
        console.log('[EdgeOneStorage] 首次使用，立即检测 Functions 部署状态...');
        try {
          await this._initializeStorage();
        } catch (error) {
          console.warn('[EdgeOneStorage] 同步检测失败，使用本地存储:', error.message);
          this.storageMode = 'local';
          this.isAvailable = false;
        }
      }
    }

    // 如果当前是本地存储模式，直接使用本地存储
    if (this.storageMode === 'local') {
      return this._handleLocalStorageRequest(endpoint, options);
    }

    try {
      // 尝试使用远程存储
      return await this._requestWithoutFallback(endpoint, options);
    } catch (error) {
      // 如果启用降级机制且远程请求失败，切换到本地存储
      if (this.options.enableFallback && this._isNetworkError(error)) {
        console.warn(`[EdgeOneStorage] 远程请求失败，自动切换到本地存储: ${error.message}`);
        this.storageMode = 'local';
        this.isAvailable = false;
        return this._handleLocalStorageRequest(endpoint, options);
      }

      // 否则直接抛出错误
      throw error;
    }
  }

  /**
   * 判断是否为网络错误
   * @param {Error} error - 错误对象
   * @returns {boolean} 是否为网络错误
   * @private
   */
  _isNetworkError(error) {
    const networkErrorMessages = [
      'fetch',
      'network',
      'timeout',
      'abort',
      'connection',
      'ECONNREFUSED',
      'ENOTFOUND',
      'ETIMEDOUT'
    ];

    const errorMessage = error.message.toLowerCase();
    return networkErrorMessages.some(msg => errorMessage.includes(msg));
  }

  /**
   * 处理本地存储请求
   * @param {string} endpoint - API 端点
   * @param {Object} options - 请求选项
   * @returns {Promise<Object>} 响应数据
   * @private
   */
  async _handleLocalStorageRequest(endpoint, options = {}) {
    try {
      // 解析端点和参数
      const [operation, queryString] = endpoint.split('?');
      const params = new URLSearchParams(queryString || '');

      switch (operation) {
        case 'put':
          if (options.body) {
            const { key, value } = JSON.parse(options.body);
            return this._localStoragePut(key, value);
          }
          throw new Error('PUT 请求缺少请求体');

        case 'get':
          const getKey = params.get('key');
          if (!getKey) throw new Error('GET 请求缺少 key 参数');
          const getValue = this._localStorageGet(getKey);
          return {
            success: true,
            message: getValue !== null ? '数据获取成功（本地存储）' : '未找到指定键的数据（本地存储）',
            key: getKey,
            value: getValue,
            found: getValue !== null,
            timestamp: new Date().toISOString()
          };

        case 'delete':
          const deleteKey = params.get('key');
          if (!deleteKey) throw new Error('DELETE 请求缺少 key 参数');
          return this._localStorageDelete(deleteKey);

        case 'list':
          const listOptions = {
            prefix: params.get('prefix') || '',
            limit: parseInt(params.get('limit')) || 100
          };
          return this._localStorageList(listOptions);

        default:
          throw new Error(`不支持的本地存储操作: ${operation}`);
      }
    } catch (error) {
      throw new Error(`本地存储操作失败: ${error.message}`);
    }
  }

  /**
   * 存储键值对数据
   * @param {string} key - 键名
   * @param {any} value - 值（会被序列化为 JSON）
   * @returns {Promise<Object>} 操作结果
   */
  async put(key, value) {
    if (!key || typeof key !== 'string') {
      throw new Error('键名必须是非空字符串');
    }

    if (value === undefined) {
      throw new Error('值不能为 undefined');
    }

    try {
      const result = await this._request('put', {
        method: 'POST',
        body: JSON.stringify({ key, value })
      });

      // 更新本地缓存
      if (this.options.enableCache) {
        this.cache.set(key, {
          value: value,
          timestamp: Date.now()
        });
      }

      return result;
    } catch (error) {
      console.error(`[EdgeOneStorage] 存储数据失败 (key: ${key}):`, error);
      throw error;
    }
  }

  /**
   * 根据键获取值
   * @param {string} key - 键名
   * @returns {Promise<any>} 存储的值，如果不存在则返回 null
   */
  async get(key) {
    if (!key || typeof key !== 'string') {
      throw new Error('键名必须是非空字符串');
    }

    // 检查本地缓存
    if (this.options.enableCache) {
      const cached = this.cache.get(key);
      if (cached && (Date.now() - cached.timestamp) < this.options.cacheTimeout) {
        console.log(`[EdgeOneStorage] 从缓存获取数据 (key: ${key})`);
        return cached.value;
      }
    }

    try {
      const result = await this._request(`get?key=${encodeURIComponent(key)}`);
      
      // 更新本地缓存
      if (this.options.enableCache && result.found) {
        this.cache.set(key, {
          value: result.value,
          timestamp: Date.now()
        });
      }

      return result.found ? result.value : null;
    } catch (error) {
      console.error(`[EdgeOneStorage] 获取数据失败 (key: ${key}):`, error);
      throw error;
    }
  }

  /**
   * 删除指定键的数据
   * @param {string} key - 键名
   * @returns {Promise<Object>} 操作结果
   */
  async delete(key) {
    if (!key || typeof key !== 'string') {
      throw new Error('键名必须是非空字符串');
    }

    try {
      const result = await this._request(`delete?key=${encodeURIComponent(key)}`, {
        method: 'DELETE'
      });

      // 从本地缓存中移除
      if (this.options.enableCache) {
        this.cache.delete(key);
      }

      return result;
    } catch (error) {
      console.error(`[EdgeOneStorage] 删除数据失败 (key: ${key}):`, error);
      throw error;
    }
  }

  /**
   * 列出所有存储的键
   * @param {Object} options - 查询选项
   * @param {string} options.prefix - 键名前缀过滤
   * @param {number} options.limit - 返回结果数量限制
   * @param {string} options.cursor - 分页游标
   * @returns {Promise<Object>} 键列表和分页信息
   */
  async list(options = {}) {
    try {
      // 构建查询参数
      const params = new URLSearchParams();

      if (options.prefix) {
        params.append('prefix', options.prefix);
      }

      if (options.limit) {
        params.append('limit', options.limit.toString());
      }

      if (options.cursor) {
        params.append('cursor', options.cursor);
      }

      const queryString = params.toString();
      const endpoint = queryString ? `list?${queryString}` : 'list';

      const result = await this._request(endpoint);
      return result;
    } catch (error) {
      console.error('[EdgeOneStorage] 获取键列表失败:', error);
      throw error;
    }
  }

  /**
   * 批量存储数据
   * @param {Object} data - 键值对对象
   * @returns {Promise<Array>} 操作结果数组
   */
  async putBatch(data) {
    if (!data || typeof data !== 'object') {
      throw new Error('数据必须是对象格式');
    }

    const results = [];
    const keys = Object.keys(data);

    console.log(`[EdgeOneStorage] 开始批量存储 ${keys.length} 个键值对`);

    for (const key of keys) {
      try {
        const result = await this.put(key, data[key]);
        results.push({ key, success: true, result });
      } catch (error) {
        console.error(`[EdgeOneStorage] 批量存储失败 (key: ${key}):`, error);
        results.push({ key, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`[EdgeOneStorage] 批量存储完成: ${successCount}/${keys.length} 成功`);

    return results;
  }

  /**
   * 批量获取数据
   * @param {Array<string>} keys - 键名数组
   * @returns {Promise<Object>} 键值对对象
   */
  async getBatch(keys) {
    if (!Array.isArray(keys)) {
      throw new Error('键名必须是数组格式');
    }

    const results = {};
    console.log(`[EdgeOneStorage] 开始批量获取 ${keys.length} 个键的数据`);

    for (const key of keys) {
      try {
        const value = await this.get(key);
        results[key] = value;
      } catch (error) {
        console.error(`[EdgeOneStorage] 批量获取失败 (key: ${key}):`, error);
        results[key] = null;
      }
    }

    console.log(`[EdgeOneStorage] 批量获取完成`);
    return results;
  }

  /**
   * 批量删除数据
   * @param {Array<string>} keys - 键名数组
   * @returns {Promise<Array>} 操作结果数组
   */
  async deleteBatch(keys) {
    if (!Array.isArray(keys)) {
      throw new Error('键名必须是数组格式');
    }

    const results = [];
    console.log(`[EdgeOneStorage] 开始批量删除 ${keys.length} 个键`);

    for (const key of keys) {
      try {
        const result = await this.delete(key);
        results.push({ key, success: true, result });
      } catch (error) {
        console.error(`[EdgeOneStorage] 批量删除失败 (key: ${key}):`, error);
        results.push({ key, success: false, error: error.message });
      }
    }

    const successCount = results.filter(r => r.success).length;
    console.log(`[EdgeOneStorage] 批量删除完成: ${successCount}/${keys.length} 成功`);

    return results;
  }

  /**
   * 检查键是否存在
   * @param {string} key - 键名
   * @returns {Promise<boolean>} 是否存在
   */
  async exists(key) {
    try {
      const value = await this.get(key);
      return value !== null;
    } catch (error) {
      console.error(`[EdgeOneStorage] 检查键存在性失败 (key: ${key}):`, error);
      return false;
    }
  }

  /**
   * 清空本地缓存
   */
  clearCache() {
    if (this.options.enableCache) {
      this.cache.clear();
      console.log('[EdgeOneStorage] 本地缓存已清空');
    }
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      requestCount: this.requestCount,
      cacheSize: this.options.enableCache ? this.cache.size : 0,
      cacheEnabled: this.options.enableCache,
      baseUrl: this.baseUrl,
      storageMode: this.storageMode,
      isAvailable: this.isAvailable,
      fallbackEnabled: this.options.enableFallback,
      useRelativePath: this.useRelativePath || false,
      options: { ...this.options }
    };
  }

  /**
   * 强制切换存储模式
   * @param {string} mode - 存储模式 ('remote' | 'local')
   */
  setStorageMode(mode) {
    if (mode !== 'remote' && mode !== 'local') {
      throw new Error('存储模式必须是 "remote" 或 "local"');
    }

    this.storageMode = mode;
    console.log(`[EdgeOneStorage] 存储模式已切换到: ${mode}`);
  }

  /**
   * 重新检测服务可用性
   * @returns {Promise<boolean>} 是否可用
   */
  async recheckAvailability() {
    console.log('[EdgeOneStorage] 🔄 开始重新检测服务可用性...');
    this.isAvailable = null;
    const available = await this.checkAvailability(true); // 强制重新检测

    if (available && this.storageMode === 'local') {
      console.log('[EdgeOneStorage] ✅ 远程服务已恢复，切换回远程存储模式');
      this.storageMode = 'remote';
    } else if (!available && this.storageMode === 'remote') {
      console.log('[EdgeOneStorage] ❌ 远程服务不可用，切换到本地存储模式');
      this.storageMode = 'local';
    }

    return available;
  }

  /**
   * 检测 Cloudflare Functions 部署状态
   * @returns {Promise<boolean>} Functions 是否已部署并生效
   */
  async checkFunctionsDeployment() {
    console.log('[EdgeOneStorage] 🔍 检测 Cloudflare Functions 部署状态...');
    return await this._checkFunctionsDeployment();
  }

  /**
   * 强制触发初始化检测（用于调试和测试）
   * @returns {Promise<boolean>} 初始化是否成功
   */
  async forceInitialization() {
    console.log('[EdgeOneStorage] 🔧 强制触发初始化检测...');

    // 重置状态
    this.isAvailable = null;
    this.initializationPromise = null;

    // 如果启用降级机制，执行初始化
    if (this.options.enableFallback) {
      this.initializationPromise = this._performInitialization();
      return await this.initializationPromise;
    } else {
      console.warn('[EdgeOneStorage] 降级功能未启用，无法执行初始化检测');
      return false;
    }
  }

  /**
   * 获取当前存储状态信息
   * @returns {Object} 存储状态详情
   */
  getStorageStatus() {
    return {
      storageMode: this.storageMode,
      isAvailable: this.isAvailable,
      functionsDeployed: this.isAvailable !== false, // 如果不是明确的 false，说明可能已部署
      useRelativePath: this.useRelativePath || false,
      baseUrl: this.baseUrl,
      fallbackEnabled: this.options.enableFallback,
      initializationPending: this.initializationPromise !== null,
      lastCheck: this.isAvailable !== null ? new Date().toISOString() : null
    };
  }

  /**
   * 调试方法：强制触发初始化检测并输出详细日志
   * @returns {Promise<Object>} 检测结果和详细信息
   */
  async debugInitialization() {
    console.log('[EdgeOneStorage] 🔧 开始调试初始化流程...');

    const debugInfo = {
      beforeCheck: {
        storageMode: this.storageMode,
        isAvailable: this.isAvailable,
        initializationPending: this.initializationPromise !== null,
        enableFallback: this.options.enableFallback,
        useRelativePath: this.useRelativePath,
        baseUrl: this.baseUrl
      },
      checkResult: null,
      afterCheck: null,
      errors: []
    };

    try {
      // 重置状态
      this.isAvailable = null;
      this.initializationPromise = null;

      console.log('[EdgeOneStorage] 🔧 重置状态，开始检测...');

      // 执行检测
      await this._initializeStorage();

      debugInfo.checkResult = {
        success: true,
        functionsAvailable: this.isAvailable
      };

    } catch (error) {
      console.error('[EdgeOneStorage] 🔧 检测过程中发生错误:', error);
      debugInfo.errors.push(error.message);
      debugInfo.checkResult = {
        success: false,
        error: error.message
      };
    }

    debugInfo.afterCheck = {
      storageMode: this.storageMode,
      isAvailable: this.isAvailable,
      initializationPending: this.initializationPromise !== null
    };

    console.log('[EdgeOneStorage] 🔧 调试信息:', debugInfo);
    return debugInfo;
  }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
  // Node.js 环境
  module.exports = EdgeOneStorageImpl;
} else {
  // 浏览器环境
  window.EdgeOneStorageImpl = EdgeOneStorageImpl;
}
