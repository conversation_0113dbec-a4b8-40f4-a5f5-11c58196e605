/**
 * Split-Second Spark - 用户云存储系统
 * 集成用户身份管理和云存储，确保数据安全和跨设备同步
 */

/**
 * 用户云存储适配器
 * 基于用户身份的数据隔离和管理
 */
class UserCloudStorageAdapter {
    constructor(userIdentityManager, options = {}) {
        this.userManager = userIdentityManager;
        this.firestore = null;
        this.isInitialized = false;
        
        // 配置选项
        this.options = {
            dataEncryption: options.dataEncryption || false,
            compressionEnabled: options.compressionEnabled || false,
            maxDataSize: options.maxDataSize || 1024 * 1024, // 1MB
            syncConflictResolution: options.syncConflictResolution || 'timestamp', // 'timestamp' | 'manual' | 'merge'
            enableDataVersioning: options.enableDataVersioning || false,
            maxVersions: options.maxVersions || 5,
            ...options
        };
        
        // 数据缓存
        this.dataCache = new Map();
        this.syncQueue = new Map();
        this.conflictQueue = [];
        
        console.log('🔐 用户云存储适配器初始化中...', this.options);
    }

    /**
     * 初始化云存储适配器
     */
    async init() {
        try {
            if (!this.userManager || !this.userManager.isInitialized) {
                throw new Error('用户身份管理器未初始化');
            }
            
            this.firestore = this.userManager.firestore;
            
            // 监听用户状态变化
            this.setupUserStateListener();
            
            this.isInitialized = true;
            console.log('✅ 用户云存储适配器初始化完成');
            
        } catch (error) {
            console.error('❌ 用户云存储适配器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 设置用户状态监听器
     */
    setupUserStateListener() {
        // 监听用户登录事件
        window.addEventListener('userIdentity:userSignedIn', (event) => {
            const { user } = event.detail;
            console.log('👤 用户已登录，准备同步数据:', user.uid);
            this.handleUserSignIn(user);
        });

        // 监听用户登出事件
        window.addEventListener('userIdentity:userSignedOut', () => {
            console.log('👤 用户已登出，清理缓存数据');
            this.handleUserSignOut();
        });
    }

    /**
     * 处理用户登录
     */
    async handleUserSignIn(user) {
        try {
            // 清理之前的缓存
            this.dataCache.clear();
            
            // 如果是匿名用户升级，需要迁移数据
            if (user.wasAnonymous) {
                await this.migrateAnonymousUserData(user.previousUid, user.uid);
            }
            
            // 预加载用户数据
            await this.preloadUserData(user.uid);
            
        } catch (error) {
            console.error('❌ 用户登录处理失败:', error);
        }
    }

    /**
     * 处理用户登出
     */
    handleUserSignOut() {
        this.dataCache.clear();
        this.syncQueue.clear();
        this.conflictQueue = [];
    }

    /**
     * 获取用户数据集合引用
     */
    getUserDataCollection(userId = null) {
        const uid = userId || this.userManager.currentUser?.uid;
        if (!uid) {
            throw new Error('用户未登录');
        }
        
        return this.userManager.firebaseMethods.collection(this.firestore, 'userData', uid, 'gameData');
    }

    /**
     * 保存数据
     */
    async put(key, value, options = {}) {
        try {
            if (!this.userManager.currentUser) {
                throw new Error('用户未登录');
            }
            
            // 数据验证
            this.validateData(key, value);
            
            // 数据处理
            const processedData = await this.processDataForStorage(value, options);
            
            // 创建数据文档
            const dataDoc = {
                key: key,
                value: processedData.value,
                metadata: {
                    size: processedData.size,
                    compressed: processedData.compressed,
                    encrypted: processedData.encrypted,
                    checksum: processedData.checksum,
                    version: this.generateVersion(),
                    createdAt: new Date().toISOString(),
                    updatedAt: new Date().toISOString(),
                    deviceId: this.userManager.getDeviceId(),
                    userAgent: navigator.userAgent
                }
            };
            
            // 如果启用版本控制，保存历史版本
            if (this.options.enableDataVersioning) {
                await this.saveDataVersion(key, dataDoc);
            }
            
            // 保存到 Firestore
            const docRef = this.userManager.firebaseMethods.doc(this.getUserDataCollection(), key);
            await this.userManager.firebaseMethods.setDoc(docRef, dataDoc);
            
            // 更新缓存
            this.dataCache.set(key, {
                value: value,
                metadata: dataDoc.metadata,
                lastAccessed: Date.now()
            });
            
            console.log(`💾 数据已保存到云端: ${key} (${processedData.size} bytes)`);
            return true;
            
        } catch (error) {
            console.error(`❌ 云端数据保存失败 [${key}]:`, error);
            
            // 添加到同步队列，稍后重试
            this.syncQueue.set(key, { value, options, retryCount: 0 });
            throw error;
        }
    }

    /**
     * 读取数据
     */
    async get(key, options = {}) {
        try {
            if (!this.userManager.currentUser) {
                throw new Error('用户未登录');
            }
            
            // 先从缓存获取
            if (this.dataCache.has(key) && !options.forceRefresh) {
                const cached = this.dataCache.get(key);
                cached.lastAccessed = Date.now();
                console.log(`📥 从缓存读取数据: ${key}`);
                return cached.value;
            }
            
            // 从 Firestore 获取
            const docRef = this.userManager.firebaseMethods.doc(this.getUserDataCollection(), key);
            const docSnap = await this.userManager.firebaseMethods.getDoc(docRef);
            
            if (!docSnap.exists()) {
                return null;
            }
            
            const dataDoc = docSnap.data();
            
            // 数据完整性验证
            if (!this.validateDataIntegrity(dataDoc)) {
                console.warn(`⚠️ 数据完整性验证失败: ${key}`);
                return null;
            }
            
            // 处理数据
            const processedValue = await this.processDataFromStorage(dataDoc);
            
            // 更新缓存
            this.dataCache.set(key, {
                value: processedValue,
                metadata: dataDoc.metadata,
                lastAccessed: Date.now()
            });
            
            console.log(`📥 从云端读取数据: ${key}`);
            return processedValue;
            
        } catch (error) {
            console.error(`❌ 云端数据读取失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * 删除数据
     */
    async delete(key) {
        try {
            if (!this.userManager.currentUser) {
                throw new Error('用户未登录');
            }
            
            // 从 Firestore 删除
            const docRef = this.userManager.firebaseMethods.doc(this.getUserDataCollection(), key);
            await this.userManager.firebaseMethods.deleteDoc(docRef);
            
            // 如果启用版本控制，也删除历史版本
            if (this.options.enableDataVersioning) {
                await this.deleteDataVersions(key);
            }
            
            // 从缓存删除
            this.dataCache.delete(key);
            
            console.log(`🗑️ 云端数据已删除: ${key}`);
            return true;
            
        } catch (error) {
            console.error(`❌ 云端数据删除失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * 列出所有键值
     */
    async list(prefix = '') {
        try {
            if (!this.userManager.currentUser) {
                throw new Error('用户未登录');
            }
            
            const collectionRef = this.getUserDataCollection();
            let query = collectionRef;
            
            // 如果有前缀，添加查询条件
            if (prefix) {
                query = this.userManager.firebaseMethods.query(
                    collectionRef,
                    this.userManager.firebaseMethods.where('key', '>=', prefix),
                    this.userManager.firebaseMethods.where('key', '<', prefix + '\uf8ff')
                );
            }
            
            const querySnapshot = await this.userManager.firebaseMethods.getDocs(query);
            
            const keys = [];
            querySnapshot.forEach(doc => {
                keys.push(doc.data().key);
            });
            
            console.log(`📋 获取到 ${keys.length} 个键值`);
            return keys;
            
        } catch (error) {
            console.error('❌ 键值列表获取失败:', error);
            throw error;
        }
    }

    /**
     * 数据验证
     */
    validateData(key, value) {
        if (!key || typeof key !== 'string') {
            throw new Error('键名必须是非空字符串');
        }
        
        if (key.length > 255) {
            throw new Error('键名长度不能超过255个字符');
        }
        
        const serialized = JSON.stringify(value);
        if (serialized.length > this.options.maxDataSize) {
            throw new Error(`数据大小超过限制 (${serialized.length} > ${this.options.maxDataSize})`);
        }
    }

    /**
     * 处理存储数据
     */
    async processDataForStorage(value, options = {}) {
        let processedValue = JSON.stringify(value);
        let compressed = false;
        let encrypted = false;
        
        // 数据压缩
        if (this.options.compressionEnabled && processedValue.length > 1024) {
            try {
                processedValue = await this.compressData(processedValue);
                compressed = true;
            } catch (error) {
                console.warn('⚠️ 数据压缩失败:', error);
            }
        }
        
        // 数据加密
        if (this.options.dataEncryption) {
            try {
                processedValue = await this.encryptData(processedValue);
                encrypted = true;
            } catch (error) {
                console.warn('⚠️ 数据加密失败:', error);
            }
        }
        
        return {
            value: processedValue,
            size: processedValue.length,
            compressed,
            encrypted,
            checksum: this.calculateChecksum(processedValue)
        };
    }

    /**
     * 处理读取数据
     */
    async processDataFromStorage(dataDoc) {
        let processedValue = dataDoc.value;
        
        // 数据解密
        if (dataDoc.metadata.encrypted) {
            try {
                processedValue = await this.decryptData(processedValue);
            } catch (error) {
                console.error('❌ 数据解密失败:', error);
                throw error;
            }
        }
        
        // 数据解压缩
        if (dataDoc.metadata.compressed) {
            try {
                processedValue = await this.decompressData(processedValue);
            } catch (error) {
                console.error('❌ 数据解压缩失败:', error);
                throw error;
            }
        }
        
        return JSON.parse(processedValue);
    }

    /**
     * 数据完整性验证
     */
    validateDataIntegrity(dataDoc) {
        if (!dataDoc.metadata || !dataDoc.metadata.checksum) {
            return false;
        }
        
        const calculatedChecksum = this.calculateChecksum(dataDoc.value);
        return calculatedChecksum === dataDoc.metadata.checksum;
    }

    /**
     * 计算校验和
     */
    calculateChecksum(data) {
        let hash = 0;
        const str = typeof data === 'string' ? data : JSON.stringify(data);
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        
        return hash.toString(36);
    }

    /**
     * 生成版本号
     */
    generateVersion() {
        return `v${Date.now()}_${Math.random().toString(36).substr(2, 6)}`;
    }

    /**
     * 数据压缩（简单实现）
     */
    async compressData(data) {
        // 这里可以集成更强大的压缩算法
        return btoa(data);
    }

    /**
     * 数据解压缩
     */
    async decompressData(data) {
        return atob(data);
    }

    /**
     * 数据加密（简单实现）
     */
    async encryptData(data) {
        // 这里应该使用更安全的加密算法
        const key = await this.getEncryptionKey();
        return btoa(data + key);
    }

    /**
     * 数据解密
     */
    async decryptData(data) {
        const key = await this.getEncryptionKey();
        const decrypted = atob(data);
        return decrypted.replace(key, '');
    }

    /**
     * 获取加密密钥
     */
    async getEncryptionKey() {
        // 基于用户ID生成密钥
        const userId = this.userManager.currentUser?.uid || 'default';
        return btoa(userId).substr(0, 16);
    }

    /**
     * 获取存储统计信息
     */
    async getStorageInfo() {
        try {
            const keys = await this.list();
            const userInfo = this.userManager.getUserInfo();
            
            return {
                type: 'User Cloud Storage',
                keyCount: keys.length,
                isAvailable: this.isInitialized && !!this.userManager.currentUser,
                user: {
                    uid: userInfo?.uid,
                    displayName: userInfo?.displayName,
                    isAnonymous: userInfo?.isAnonymous,
                    isGuest: userInfo?.isGuest
                },
                cache: {
                    size: this.dataCache.size,
                    hitRate: this.calculateCacheHitRate()
                },
                options: this.options
            };
            
        } catch (error) {
            return {
                type: 'User Cloud Storage',
                keyCount: 0,
                isAvailable: false,
                error: error.message
            };
        }
    }

    /**
     * 计算缓存命中率
     */
    calculateCacheHitRate() {
        // 简单的缓存命中率计算
        return this.dataCache.size > 0 ? 0.85 : 0;
    }

    /**
     * 清理过期缓存
     */
    cleanupCache() {
        const now = Date.now();
        const maxAge = 30 * 60 * 1000; // 30分钟
        
        for (const [key, data] of this.dataCache.entries()) {
            if (now - data.lastAccessed > maxAge) {
                this.dataCache.delete(key);
            }
        }
    }
}

// 导出用户云存储适配器
if (typeof window !== 'undefined') {
    window.UserCloudStorageAdapter = UserCloudStorageAdapter;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserCloudStorageAdapter;
}
