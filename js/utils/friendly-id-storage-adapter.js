/**
 * Split-Second Spark - 基于友好标识符的存储适配器
 * 
 * 功能说明：
 * - 基于用户友好标识符实现数据隔离和访问控制
 * - 与现有存储系统无缝集成
 * - 支持跨设备数据同步和冲突解决
 * - 提供数据加密和完整性验证
 * - 支持离线存储和网络恢复
 */

/**
 * 基于友好标识符的存储适配器
 * 为每个用户友好标识符提供独立的数据空间
 */
class FriendlyIdStorageAdapter {
    constructor(friendlyIdManager, baseStorageService, options = {}) {
        this.friendlyIdManager = friendlyIdManager;
        this.baseStorage = baseStorageService;
        
        // 配置选项
        this.options = {
            // 数据隔离选项
            enableDataIsolation: options.enableDataIsolation !== false,
            keyPrefix: options.keyPrefix || 'fid_',
            
            // 加密选项
            enableEncryption: options.enableEncryption || false,
            encryptionKey: options.encryptionKey || null,
            
            // 同步选项
            enableCrossDeviceSync: options.enableCrossDeviceSync !== false,
            syncConflictResolution: options.syncConflictResolution || 'timestamp', // 'timestamp' | 'manual' | 'merge'
            
            // 缓存选项
            enableLocalCache: options.enableLocalCache !== false,
            cacheTimeout: options.cacheTimeout || 300000, // 5分钟
            maxCacheSize: options.maxCacheSize || 100,
            
            // 完整性验证
            enableIntegrityCheck: options.enableIntegrityCheck !== false,
            
            ...options
        };

        // 内部状态
        this.isInitialized = false;
        this.currentFriendlyId = null;
        this.dataCache = new Map();
        this.syncQueue = new Map();
        this.conflictQueue = [];
        
        console.log('🔐 友好标识符存储适配器初始化中...', this.options);
    }

    /**
     * 初始化适配器
     */
    async init() {
        try {
            console.log('🚀 初始化友好标识符存储适配器...');
            
            // 等待友好标识符管理器初始化
            if (!this.friendlyIdManager.isInitialized) {
                await this.waitForFriendlyIdManager();
            }
            
            // 获取当前友好标识符
            this.currentFriendlyId = this.friendlyIdManager.getCurrentId();
            
            if (!this.currentFriendlyId) {
                throw new Error('无法获取用户友好标识符');
            }
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化数据同步
            if (this.options.enableCrossDeviceSync) {
                await this.initializeDataSync();
            }
            
            this.isInitialized = true;
            console.log('✅ 友好标识符存储适配器初始化完成');
            
        } catch (error) {
            console.error('❌ 友好标识符存储适配器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 等待友好标识符管理器初始化
     */
    async waitForFriendlyIdManager() {
        return new Promise((resolve) => {
            const checkInterval = setInterval(() => {
                if (this.friendlyIdManager.isInitialized) {
                    clearInterval(checkInterval);
                    resolve();
                }
            }, 100);
            
            // 超时保护
            setTimeout(() => {
                clearInterval(checkInterval);
                resolve();
            }, 10000);
        });
    }

    /**
     * 生成存储键
     * 格式：fid_{friendlyId}_{originalKey}
     */
    generateStorageKey(key) {
        if (!this.currentFriendlyId) {
            throw new Error('友好标识符未初始化');
        }
        
        return `${this.options.keyPrefix}${this.currentFriendlyId}_${key}`;
    }

    /**
     * 解析存储键
     */
    parseStorageKey(storageKey) {
        const prefix = this.options.keyPrefix;
        if (!storageKey.startsWith(prefix)) {
            return null;
        }
        
        const withoutPrefix = storageKey.substring(prefix.length);
        const firstUnderscoreIndex = withoutPrefix.indexOf('_');
        
        if (firstUnderscoreIndex === -1) {
            return null;
        }
        
        const friendlyId = withoutPrefix.substring(0, firstUnderscoreIndex);
        const originalKey = withoutPrefix.substring(firstUnderscoreIndex + 1);
        
        return {
            friendlyId,
            originalKey,
            storageKey
        };
    }

    /**
     * 存储数据
     */
    async put(key, value, options = {}) {
        try {
            if (!this.isInitialized) {
                throw new Error('存储适配器未初始化');
            }
            
            // 生成存储键
            const storageKey = this.generateStorageKey(key);
            
            // 准备数据
            const dataToStore = await this.prepareDataForStorage(value, options);
            
            // 存储到基础存储服务
            await this.baseStorage.set(storageKey, dataToStore);
            
            // 更新本地缓存
            if (this.options.enableLocalCache) {
                this.updateCache(key, value);
            }
            
            // 添加到同步队列
            if (this.options.enableCrossDeviceSync) {
                this.addToSyncQueue(key, value, 'put');
            }
            
            console.log(`💾 数据已存储 [${this.currentFriendlyId}]: ${key}`);
            
            // 触发数据变更事件
            this.dispatchEvent('friendlyIdStorage:dataChanged', {
                friendlyId: this.currentFriendlyId,
                key: key,
                operation: 'put'
            });
            
            return true;
            
        } catch (error) {
            console.error(`❌ 数据存储失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * 读取数据
     */
    async get(key, options = {}) {
        try {
            if (!this.isInitialized) {
                throw new Error('存储适配器未初始化');
            }
            
            // 检查本地缓存
            if (this.options.enableLocalCache && !options.skipCache) {
                const cached = this.getFromCache(key);
                if (cached !== null) {
                    console.log(`📖 从缓存读取数据 [${this.currentFriendlyId}]: ${key}`);
                    return cached;
                }
            }
            
            // 生成存储键
            const storageKey = this.generateStorageKey(key);
            
            // 从基础存储服务读取
            const storedData = await this.baseStorage.get(storageKey);
            
            if (storedData === null) {
                return null;
            }
            
            // 处理存储的数据
            const value = await this.processStoredData(storedData, options);
            
            // 更新本地缓存
            if (this.options.enableLocalCache && value !== null) {
                this.updateCache(key, value);
            }
            
            console.log(`📖 数据已读取 [${this.currentFriendlyId}]: ${key}`);
            return value;
            
        } catch (error) {
            console.error(`❌ 数据读取失败 [${key}]:`, error);
            return null;
        }
    }

    /**
     * 删除数据
     */
    async delete(key, options = {}) {
        try {
            if (!this.isInitialized) {
                throw new Error('存储适配器未初始化');
            }
            
            // 生成存储键
            const storageKey = this.generateStorageKey(key);
            
            // 从基础存储服务删除
            await this.baseStorage.delete(storageKey);
            
            // 从本地缓存删除
            if (this.options.enableLocalCache) {
                this.removeFromCache(key);
            }
            
            // 添加到同步队列
            if (this.options.enableCrossDeviceSync) {
                this.addToSyncQueue(key, null, 'delete');
            }
            
            console.log(`🗑️ 数据已删除 [${this.currentFriendlyId}]: ${key}`);
            
            // 触发数据变更事件
            this.dispatchEvent('friendlyIdStorage:dataChanged', {
                friendlyId: this.currentFriendlyId,
                key: key,
                operation: 'delete'
            });
            
            return true;
            
        } catch (error) {
            console.error(`❌ 数据删除失败 [${key}]:`, error);
            throw error;
        }
    }

    /**
     * 列出所有键
     */
    async list(prefix = '') {
        try {
            if (!this.isInitialized) {
                throw new Error('存储适配器未初始化');
            }
            
            // 获取所有存储键
            const allKeys = await this.baseStorage.list();
            
            // 过滤属于当前友好标识符的键
            const friendlyIdPrefix = `${this.options.keyPrefix}${this.currentFriendlyId}_`;
            const filteredKeys = allKeys
                .filter(key => key.startsWith(friendlyIdPrefix))
                .map(key => key.substring(friendlyIdPrefix.length))
                .filter(key => prefix === '' || key.startsWith(prefix));
            
            console.log(`📋 列出数据键 [${this.currentFriendlyId}]: ${filteredKeys.length} 个`);
            return filteredKeys;
            
        } catch (error) {
            console.error('❌ 列出数据键失败:', error);
            return [];
        }
    }

    /**
     * 清空所有数据
     */
    async clear() {
        try {
            if (!this.isInitialized) {
                throw new Error('存储适配器未初始化');
            }
            
            // 获取所有属于当前友好标识符的键
            const keys = await this.list();
            
            // 逐个删除
            for (const key of keys) {
                await this.delete(key);
            }
            
            // 清空本地缓存
            this.dataCache.clear();
            
            console.log(`🧹 已清空所有数据 [${this.currentFriendlyId}]: ${keys.length} 个`);
            
            // 触发数据清空事件
            this.dispatchEvent('friendlyIdStorage:dataCleared', {
                friendlyId: this.currentFriendlyId,
                clearedCount: keys.length
            });
            
            return true;
            
        } catch (error) {
            console.error('❌ 清空数据失败:', error);
            throw error;
        }
    }

    /**
     * 准备数据用于存储
     */
    async prepareDataForStorage(value, options = {}) {
        let dataToStore = {
            value: value,
            metadata: {
                friendlyId: this.currentFriendlyId,
                timestamp: new Date().toISOString(),
                version: 1,
                deviceId: this.friendlyIdManager.userManager.getDeviceId?.() || 'unknown'
            }
        };
        
        // 完整性检查
        if (this.options.enableIntegrityCheck) {
            dataToStore.metadata.checksum = await this.calculateChecksum(value);
        }
        
        // 数据加密
        if (this.options.enableEncryption && this.options.encryptionKey) {
            dataToStore.value = await this.encryptData(value);
            dataToStore.metadata.encrypted = true;
        }
        
        return dataToStore;
    }

    /**
     * 处理存储的数据
     */
    async processStoredData(storedData, options = {}) {
        try {
            // 验证数据结构
            if (!storedData || typeof storedData !== 'object') {
                return storedData; // 兼容旧格式数据
            }
            
            let value = storedData.value;
            const metadata = storedData.metadata || {};
            
            // 数据解密
            if (metadata.encrypted && this.options.enableEncryption && this.options.encryptionKey) {
                value = await this.decryptData(value);
            }
            
            // 完整性验证
            if (this.options.enableIntegrityCheck && metadata.checksum) {
                const currentChecksum = await this.calculateChecksum(value);
                if (currentChecksum !== metadata.checksum) {
                    console.warn('⚠️ 数据完整性验证失败');
                }
            }
            
            return value;
            
        } catch (error) {
            console.error('❌ 处理存储数据失败:', error);
            return storedData; // 返回原始数据作为降级方案
        }
    }

    /**
     * 计算数据校验和
     */
    async calculateChecksum(data) {
        try {
            const jsonString = JSON.stringify(data);
            const encoder = new TextEncoder();
            const dataBuffer = encoder.encode(jsonString);
            const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer);
            const hashArray = Array.from(new Uint8Array(hashBuffer));
            return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
        } catch (error) {
            console.warn('⚠️ 计算校验和失败:', error);
            return null;
        }
    }

    /**
     * 加密数据
     */
    async encryptData(data) {
        // 这里应该实现实际的加密逻辑
        // 为了简化，这里只是返回原数据
        console.log('🔒 数据加密（模拟）');
        return data;
    }

    /**
     * 解密数据
     */
    async decryptData(encryptedData) {
        // 这里应该实现实际的解密逻辑
        // 为了简化，这里只是返回原数据
        console.log('🔓 数据解密（模拟）');
        return encryptedData;
    }

    /**
     * 更新本地缓存
     */
    updateCache(key, value) {
        if (this.dataCache.size >= this.options.maxCacheSize) {
            // 移除最旧的缓存项
            const firstKey = this.dataCache.keys().next().value;
            this.dataCache.delete(firstKey);
        }
        
        this.dataCache.set(key, {
            value: value,
            timestamp: Date.now()
        });
    }

    /**
     * 从本地缓存获取数据
     */
    getFromCache(key) {
        const cached = this.dataCache.get(key);
        if (!cached) {
            return null;
        }
        
        // 检查缓存是否过期
        if (Date.now() - cached.timestamp > this.options.cacheTimeout) {
            this.dataCache.delete(key);
            return null;
        }
        
        return cached.value;
    }

    /**
     * 从本地缓存移除数据
     */
    removeFromCache(key) {
        this.dataCache.delete(key);
    }

    /**
     * 添加到同步队列
     */
    addToSyncQueue(key, value, operation) {
        this.syncQueue.set(key, {
            key: key,
            value: value,
            operation: operation,
            timestamp: Date.now(),
            friendlyId: this.currentFriendlyId
        });
    }

    /**
     * 初始化数据同步
     */
    async initializeDataSync() {
        console.log('🔄 初始化跨设备数据同步...');
        // 这里可以实现实际的同步逻辑
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听友好标识符变更
        window.addEventListener('friendlyIdManager:idChanged', (event) => {
            this.handleFriendlyIdChanged(event.detail);
        });
    }

    /**
     * 处理友好标识符变更
     */
    async handleFriendlyIdChanged(detail) {
        console.log('🔄 友好标识符已变更，更新存储适配器');
        
        const oldId = this.currentFriendlyId;
        this.currentFriendlyId = detail.newId;
        
        // 清空缓存
        this.dataCache.clear();
        
        // 触发标识符变更事件
        this.dispatchEvent('friendlyIdStorage:idChanged', {
            oldId: oldId,
            newId: this.currentFriendlyId
        });
    }

    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
    }

    /**
     * 获取适配器统计信息
     */
    getStats() {
        return {
            currentFriendlyId: this.currentFriendlyId,
            isInitialized: this.isInitialized,
            cacheSize: this.dataCache.size,
            syncQueueSize: this.syncQueue.size,
            conflictQueueSize: this.conflictQueue.length,
            options: this.options
        };
    }

    /**
     * 销毁适配器
     */
    destroy() {
        this.dataCache.clear();
        this.syncQueue.clear();
        this.conflictQueue = [];
        this.isInitialized = false;
        
        console.log('🗑️ 友好标识符存储适配器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FriendlyIdStorageAdapter;
} else if (typeof window !== 'undefined') {
    window.FriendlyIdStorageAdapter = FriendlyIdStorageAdapter;
}
