/**
 * Split-Second Spark - 用户友好标识符生成器
 * 
 * 功能说明：
 * - 生成易于记忆和输入的用户友好标识符
 * - 使用有意义的词汇组合 + 数字后缀
 * - 确保全局唯一性和一定的安全性
 * - 支持冲突检测和重新生成
 * - 跨设备访问和数据同步支持
 * 
 * 标识符格式：[形容词]-[名词]-[数字后缀]
 * 示例：swift-eagle-2024, bright-star-7891, clever-fox-3456
 */

/**
 * 用户友好标识符生成器
 * 生成格式：[形容词]-[名词]-[数字后缀]
 */
class FriendlyUserIdGenerator {
    constructor(options = {}) {
        // 配置选项
        this.options = {
            // 标识符长度控制
            minLength: options.minLength || 12,
            maxLength: options.maxLength || 20,
            
            // 数字后缀范围
            numberMin: options.numberMin || 1000,
            numberMax: options.numberMax || 9999,
            
            // 重试次数
            maxRetries: options.maxRetries || 10,
            
            // 是否启用安全增强
            enableSecurityEnhancement: options.enableSecurityEnhancement !== false,
            
            // 自定义词汇表
            customAdjectives: options.customAdjectives || [],
            customNouns: options.customNouns || [],
            
            ...options
        };

        // 初始化词汇表
        this.initializeWordLists();
        
        console.log('🎯 用户友好标识符生成器初始化完成', this.options);
    }

    /**
     * 初始化词汇表
     * 选择易于记忆、输入和理解的词汇
     */
    initializeWordLists() {
        // 积极正面的形容词（易记、易输入）
        this.adjectives = [
            // 速度相关
            'swift', 'quick', 'fast', 'rapid', 'speedy',
            // 光亮相关
            'bright', 'shiny', 'golden', 'silver', 'crystal',
            // 智慧相关
            'smart', 'clever', 'wise', 'sharp', 'keen',
            // 力量相关
            'strong', 'mighty', 'bold', 'brave', 'fierce',
            // 美好相关
            'happy', 'lucky', 'magic', 'noble', 'royal',
            // 自然相关
            'fresh', 'pure', 'wild', 'free', 'calm',
            // 特殊相关
            'rare', 'unique', 'epic', 'super', 'ultra'
        ];

        // 易于理解的名词（动物、自然、物品）
        this.nouns = [
            // 动物类
            'eagle', 'tiger', 'lion', 'wolf', 'bear',
            'fox', 'hawk', 'shark', 'dragon', 'phoenix',
            'falcon', 'panther', 'leopard', 'jaguar', 'lynx',
            // 自然类
            'star', 'moon', 'sun', 'ocean', 'river',
            'mountain', 'forest', 'storm', 'thunder', 'lightning',
            'flame', 'crystal', 'diamond', 'ruby', 'emerald',
            // 物品类
            'sword', 'shield', 'crown', 'gem', 'key',
            'arrow', 'blade', 'hammer', 'staff', 'orb',
            // 抽象类
            'spirit', 'soul', 'dream', 'hope', 'quest',
            'legend', 'myth', 'saga', 'tale', 'story'
        ];

        // 合并自定义词汇
        if (this.options.customAdjectives.length > 0) {
            this.adjectives = [...this.adjectives, ...this.options.customAdjectives];
        }
        
        if (this.options.customNouns.length > 0) {
            this.nouns = [...this.nouns, ...this.options.customNouns];
        }

        // 去重并排序
        this.adjectives = [...new Set(this.adjectives)].sort();
        this.nouns = [...new Set(this.nouns)].sort();

        console.log(`📚 词汇表初始化完成: ${this.adjectives.length} 个形容词, ${this.nouns.length} 个名词`);
    }

    /**
     * 生成用户友好标识符
     * @param {Object} options - 生成选项
     * @returns {Promise<string>} 生成的标识符
     */
    async generateId(options = {}) {
        const generateOptions = {
            ...this.options,
            ...options
        };

        let attempts = 0;
        let generatedId = null;

        while (attempts < generateOptions.maxRetries) {
            try {
                // 生成基础标识符
                const baseId = this.generateBaseId(generateOptions);
                
                // 验证标识符格式
                if (!this.validateIdFormat(baseId)) {
                    attempts++;
                    continue;
                }

                // 如果提供了唯一性检查函数，进行验证
                if (generateOptions.uniquenessChecker) {
                    const isUnique = await generateOptions.uniquenessChecker(baseId);
                    if (!isUnique) {
                        console.log(`🔄 标识符冲突，重新生成: ${baseId}`);
                        attempts++;
                        continue;
                    }
                }

                generatedId = baseId;
                break;

            } catch (error) {
                console.error(`❌ 标识符生成失败 (尝试 ${attempts + 1}):`, error);
                attempts++;
            }
        }

        if (!generatedId) {
            throw new Error(`标识符生成失败，已尝试 ${attempts} 次`);
        }

        console.log(`✅ 成功生成用户友好标识符: ${generatedId}`);
        return generatedId;
    }

    /**
     * 生成基础标识符
     * @param {Object} options - 生成选项
     * @returns {string} 基础标识符
     */
    generateBaseId(options = {}) {
        // 随机选择形容词
        const adjective = this.getRandomWord(this.adjectives);
        
        // 随机选择名词
        const noun = this.getRandomWord(this.nouns);
        
        // 生成数字后缀
        const number = this.generateNumberSuffix(options);
        
        // 组合标识符
        const baseId = `${adjective}-${noun}-${number}`;
        
        // 长度检查
        if (baseId.length < options.minLength || baseId.length > options.maxLength) {
            // 如果长度不符合要求，递归重新生成
            return this.generateBaseId(options);
        }
        
        return baseId;
    }

    /**
     * 生成数字后缀
     * @param {Object} options - 生成选项
     * @returns {string} 数字后缀
     */
    generateNumberSuffix(options = {}) {
        const min = options.numberMin || this.options.numberMin;
        const max = options.numberMax || this.options.numberMax;
        
        if (options.enableSecurityEnhancement) {
            // 使用加密安全的随机数生成器
            const array = new Uint32Array(1);
            crypto.getRandomValues(array);
            const randomValue = array[0] / (0xFFFFFFFF + 1);
            return Math.floor(randomValue * (max - min + 1)) + min;
        } else {
            // 使用标准随机数生成器
            return Math.floor(Math.random() * (max - min + 1)) + min;
        }
    }

    /**
     * 获取随机词汇
     * @param {Array} wordList - 词汇列表
     * @returns {string} 随机词汇
     */
    getRandomWord(wordList) {
        if (this.options.enableSecurityEnhancement) {
            // 使用加密安全的随机数选择
            const array = new Uint32Array(1);
            crypto.getRandomValues(array);
            const randomIndex = array[0] % wordList.length;
            return wordList[randomIndex];
        } else {
            // 使用标准随机数选择
            const randomIndex = Math.floor(Math.random() * wordList.length);
            return wordList[randomIndex];
        }
    }

    /**
     * 验证标识符格式
     * @param {string} id - 待验证的标识符
     * @returns {boolean} 是否有效
     */
    validateIdFormat(id) {
        if (!id || typeof id !== 'string') {
            return false;
        }

        // 检查基本格式：形容词-名词-数字
        const pattern = /^[a-z]+(-[a-z]+)*-\d+$/;
        if (!pattern.test(id)) {
            return false;
        }

        // 检查长度
        if (id.length < this.options.minLength || id.length > this.options.maxLength) {
            return false;
        }

        // 检查组成部分
        const parts = id.split('-');
        if (parts.length < 3) {
            return false;
        }

        // 检查数字后缀
        const numberPart = parts[parts.length - 1];
        const number = parseInt(numberPart, 10);
        if (isNaN(number) || number < this.options.numberMin || number > this.options.numberMax) {
            return false;
        }

        return true;
    }

    /**
     * 解析标识符
     * @param {string} id - 标识符
     * @returns {Object} 解析结果
     */
    parseId(id) {
        if (!this.validateIdFormat(id)) {
            throw new Error('无效的标识符格式');
        }

        const parts = id.split('-');
        const numberPart = parts.pop();
        const wordParts = parts;

        return {
            id: id,
            words: wordParts,
            number: parseInt(numberPart, 10),
            adjective: wordParts[0],
            noun: wordParts.length > 1 ? wordParts[wordParts.length - 1] : null,
            isValid: true,
            length: id.length,
            createdAt: new Date().toISOString()
        };
    }

    /**
     * 生成多个候选标识符
     * @param {number} count - 生成数量
     * @param {Object} options - 生成选项
     * @returns {Promise<Array>} 候选标识符列表
     */
    async generateCandidates(count = 5, options = {}) {
        const candidates = [];
        const generateOptions = {
            ...this.options,
            ...options,
            maxRetries: 3 // 降低单个候选的重试次数
        };

        for (let i = 0; i < count; i++) {
            try {
                const candidate = await this.generateId(generateOptions);
                candidates.push({
                    id: candidate,
                    parsed: this.parseId(candidate),
                    score: this.calculateIdScore(candidate)
                });
            } catch (error) {
                console.warn(`⚠️ 候选标识符生成失败 (${i + 1}/${count}):`, error.message);
            }
        }

        // 按评分排序
        candidates.sort((a, b) => b.score - a.score);

        console.log(`🎯 生成 ${candidates.length} 个候选标识符`);
        return candidates;
    }

    /**
     * 计算标识符评分
     * 评分标准：易记性、输入便利性、美观度
     * @param {string} id - 标识符
     * @returns {number} 评分 (0-100)
     */
    calculateIdScore(id) {
        let score = 50; // 基础分

        try {
            const parsed = this.parseId(id);
            
            // 长度评分 (较短的更好)
            if (parsed.length <= 15) score += 10;
            else if (parsed.length <= 18) score += 5;
            
            // 词汇常见度评分
            const commonAdjectives = ['swift', 'bright', 'smart', 'strong', 'happy'];
            const commonNouns = ['eagle', 'star', 'lion', 'dragon', 'crystal'];
            
            if (commonAdjectives.includes(parsed.adjective)) score += 10;
            if (commonNouns.includes(parsed.noun)) score += 10;
            
            // 数字后缀评分 (避免过于简单的数字)
            if (parsed.number >= 2000 && parsed.number <= 8999) score += 10;
            
            // 音韵评分 (简单的音韵匹配)
            if (this.hasGoodPhonetics(parsed.adjective, parsed.noun)) score += 5;
            
        } catch (error) {
            score = 0; // 无效标识符
        }

        return Math.min(100, Math.max(0, score));
    }

    /**
     * 检查音韵匹配
     * @param {string} adjective - 形容词
     * @param {string} noun - 名词
     * @returns {boolean} 是否有良好的音韵
     */
    hasGoodPhonetics(adjective, noun) {
        // 简单的音韵检查：避免相同的开头字母
        return adjective[0] !== noun[0];
    }

    /**
     * 获取生成器统计信息
     * @returns {Object} 统计信息
     */
    getStats() {
        const totalCombinations = this.adjectives.length * this.nouns.length * 
            (this.options.numberMax - this.options.numberMin + 1);

        return {
            adjectives: this.adjectives.length,
            nouns: this.nouns.length,
            numberRange: `${this.options.numberMin}-${this.options.numberMax}`,
            totalCombinations: totalCombinations,
            estimatedUniqueness: totalCombinations > 1000000 ? '极高' : 
                                totalCombinations > 100000 ? '很高' : 
                                totalCombinations > 10000 ? '高' : '中等',
            options: this.options
        };
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FriendlyUserIdGenerator;
} else if (typeof window !== 'undefined') {
    window.FriendlyUserIdGenerator = FriendlyUserIdGenerator;
}
