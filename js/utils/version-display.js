/**
 * 版本信息显示组件
 * 提供在网页中显示版本信息的功能
 */

class VersionDisplay {
    constructor() {
        this.versionInfo = null;
        this.loadVersionInfo();
    }

    /**
     * 加载版本信息
     */
    async loadVersionInfo() {
        try {
            // 尝试从version.js模块加载
            if (typeof window !== 'undefined' && window.VersionInfo) {
                this.versionInfo = window.VersionInfo.getVersionInfo();
                return;
            }

            // 尝试从version.json加载
            const response = await fetch('/version.json');
            if (response.ok) {
                this.versionInfo = await response.json();
                return;
            }

            console.warn('无法加载版本信息');
        } catch (error) {
            console.warn('加载版本信息失败:', error);
        }
    }

    /**
     * 获取版本信息
     */
    getVersionInfo() {
        return this.versionInfo;
    }

    /**
     * 创建版本信息弹窗
     */
    createVersionModal() {
        if (!this.versionInfo) {
            console.warn('版本信息未加载');
            return null;
        }

        const modal = document.createElement('div');
        modal.className = 'version-modal';
        modal.innerHTML = `
            <div class="version-modal-overlay">
                <div class="version-modal-content">
                    <div class="version-modal-header">
                        <h3>🎮 Split-Second Spark</h3>
                        <button class="version-modal-close">&times;</button>
                    </div>
                    <div class="version-modal-body">
                        ${this.generateVersionHTML()}
                    </div>
                    <div class="version-modal-footer">
                        <button class="version-modal-copy">📋 复制信息</button>
                        <button class="version-modal-close-btn">关闭</button>
                    </div>
                </div>
            </div>
        `;

        // 添加样式
        this.addModalStyles();

        // 添加事件监听
        this.addModalEventListeners(modal);

        return modal;
    }

    /**
     * 生成版本信息HTML
     */
    generateVersionHTML() {
        const info = this.versionInfo;
        const git = info.git || {};
        const env = info.build_environment || {};

        return `
            <div class="version-section">
                <h4>📋 基本信息</h4>
                <div class="version-item">
                    <span class="version-label">版本号:</span>
                    <span class="version-value">${info.version || 'N/A'}</span>
                </div>
                <div class="version-item">
                    <span class="version-label">构建时间:</span>
                    <span class="version-value">${info.build_time_formatted || 'N/A'}</span>
                </div>
                <div class="version-item">
                    <span class="version-label">构建日期:</span>
                    <span class="version-value">${info.build_date || 'N/A'}</span>
                </div>
            </div>

            <div class="version-section">
                <h4>🔧 Git信息</h4>
                <div class="version-item">
                    <span class="version-label">提交哈希:</span>
                    <span class="version-value version-hash">${git.commit_hash_short || 'N/A'}</span>
                </div>
                <div class="version-item">
                    <span class="version-label">分支:</span>
                    <span class="version-value">${git.branch || 'N/A'}</span>
                </div>
                <div class="version-item">
                    <span class="version-label">提交消息:</span>
                    <span class="version-value">${git.commit_message || 'N/A'}</span>
                </div>
                <div class="version-item">
                    <span class="version-label">工作区状态:</span>
                    <span class="version-value ${git.is_dirty ? 'version-dirty' : 'version-clean'}">
                        ${git.is_dirty ? '有未提交更改' : '干净'}
                    </span>
                </div>
            </div>

            <div class="version-section">
                <h4>🖥️ 构建环境</h4>
                <div class="version-item">
                    <span class="version-label">操作系统:</span>
                    <span class="version-value">${env.system || 'N/A'}</span>
                </div>
                <div class="version-item">
                    <span class="version-label">Python版本:</span>
                    <span class="version-value">${env.python_version || 'N/A'}</span>
                </div>
                <div class="version-item">
                    <span class="version-label">构建工具:</span>
                    <span class="version-value">${env.build_tool || 'N/A'}</span>
                </div>
            </div>
        `;
    }

    /**
     * 添加模态框样式
     */
    addModalStyles() {
        if (document.getElementById('version-modal-styles')) {
            return; // 样式已存在
        }

        const style = document.createElement('style');
        style.id = 'version-modal-styles';
        style.textContent = `
            .version-modal {
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }

            .version-modal-overlay {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: rgba(0, 0, 0, 0.7);
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                box-sizing: border-box;
            }

            .version-modal-content {
                background: #1a1a2e;
                color: #eee;
                border-radius: 12px;
                max-width: 600px;
                width: 100%;
                max-height: 80vh;
                overflow: hidden;
                box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
                border: 1px solid #16213e;
            }

            .version-modal-header {
                padding: 20px;
                border-bottom: 1px solid #16213e;
                display: flex;
                justify-content: space-between;
                align-items: center;
                background: linear-gradient(135deg, #0f3460 0%, #16213e 100%);
            }

            .version-modal-header h3 {
                margin: 0;
                font-size: 1.5em;
                color: #64ffda;
            }

            .version-modal-close {
                background: none;
                border: none;
                color: #999;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                border-radius: 50%;
                transition: all 0.2s;
            }

            .version-modal-close:hover {
                background: rgba(255, 255, 255, 0.1);
                color: #fff;
            }

            .version-modal-body {
                padding: 20px;
                max-height: 60vh;
                overflow-y: auto;
            }

            .version-section {
                margin-bottom: 24px;
            }

            .version-section:last-child {
                margin-bottom: 0;
            }

            .version-section h4 {
                margin: 0 0 12px 0;
                color: #64ffda;
                font-size: 1.1em;
                border-bottom: 1px solid #16213e;
                padding-bottom: 8px;
            }

            .version-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 8px 0;
                border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            }

            .version-item:last-child {
                border-bottom: none;
            }

            .version-label {
                font-weight: 500;
                color: #ccc;
                min-width: 120px;
            }

            .version-value {
                color: #fff;
                font-family: 'Courier New', monospace;
                text-align: right;
                word-break: break-all;
            }

            .version-hash {
                background: #16213e;
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 0.9em;
            }

            .version-dirty {
                color: #ff6b6b !important;
            }

            .version-clean {
                color: #51cf66 !important;
            }

            .version-modal-footer {
                padding: 20px;
                border-top: 1px solid #16213e;
                display: flex;
                gap: 12px;
                justify-content: flex-end;
                background: rgba(255, 255, 255, 0.02);
            }

            .version-modal-copy,
            .version-modal-close-btn {
                padding: 8px 16px;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                font-size: 14px;
                transition: all 0.2s;
            }

            .version-modal-copy {
                background: #0f3460;
                color: #64ffda;
                border: 1px solid #64ffda;
            }

            .version-modal-copy:hover {
                background: #64ffda;
                color: #0f3460;
            }

            .version-modal-close-btn {
                background: #666;
                color: #fff;
            }

            .version-modal-close-btn:hover {
                background: #777;
            }

            @media (max-width: 768px) {
                .version-modal-content {
                    margin: 10px;
                    max-height: 90vh;
                }

                .version-item {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 4px;
                }

                .version-value {
                    text-align: left;
                }
            }
        `;

        document.head.appendChild(style);
    }

    /**
     * 添加模态框事件监听
     */
    addModalEventListeners(modal) {
        const overlay = modal.querySelector('.version-modal-overlay');
        const closeBtn = modal.querySelector('.version-modal-close');
        const closeBtnFooter = modal.querySelector('.version-modal-close-btn');
        const copyBtn = modal.querySelector('.version-modal-copy');

        // 关闭模态框
        const closeModal = () => {
            modal.remove();
        };

        // 点击遮罩关闭
        overlay.addEventListener('click', (e) => {
            if (e.target === overlay) {
                closeModal();
            }
        });

        // 点击关闭按钮
        closeBtn.addEventListener('click', closeModal);
        closeBtnFooter.addEventListener('click', closeModal);

        // ESC键关闭
        const handleKeydown = (e) => {
            if (e.key === 'Escape') {
                closeModal();
                document.removeEventListener('keydown', handleKeydown);
            }
        };
        document.addEventListener('keydown', handleKeydown);

        // 复制版本信息
        copyBtn.addEventListener('click', () => {
            this.copyVersionInfo();
        });
    }

    /**
     * 复制版本信息到剪贴板
     */
    async copyVersionInfo() {
        if (!this.versionInfo) {
            return;
        }

        const info = this.versionInfo;
        const git = info.git || {};
        const env = info.build_environment || {};

        const text = `Split-Second Spark 版本信息
${'='.repeat(40)}

基本信息:
  版本号: ${info.version || 'N/A'}
  构建时间: ${info.build_time_formatted || 'N/A'}
  构建日期: ${info.build_date || 'N/A'}

Git信息:
  提交哈希: ${git.commit_hash_short || 'N/A'}
  分支: ${git.branch || 'N/A'}
  提交消息: ${git.commit_message || 'N/A'}
  工作区状态: ${git.is_dirty ? '有未提交更改' : '干净'}

构建环境:
  操作系统: ${env.system || 'N/A'}
  Python版本: ${env.python_version || 'N/A'}
  构建工具: ${env.build_tool || 'N/A'}

${'='.repeat(40)}
生成时间: ${info.build_time_formatted || 'N/A'}`;

        try {
            await navigator.clipboard.writeText(text);
            this.showToast('版本信息已复制到剪贴板', 'success');
        } catch (error) {
            console.warn('复制失败，尝试备用方法:', error);
            this.fallbackCopy(text);
        }
    }

    /**
     * 备用复制方法
     */
    fallbackCopy(text) {
        const textarea = document.createElement('textarea');
        textarea.value = text;
        textarea.style.position = 'fixed';
        textarea.style.opacity = '0';
        document.body.appendChild(textarea);
        textarea.select();

        try {
            document.execCommand('copy');
            this.showToast('版本信息已复制到剪贴板', 'success');
        } catch (error) {
            this.showToast('复制失败', 'error');
        }

        document.body.removeChild(textarea);
    }

    /**
     * 显示提示消息
     */
    showToast(message, type = 'info') {
        const toast = document.createElement('div');
        toast.className = `version-toast version-toast-${type}`;
        toast.textContent = message;

        // 添加toast样式
        if (!document.getElementById('version-toast-styles')) {
            const style = document.createElement('style');
            style.id = 'version-toast-styles';
            style.textContent = `
                .version-toast {
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    padding: 12px 20px;
                    border-radius: 6px;
                    color: white;
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                    font-size: 14px;
                    z-index: 10001;
                    animation: slideInRight 0.3s ease-out;
                }

                .version-toast-success {
                    background: #51cf66;
                }

                .version-toast-error {
                    background: #ff6b6b;
                }

                .version-toast-info {
                    background: #339af0;
                }

                @keyframes slideInRight {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
            `;
            document.head.appendChild(style);
        }

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.remove();
            }
        }, 3000);
    }

    /**
     * 显示版本信息模态框
     */
    showVersionModal() {
        // 移除已存在的模态框
        const existingModal = document.querySelector('.version-modal');
        if (existingModal) {
            existingModal.remove();
        }

        const modal = this.createVersionModal();
        if (modal) {
            document.body.appendChild(modal);
        }
    }

    /**
     * 创建版本信息按钮
     */
    createVersionButton(options = {}) {
        const {
            text = '版本信息',
            className = 'version-button',
            style = {}
        } = options;

        const button = document.createElement('button');
        button.className = className;
        button.textContent = text;
        button.title = '点击查看版本信息';

        // 默认样式
        Object.assign(button.style, {
            position: 'fixed',
            bottom: '20px',
            right: '20px',
            padding: '8px 12px',
            background: 'rgba(15, 52, 96, 0.9)',
            color: '#64ffda',
            border: '1px solid #64ffda',
            borderRadius: '6px',
            cursor: 'pointer',
            fontSize: '12px',
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',
            zIndex: '1000',
            transition: 'all 0.2s',
            ...style
        });

        // 悬停效果
        button.addEventListener('mouseenter', () => {
            button.style.background = '#64ffda';
            button.style.color = '#0f3460';
        });

        button.addEventListener('mouseleave', () => {
            button.style.background = 'rgba(15, 52, 96, 0.9)';
            button.style.color = '#64ffda';
        });

        // 点击显示版本信息
        button.addEventListener('click', () => {
            this.showVersionModal();
        });

        return button;
    }

    /**
     * 在页面中添加版本信息按钮
     */
    addVersionButton(options = {}) {
        const button = this.createVersionButton(options);
        document.body.appendChild(button);
        return button;
    }

    /**
     * 格式化版本字符串（简短格式）
     */
    getVersionString() {
        if (!this.versionInfo) {
            return 'v1.0.0';
        }

        const version = this.versionInfo.version;
        const commit = this.versionInfo.git?.commit_hash_short;
        const isDev = this.versionInfo.git?.is_dirty;

        let versionStr = `v${version}`;
        if (isDev) {
            versionStr += '-dev';
        }
        if (commit) {
            versionStr += ` (${commit})`;
        }

        return versionStr;
    }
}

// 创建全局实例
const versionDisplay = new VersionDisplay();

// 导出到全局作用域
if (typeof window !== 'undefined') {
    window.VersionDisplay = VersionDisplay;
    window.versionDisplay = versionDisplay;
}

// 自动添加版本按钮（可通过URL参数控制）
if (typeof window !== 'undefined') {
    document.addEventListener('DOMContentLoaded', () => {
        const urlParams = new URLSearchParams(window.location.search);
        const showVersionButton = urlParams.get('version') !== 'false';

        if (showVersionButton) {
            // 延迟添加，确保页面加载完成
            setTimeout(() => {
                versionDisplay.addVersionButton();
            }, 1000);
        }
    });
}
