/**
 * Split-Second Spark - 用户友好标识符管理系统
 * 
 * 功能说明：
 * - 管理用户友好标识符的生成、存储和验证
 * - 与现有用户身份管理系统集成
 * - 支持跨设备数据同步和访问控制
 * - 提供标识符冲突检测和解决机制
 * - 支持标识符历史记录和恢复
 */

/**
 * 用户友好标识符管理器
 * 集成标识符生成、存储、验证和同步功能
 */
class UserFriendlyIdManager {
    constructor(userIdentityManager, storageService, options = {}) {
        this.userManager = userIdentityManager;
        this.storageService = storageService;
        
        // 配置选项
        this.options = {
            // 标识符生成选项
            idGeneratorOptions: {
                minLength: 12,
                maxLength: 20,
                numberMin: 1000,
                numberMax: 9999,
                enableSecurityEnhancement: true,
                ...options.idGeneratorOptions
            },
            
            // 存储选项
            storagePrefix: options.storagePrefix || 'friendly_id_',
            enableHistory: options.enableHistory !== false,
            maxHistoryEntries: options.maxHistoryEntries || 10,
            
            // 同步选项
            enableAutoSync: options.enableAutoSync !== false,
            syncInterval: options.syncInterval || 300000, // 5分钟
            
            // 验证选项
            enableRemoteValidation: options.enableRemoteValidation !== false,
            validationTimeout: options.validationTimeout || 5000,
            
            ...options
        };

        // 初始化标识符生成器
        this.idGenerator = new FriendlyUserIdGenerator(this.options.idGeneratorOptions);
        
        // 内部状态
        this.currentFriendlyId = null;
        this.idHistory = [];
        this.isInitialized = false;
        this.syncTimer = null;
        
        // 缓存
        this.validationCache = new Map();
        this.remoteIdRegistry = new Set();
        
        console.log('🎯 用户友好标识符管理器初始化中...', this.options);
    }

    /**
     * 初始化管理器
     */
    async init() {
        try {
            console.log('🚀 初始化用户友好标识符管理器...');
            
            // 等待用户身份管理器初始化
            if (!this.userManager.isInitialized) {
                console.log('⏳ 等待用户身份管理器初始化...');
                await this.waitForUserManager();
            }
            
            // 加载现有标识符
            await this.loadExistingId();
            
            // 如果没有标识符，生成新的
            if (!this.currentFriendlyId) {
                await this.generateNewId();
            }
            
            // 启动自动同步
            if (this.options.enableAutoSync) {
                this.startAutoSync();
            }
            
            // 监听用户状态变化
            this.setupEventListeners();
            
            this.isInitialized = true;
            console.log('✅ 用户友好标识符管理器初始化完成');
            
            // 触发初始化完成事件
            this.dispatchEvent('friendlyIdManager:initialized', {
                friendlyId: this.currentFriendlyId,
                userId: this.userManager.currentUser?.uid
            });
            
        } catch (error) {
            console.error('❌ 用户友好标识符管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 等待用户身份管理器初始化
     */
    async waitForUserManager() {
        return new Promise((resolve) => {
            const checkInterval = setInterval(() => {
                if (this.userManager.isInitialized) {
                    clearInterval(checkInterval);
                    resolve();
                }
            }, 100);
            
            // 超时保护
            setTimeout(() => {
                clearInterval(checkInterval);
                resolve();
            }, 10000);
        });
    }

    /**
     * 加载现有标识符
     */
    async loadExistingId() {
        try {
            console.log('📖 加载现有用户友好标识符...');
            
            // 从存储中加载当前标识符
            const storedId = await this.storageService.get(
                `${this.options.storagePrefix}current`
            );
            
            if (storedId && this.idGenerator.validateIdFormat(storedId)) {
                this.currentFriendlyId = storedId;
                console.log('✅ 加载现有标识符:', storedId);
            }
            
            // 加载历史记录
            if (this.options.enableHistory) {
                await this.loadIdHistory();
            }
            
        } catch (error) {
            console.warn('⚠️ 加载现有标识符失败:', error);
        }
    }

    /**
     * 加载标识符历史记录
     */
    async loadIdHistory() {
        try {
            const historyData = await this.storageService.get(
                `${this.options.storagePrefix}history`
            );
            
            if (historyData && Array.isArray(historyData)) {
                this.idHistory = historyData;
                console.log(`📚 加载标识符历史记录: ${this.idHistory.length} 条`);
            }
            
        } catch (error) {
            console.warn('⚠️ 加载标识符历史记录失败:', error);
            this.idHistory = [];
        }
    }

    /**
     * 生成新的用户友好标识符
     */
    async generateNewId(options = {}) {
        try {
            console.log('🎲 生成新的用户友好标识符...');
            
            const generateOptions = {
                ...this.options.idGeneratorOptions,
                ...options,
                uniquenessChecker: (id) => this.checkIdUniqueness(id)
            };
            
            // 生成新标识符
            const newId = await this.idGenerator.generateId(generateOptions);
            
            // 保存旧标识符到历史记录
            if (this.currentFriendlyId && this.options.enableHistory) {
                await this.addToHistory(this.currentFriendlyId);
            }
            
            // 设置新标识符
            this.currentFriendlyId = newId;
            
            // 保存到存储
            await this.saveCurrentId();
            
            console.log('✅ 新标识符生成完成:', newId);
            
            // 触发标识符变更事件
            this.dispatchEvent('friendlyIdManager:idChanged', {
                newId: newId,
                oldId: this.idHistory.length > 0 ? this.idHistory[0].id : null,
                userId: this.userManager.currentUser?.uid
            });
            
            return newId;
            
        } catch (error) {
            console.error('❌ 生成新标识符失败:', error);
            throw error;
        }
    }

    /**
     * 检查标识符唯一性
     */
    async checkIdUniqueness(id) {
        try {
            // 检查本地缓存
            if (this.validationCache.has(id)) {
                const cached = this.validationCache.get(id);
                if (Date.now() - cached.timestamp < 60000) { // 1分钟缓存
                    return cached.isUnique;
                }
            }
            
            // 检查本地历史记录
            const isInHistory = this.idHistory.some(entry => entry.id === id);
            if (isInHistory) {
                console.log('🔍 标识符在历史记录中存在:', id);
                return false;
            }
            
            // 远程验证（如果启用）
            let isRemoteUnique = true;
            if (this.options.enableRemoteValidation) {
                isRemoteUnique = await this.checkRemoteUniqueness(id);
            }
            
            const isUnique = !isInHistory && isRemoteUnique;
            
            // 缓存结果
            this.validationCache.set(id, {
                isUnique: isUnique,
                timestamp: Date.now()
            });
            
            return isUnique;
            
        } catch (error) {
            console.warn('⚠️ 标识符唯一性检查失败:', error);
            return true; // 默认认为唯一，避免阻塞生成
        }
    }

    /**
     * 远程唯一性检查
     */
    async checkRemoteUniqueness(id) {
        try {
            // 这里可以集成实际的远程验证服务
            // 例如：调用云函数或API检查全局唯一性
            
            // 模拟远程检查
            console.log('🌐 执行远程唯一性检查:', id);
            
            // 检查远程注册表（模拟）
            if (this.remoteIdRegistry.has(id)) {
                return false;
            }
            
            // 实际实现中，这里应该调用远程API
            // const response = await fetch('/api/check-friendly-id', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify({ id })
            // });
            // return response.ok && (await response.json()).isUnique;
            
            return true;
            
        } catch (error) {
            console.warn('⚠️ 远程唯一性检查失败:', error);
            return true; // 网络错误时默认认为唯一
        }
    }

    /**
     * 保存当前标识符
     */
    async saveCurrentId() {
        try {
            await this.storageService.set(
                `${this.options.storagePrefix}current`,
                this.currentFriendlyId
            );
            
            // 保存元数据
            await this.storageService.set(
                `${this.options.storagePrefix}metadata`,
                {
                    id: this.currentFriendlyId,
                    createdAt: new Date().toISOString(),
                    userId: this.userManager.currentUser?.uid,
                    deviceId: this.userManager.getDeviceId?.() || 'unknown'
                }
            );
            
            console.log('💾 当前标识符已保存:', this.currentFriendlyId);
            
        } catch (error) {
            console.error('❌ 保存当前标识符失败:', error);
            throw error;
        }
    }

    /**
     * 添加到历史记录
     */
    async addToHistory(id) {
        try {
            const historyEntry = {
                id: id,
                replacedAt: new Date().toISOString(),
                userId: this.userManager.currentUser?.uid,
                deviceId: this.userManager.getDeviceId?.() || 'unknown'
            };
            
            // 添加到历史记录开头
            this.idHistory.unshift(historyEntry);
            
            // 限制历史记录数量
            if (this.idHistory.length > this.options.maxHistoryEntries) {
                this.idHistory = this.idHistory.slice(0, this.options.maxHistoryEntries);
            }
            
            // 保存历史记录
            await this.storageService.set(
                `${this.options.storagePrefix}history`,
                this.idHistory
            );
            
            console.log('📚 标识符已添加到历史记录:', id);
            
        } catch (error) {
            console.error('❌ 添加历史记录失败:', error);
        }
    }

    /**
     * 获取当前用户友好标识符
     */
    getCurrentId() {
        return this.currentFriendlyId;
    }

    /**
     * 获取标识符历史记录
     */
    getIdHistory() {
        return [...this.idHistory];
    }

    /**
     * 验证标识符格式
     */
    validateId(id) {
        return this.idGenerator.validateIdFormat(id);
    }

    /**
     * 解析标识符
     */
    parseId(id) {
        return this.idGenerator.parseId(id);
    }

    /**
     * 生成候选标识符
     */
    async generateCandidates(count = 5) {
        const options = {
            uniquenessChecker: (id) => this.checkIdUniqueness(id)
        };
        
        return await this.idGenerator.generateCandidates(count, options);
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听用户登录状态变化
        window.addEventListener('userIdentity:userSignedIn', (event) => {
            console.log('👤 用户登录，同步友好标识符');
            this.handleUserSignIn(event.detail);
        });
        
        window.addEventListener('userIdentity:userSignedOut', (event) => {
            console.log('👤 用户登出，清理友好标识符');
            this.handleUserSignOut();
        });
    }

    /**
     * 处理用户登录
     */
    async handleUserSignIn(userInfo) {
        try {
            // 同步用户数据
            await this.syncWithCloud();
            
        } catch (error) {
            console.error('❌ 用户登录后同步失败:', error);
        }
    }

    /**
     * 处理用户登出
     */
    handleUserSignOut() {
        // 停止自动同步
        this.stopAutoSync();
        
        // 清理缓存
        this.validationCache.clear();
    }

    /**
     * 与云端同步
     */
    async syncWithCloud() {
        try {
            console.log('🔄 与云端同步友好标识符...');
            
            // 这里可以实现与云存储的同步逻辑
            // 例如：上传本地标识符，下载云端标识符，解决冲突等
            
            console.log('✅ 云端同步完成');
            
        } catch (error) {
            console.error('❌ 云端同步失败:', error);
        }
    }

    /**
     * 启动自动同步
     */
    startAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
        }
        
        this.syncTimer = setInterval(() => {
            if (this.userManager.currentUser) {
                this.syncWithCloud().catch(error => {
                    console.warn('⚠️ 自动同步失败:', error);
                });
            }
        }, this.options.syncInterval);
        
        console.log('🔄 自动同步已启动');
    }

    /**
     * 停止自动同步
     */
    stopAutoSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
            console.log('⏹️ 自动同步已停止');
        }
    }

    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
    }

    /**
     * 获取管理器统计信息
     */
    getStats() {
        return {
            currentId: this.currentFriendlyId,
            historyCount: this.idHistory.length,
            isInitialized: this.isInitialized,
            cacheSize: this.validationCache.size,
            autoSyncEnabled: !!this.syncTimer,
            generatorStats: this.idGenerator.getStats(),
            options: this.options
        };
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.stopAutoSync();
        this.validationCache.clear();
        this.remoteIdRegistry.clear();
        this.isInitialized = false;
        
        console.log('🗑️ 用户友好标识符管理器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserFriendlyIdManager;
} else if (typeof window !== 'undefined') {
    window.UserFriendlyIdManager = UserFriendlyIdManager;
}
