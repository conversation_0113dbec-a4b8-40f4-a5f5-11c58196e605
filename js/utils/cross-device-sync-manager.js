/**
 * Split-Second Spark - 跨设备数据同步管理器
 * 
 * 功能说明：
 * - 基于用户友好标识符实现跨设备数据同步
 * - 支持冲突检测和智能解决
 * - 提供离线支持和网络恢复机制
 * - 实现增量同步和数据压缩
 * - 支持同步状态监控和错误恢复
 */

/**
 * 跨设备数据同步管理器
 * 管理基于友好标识符的跨设备数据同步
 */
class CrossDeviceSyncManager {
    constructor(friendlyIdManager, storageAdapter, options = {}) {
        this.friendlyIdManager = friendlyIdManager;
        this.storageAdapter = storageAdapter;
        
        // 配置选项
        this.options = {
            // 同步策略
            syncStrategy: options.syncStrategy || 'incremental', // 'full' | 'incremental'
            syncInterval: options.syncInterval || 300000, // 5分钟
            
            // 冲突解决
            conflictResolution: options.conflictResolution || 'timestamp', // 'timestamp' | 'manual' | 'merge'
            maxConflictHistory: options.maxConflictHistory || 10,
            
            // 网络选项
            enableOfflineSupport: options.enableOfflineSupport !== false,
            maxRetryAttempts: options.maxRetryAttempts || 3,
            retryDelay: options.retryDelay || 5000,
            
            // 数据压缩
            enableCompression: options.enableCompression || false,
            compressionThreshold: options.compressionThreshold || 1024, // 1KB
            
            // 同步限制
            maxSyncDataSize: options.maxSyncDataSize || 10 * 1024 * 1024, // 10MB
            maxSyncItems: options.maxSyncItems || 1000,
            
            ...options
        };

        // 内部状态
        this.isInitialized = false;
        this.isSyncing = false;
        this.syncTimer = null;
        this.lastSyncTime = null;
        
        // 同步队列和状态
        this.syncQueue = new Map();
        this.conflictQueue = [];
        this.syncHistory = [];
        this.deviceRegistry = new Map();
        
        // 网络状态
        this.isOnline = navigator.onLine;
        this.pendingOperations = [];
        
        console.log('🔄 跨设备数据同步管理器初始化中...', this.options);
    }

    /**
     * 初始化同步管理器
     */
    async init() {
        try {
            console.log('🚀 初始化跨设备数据同步管理器...');
            
            // 等待依赖组件初始化
            await this.waitForDependencies();
            
            // 注册当前设备
            await this.registerCurrentDevice();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 执行初始同步
            await this.performInitialSync();
            
            // 启动定期同步
            this.startPeriodicSync();
            
            this.isInitialized = true;
            console.log('✅ 跨设备数据同步管理器初始化完成');
            
            // 触发初始化完成事件
            this.dispatchEvent('crossDeviceSync:initialized', {
                friendlyId: this.friendlyIdManager.getCurrentId(),
                deviceId: this.getCurrentDeviceId()
            });
            
        } catch (error) {
            console.error('❌ 跨设备数据同步管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 等待依赖组件初始化
     */
    async waitForDependencies() {
        const maxWaitTime = 10000; // 10秒
        const startTime = Date.now();
        
        while (Date.now() - startTime < maxWaitTime) {
            if (this.friendlyIdManager.isInitialized && this.storageAdapter.isInitialized) {
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error('依赖组件初始化超时');
    }

    /**
     * 注册当前设备
     */
    async registerCurrentDevice() {
        try {
            const deviceInfo = {
                deviceId: this.getCurrentDeviceId(),
                friendlyId: this.friendlyIdManager.getCurrentId(),
                deviceName: this.getDeviceName(),
                platform: navigator.platform,
                userAgent: navigator.userAgent,
                screenResolution: `${screen.width}x${screen.height}`,
                timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
                language: navigator.language,
                registeredAt: new Date().toISOString(),
                lastSyncAt: null,
                syncVersion: 1
            };
            
            // 保存设备信息到本地
            await this.storageAdapter.put('_device_info', deviceInfo);
            
            // 添加到设备注册表
            this.deviceRegistry.set(deviceInfo.deviceId, deviceInfo);
            
            console.log('📱 当前设备已注册:', deviceInfo.deviceName);
            
        } catch (error) {
            console.error('❌ 设备注册失败:', error);
        }
    }

    /**
     * 获取当前设备ID
     */
    getCurrentDeviceId() {
        return this.friendlyIdManager.userManager.getDeviceId?.() || 
               `device_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * 获取设备名称
     */
    getDeviceName() {
        const platform = navigator.platform.toLowerCase();
        const userAgent = navigator.userAgent.toLowerCase();
        
        if (platform.includes('win')) return 'Windows 设备';
        if (platform.includes('mac')) return 'Mac 设备';
        if (platform.includes('linux')) return 'Linux 设备';
        if (userAgent.includes('mobile')) return '移动设备';
        if (userAgent.includes('tablet')) return '平板设备';
        
        return '未知设备';
    }

    /**
     * 执行初始同步
     */
    async performInitialSync() {
        try {
            console.log('🔄 执行初始数据同步...');
            
            if (!this.isOnline) {
                console.log('📴 离线状态，跳过初始同步');
                return;
            }
            
            // 获取远程设备列表
            const remoteDevices = await this.getRemoteDevices();
            
            // 如果有其他设备，执行数据同步
            if (remoteDevices.length > 0) {
                await this.syncWithRemoteDevices(remoteDevices);
            }
            
            this.lastSyncTime = new Date().toISOString();
            console.log('✅ 初始同步完成');
            
        } catch (error) {
            console.error('❌ 初始同步失败:', error);
        }
    }

    /**
     * 获取远程设备列表
     */
    async getRemoteDevices() {
        try {
            // 这里应该从云存储获取同一友好标识符下的其他设备
            // 为了演示，返回模拟数据
            console.log('🌐 获取远程设备列表...');
            
            // 实际实现中，这里应该调用云存储API
            // const response = await fetch('/api/devices', {
            //     method: 'POST',
            //     headers: { 'Content-Type': 'application/json' },
            //     body: JSON.stringify({ friendlyId: this.friendlyIdManager.getCurrentId() })
            // });
            // return await response.json();
            
            return []; // 暂时返回空数组
            
        } catch (error) {
            console.error('❌ 获取远程设备列表失败:', error);
            return [];
        }
    }

    /**
     * 与远程设备同步
     */
    async syncWithRemoteDevices(remoteDevices) {
        try {
            console.log(`🔄 与 ${remoteDevices.length} 个远程设备同步...`);
            
            for (const device of remoteDevices) {
                await this.syncWithDevice(device);
            }
            
        } catch (error) {
            console.error('❌ 远程设备同步失败:', error);
        }
    }

    /**
     * 与单个设备同步
     */
    async syncWithDevice(device) {
        try {
            console.log(`🔄 与设备同步: ${device.deviceName}`);
            
            // 获取设备的数据清单
            const remoteManifest = await this.getDeviceDataManifest(device);
            
            // 获取本地数据清单
            const localManifest = await this.getLocalDataManifest();
            
            // 比较清单，找出需要同步的数据
            const syncPlan = this.createSyncPlan(localManifest, remoteManifest);
            
            // 执行同步计划
            await this.executeSyncPlan(syncPlan, device);
            
        } catch (error) {
            console.error(`❌ 与设备 ${device.deviceName} 同步失败:`, error);
        }
    }

    /**
     * 获取设备数据清单
     */
    async getDeviceDataManifest(device) {
        // 这里应该从云存储获取设备的数据清单
        // 清单包含每个数据项的键、版本、时间戳等信息
        console.log(`📋 获取设备数据清单: ${device.deviceName}`);
        return {};
    }

    /**
     * 获取本地数据清单
     */
    async getLocalDataManifest() {
        try {
            const keys = await this.storageAdapter.list();
            const manifest = {};
            
            for (const key of keys) {
                if (key.startsWith('_')) continue; // 跳过系统键
                
                const data = await this.storageAdapter.get(key);
                if (data && data.metadata) {
                    manifest[key] = {
                        version: data.metadata.version || 1,
                        timestamp: data.metadata.timestamp,
                        checksum: data.metadata.checksum,
                        deviceId: data.metadata.deviceId
                    };
                }
            }
            
            return manifest;
            
        } catch (error) {
            console.error('❌ 获取本地数据清单失败:', error);
            return {};
        }
    }

    /**
     * 创建同步计划
     */
    createSyncPlan(localManifest, remoteManifest) {
        const syncPlan = {
            toDownload: [], // 需要从远程下载的数据
            toUpload: [],   // 需要上传到远程的数据
            conflicts: []   // 冲突的数据
        };
        
        // 检查远程数据
        for (const [key, remoteInfo] of Object.entries(remoteManifest)) {
            const localInfo = localManifest[key];
            
            if (!localInfo) {
                // 本地没有，需要下载
                syncPlan.toDownload.push({ key, remoteInfo });
            } else {
                // 比较版本和时间戳
                const remoteTime = new Date(remoteInfo.timestamp);
                const localTime = new Date(localInfo.timestamp);
                
                if (remoteTime > localTime) {
                    syncPlan.toDownload.push({ key, remoteInfo, localInfo });
                } else if (localTime > remoteTime) {
                    syncPlan.toUpload.push({ key, localInfo, remoteInfo });
                } else if (remoteInfo.checksum !== localInfo.checksum) {
                    // 时间相同但内容不同，标记为冲突
                    syncPlan.conflicts.push({ key, localInfo, remoteInfo });
                }
            }
        }
        
        // 检查本地独有的数据
        for (const [key, localInfo] of Object.entries(localManifest)) {
            if (!remoteManifest[key]) {
                syncPlan.toUpload.push({ key, localInfo });
            }
        }
        
        return syncPlan;
    }

    /**
     * 执行同步计划
     */
    async executeSyncPlan(syncPlan, device) {
        try {
            console.log('📋 执行同步计划:', {
                toDownload: syncPlan.toDownload.length,
                toUpload: syncPlan.toUpload.length,
                conflicts: syncPlan.conflicts.length
            });
            
            // 下载数据
            for (const item of syncPlan.toDownload) {
                await this.downloadData(item, device);
            }
            
            // 上传数据
            for (const item of syncPlan.toUpload) {
                await this.uploadData(item, device);
            }
            
            // 处理冲突
            for (const conflict of syncPlan.conflicts) {
                await this.handleDataConflict(conflict, device);
            }
            
        } catch (error) {
            console.error('❌ 执行同步计划失败:', error);
        }
    }

    /**
     * 下载数据
     */
    async downloadData(item, device) {
        try {
            console.log(`⬇️ 下载数据: ${item.key}`);
            
            // 这里应该从云存储下载实际数据
            // const data = await this.fetchRemoteData(item.key, device);
            // await this.storageAdapter.put(item.key, data);
            
        } catch (error) {
            console.error(`❌ 下载数据失败 [${item.key}]:`, error);
        }
    }

    /**
     * 上传数据
     */
    async uploadData(item, device) {
        try {
            console.log(`⬆️ 上传数据: ${item.key}`);
            
            // 这里应该上传数据到云存储
            // const data = await this.storageAdapter.get(item.key);
            // await this.uploadRemoteData(item.key, data, device);
            
        } catch (error) {
            console.error(`❌ 上传数据失败 [${item.key}]:`, error);
        }
    }

    /**
     * 处理数据冲突
     */
    async handleDataConflict(conflict, device) {
        try {
            console.log(`⚠️ 处理数据冲突: ${conflict.key}`);
            
            switch (this.options.conflictResolution) {
                case 'timestamp':
                    await this.resolveConflictByTimestamp(conflict, device);
                    break;
                case 'manual':
                    this.addToConflictQueue(conflict, device);
                    break;
                case 'merge':
                    await this.resolveConflictByMerge(conflict, device);
                    break;
                default:
                    console.warn('未知的冲突解决策略:', this.options.conflictResolution);
            }
            
        } catch (error) {
            console.error(`❌ 处理数据冲突失败 [${conflict.key}]:`, error);
        }
    }

    /**
     * 按时间戳解决冲突
     */
    async resolveConflictByTimestamp(conflict, device) {
        const localTime = new Date(conflict.localInfo.timestamp);
        const remoteTime = new Date(conflict.remoteInfo.timestamp);
        
        if (remoteTime > localTime) {
            console.log(`🕒 选择远程版本 (更新): ${conflict.key}`);
            await this.downloadData(conflict, device);
        } else {
            console.log(`🕒 保持本地版本 (更新): ${conflict.key}`);
            await this.uploadData(conflict, device);
        }
    }

    /**
     * 添加到冲突队列
     */
    addToConflictQueue(conflict, device) {
        this.conflictQueue.push({
            ...conflict,
            device: device,
            timestamp: new Date().toISOString()
        });
        
        // 触发冲突事件
        this.dispatchEvent('crossDeviceSync:conflict', {
            conflict: conflict,
            device: device
        });
    }

    /**
     * 通过合并解决冲突
     */
    async resolveConflictByMerge(conflict, device) {
        try {
            console.log(`🔀 尝试合并数据: ${conflict.key}`);
            
            // 获取本地和远程数据
            const localData = await this.storageAdapter.get(conflict.key);
            // const remoteData = await this.fetchRemoteData(conflict.key, device);
            
            // 这里应该实现智能合并逻辑
            // const mergedData = this.mergeData(localData, remoteData);
            // await this.storageAdapter.put(conflict.key, mergedData);
            
            console.log(`✅ 数据合并完成: ${conflict.key}`);
            
        } catch (error) {
            console.error(`❌ 数据合并失败 [${conflict.key}]:`, error);
            // 合并失败时添加到手动处理队列
            this.addToConflictQueue(conflict, device);
        }
    }

    /**
     * 启动定期同步
     */
    startPeriodicSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
        }
        
        this.syncTimer = setInterval(() => {
            if (this.isOnline && !this.isSyncing) {
                this.performPeriodicSync().catch(error => {
                    console.warn('⚠️ 定期同步失败:', error);
                });
            }
        }, this.options.syncInterval);
        
        console.log('🔄 定期同步已启动');
    }

    /**
     * 执行定期同步
     */
    async performPeriodicSync() {
        try {
            if (this.isSyncing) {
                console.log('🔄 同步正在进行中，跳过本次定期同步');
                return;
            }
            
            this.isSyncing = true;
            console.log('🔄 执行定期同步...');
            
            // 执行增量同步
            await this.performIncrementalSync();
            
            this.lastSyncTime = new Date().toISOString();
            console.log('✅ 定期同步完成');
            
        } catch (error) {
            console.error('❌ 定期同步失败:', error);
        } finally {
            this.isSyncing = false;
        }
    }

    /**
     * 执行增量同步
     */
    async performIncrementalSync() {
        try {
            // 获取自上次同步以来的变更
            const changes = await this.getChangesSinceLastSync();
            
            if (changes.length === 0) {
                console.log('📝 没有需要同步的变更');
                return;
            }
            
            console.log(`📝 发现 ${changes.length} 个变更需要同步`);
            
            // 同步变更到云端
            for (const change of changes) {
                await this.syncChange(change);
            }
            
        } catch (error) {
            console.error('❌ 增量同步失败:', error);
        }
    }

    /**
     * 获取自上次同步以来的变更
     */
    async getChangesSinceLastSync() {
        // 这里应该实现变更跟踪逻辑
        // 可以通过比较数据时间戳或使用变更日志
        return [];
    }

    /**
     * 同步单个变更
     */
    async syncChange(change) {
        try {
            console.log(`🔄 同步变更: ${change.key} (${change.operation})`);
            
            // 根据操作类型执行相应的同步
            switch (change.operation) {
                case 'put':
                    await this.syncPutOperation(change);
                    break;
                case 'delete':
                    await this.syncDeleteOperation(change);
                    break;
                default:
                    console.warn('未知的同步操作:', change.operation);
            }
            
        } catch (error) {
            console.error(`❌ 同步变更失败 [${change.key}]:`, error);
        }
    }

    /**
     * 同步PUT操作
     */
    async syncPutOperation(change) {
        // 实现PUT操作的云端同步
        console.log(`💾 同步PUT操作: ${change.key}`);
    }

    /**
     * 同步DELETE操作
     */
    async syncDeleteOperation(change) {
        // 实现DELETE操作的云端同步
        console.log(`🗑️ 同步DELETE操作: ${change.key}`);
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听网络状态变化
        window.addEventListener('online', () => {
            console.log('🌐 网络已连接');
            this.isOnline = true;
            this.processPendingOperations();
        });
        
        window.addEventListener('offline', () => {
            console.log('📴 网络已断开');
            this.isOnline = false;
        });
        
        // 监听存储变更
        window.addEventListener('friendlyIdStorage:dataChanged', (event) => {
            this.handleStorageChange(event.detail);
        });
    }

    /**
     * 处理存储变更
     */
    handleStorageChange(detail) {
        if (!this.options.enableOfflineSupport) {
            return;
        }
        
        // 如果离线，添加到待处理队列
        if (!this.isOnline) {
            this.pendingOperations.push({
                ...detail,
                timestamp: new Date().toISOString()
            });
        }
    }

    /**
     * 处理待处理操作
     */
    async processPendingOperations() {
        if (this.pendingOperations.length === 0) {
            return;
        }
        
        console.log(`🔄 处理 ${this.pendingOperations.length} 个待处理操作...`);
        
        const operations = [...this.pendingOperations];
        this.pendingOperations = [];
        
        for (const operation of operations) {
            try {
                await this.syncChange(operation);
            } catch (error) {
                console.error('❌ 处理待处理操作失败:', error);
                // 重新添加到队列
                this.pendingOperations.push(operation);
            }
        }
    }

    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
    }

    /**
     * 获取同步统计信息
     */
    getStats() {
        return {
            isInitialized: this.isInitialized,
            isSyncing: this.isSyncing,
            lastSyncTime: this.lastSyncTime,
            isOnline: this.isOnline,
            syncQueueSize: this.syncQueue.size,
            conflictQueueSize: this.conflictQueue.length,
            pendingOperationsCount: this.pendingOperations.length,
            deviceCount: this.deviceRegistry.size,
            options: this.options
        };
    }

    /**
     * 停止同步
     */
    stopSync() {
        if (this.syncTimer) {
            clearInterval(this.syncTimer);
            this.syncTimer = null;
        }
        
        console.log('⏹️ 跨设备同步已停止');
    }

    /**
     * 销毁同步管理器
     */
    destroy() {
        this.stopSync();
        this.syncQueue.clear();
        this.conflictQueue = [];
        this.pendingOperations = [];
        this.deviceRegistry.clear();
        this.isInitialized = false;
        
        console.log('🗑️ 跨设备数据同步管理器已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = CrossDeviceSyncManager;
} else if (typeof window !== 'undefined') {
    window.CrossDeviceSyncManager = CrossDeviceSyncManager;
}
