/**
 * Split-Second Spark - 安全数据清理工具
 * 用于清除可能泄漏用户信息的数据，确保新环境中的隐私安全
 */

class SecureDataCleaner {
    constructor() {
        this.cleanupTasks = [];
        console.log('🧹 安全数据清理工具已创建');
    }

    /**
     * 执行完整的数据清理
     * @param {Object} options - 清理选项
     * @returns {Promise<Object>} 清理结果
     */
    async performSecureCleanup(options = {}) {
        const results = {
            success: true,
            clearedItems: [],
            errors: [],
            timestamp: new Date().toISOString()
        };

        console.log('🧹 开始执行安全数据清理...');

        try {
            // 清理本地存储
            await this.clearLocalStorage(results);

            // 清理会话存储
            await this.clearSessionStorage(results);

            // 清理IndexedDB
            await this.clearIndexedDB(results);

            // 清理Cookies
            await this.clearCookies(results);

            // 清理缓存存储
            await this.clearCacheStorage(results);

            // 清理内存中的全局变量
            await this.clearGlobalVariables(results);

            console.log('✅ 安全数据清理完成');
            console.log('📊 清理结果:', results);

        } catch (error) {
            console.error('❌ 安全数据清理失败:', error);
            results.success = false;
            results.errors.push(error.message);
        }

        return results;
    }

    /**
     * 清理本地存储
     */
    async clearLocalStorage(results) {
        try {
            if (typeof localStorage !== 'undefined') {
                const keysToRemove = [];
                
                // 收集所有相关的键
                for (let i = 0; i < localStorage.length; i++) {
                    const key = localStorage.key(i);
                    if (this.isUserDataKey(key)) {
                        keysToRemove.push(key);
                    }
                }

                // 删除收集到的键
                keysToRemove.forEach(key => {
                    localStorage.removeItem(key);
                    results.clearedItems.push(`localStorage: ${key}`);
                });

                console.log(`🧹 清理了 ${keysToRemove.length} 个 localStorage 项目`);
            }
        } catch (error) {
            console.warn('⚠️ 清理 localStorage 时出现错误:', error);
            results.errors.push(`localStorage: ${error.message}`);
        }
    }

    /**
     * 清理会话存储
     */
    async clearSessionStorage(results) {
        try {
            if (typeof sessionStorage !== 'undefined') {
                const keysToRemove = [];
                
                // 收集所有相关的键
                for (let i = 0; i < sessionStorage.length; i++) {
                    const key = sessionStorage.key(i);
                    if (this.isUserDataKey(key)) {
                        keysToRemove.push(key);
                    }
                }

                // 删除收集到的键
                keysToRemove.forEach(key => {
                    sessionStorage.removeItem(key);
                    results.clearedItems.push(`sessionStorage: ${key}`);
                });

                console.log(`🧹 清理了 ${keysToRemove.length} 个 sessionStorage 项目`);
            }
        } catch (error) {
            console.warn('⚠️ 清理 sessionStorage 时出现错误:', error);
            results.errors.push(`sessionStorage: ${error.message}`);
        }
    }

    /**
     * 清理IndexedDB
     */
    async clearIndexedDB(results) {
        try {
            if ('indexedDB' in window) {
                // 获取所有数据库
                const databases = await indexedDB.databases();
                
                for (const dbInfo of databases) {
                    if (this.isUserDataDatabase(dbInfo.name)) {
                        await this.deleteDatabase(dbInfo.name);
                        results.clearedItems.push(`IndexedDB: ${dbInfo.name}`);
                    }
                }

                console.log(`🧹 清理了 ${databases.length} 个 IndexedDB 数据库`);
            }
        } catch (error) {
            console.warn('⚠️ 清理 IndexedDB 时出现错误:', error);
            results.errors.push(`IndexedDB: ${error.message}`);
        }
    }

    /**
     * 删除IndexedDB数据库
     */
    async deleteDatabase(dbName) {
        return new Promise((resolve, reject) => {
            const deleteRequest = indexedDB.deleteDatabase(dbName);
            
            deleteRequest.onsuccess = () => {
                console.log(`🗑️ 已删除数据库: ${dbName}`);
                resolve();
            };
            
            deleteRequest.onerror = () => {
                console.warn(`⚠️ 删除数据库失败: ${dbName}`);
                reject(deleteRequest.error);
            };
            
            deleteRequest.onblocked = () => {
                console.warn(`⚠️ 删除数据库被阻塞: ${dbName}`);
                // 等待一段时间后重试
                setTimeout(() => resolve(), 1000);
            };
        });
    }

    /**
     * 清理Cookies
     */
    async clearCookies(results) {
        try {
            if (typeof document !== 'undefined') {
                const cookies = document.cookie.split(';');
                let clearedCount = 0;

                cookies.forEach(cookie => {
                    const [name] = cookie.split('=');
                    const cookieName = name.trim();
                    
                    if (this.isUserDataCookie(cookieName)) {
                        // 删除cookie
                        document.cookie = `${cookieName}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;`;
                        results.clearedItems.push(`Cookie: ${cookieName}`);
                        clearedCount++;
                    }
                });

                console.log(`🧹 清理了 ${clearedCount} 个 Cookie`);
            }
        } catch (error) {
            console.warn('⚠️ 清理 Cookies 时出现错误:', error);
            results.errors.push(`Cookies: ${error.message}`);
        }
    }

    /**
     * 清理缓存存储
     */
    async clearCacheStorage(results) {
        try {
            if ('caches' in window) {
                const cacheNames = await caches.keys();
                let clearedCount = 0;

                for (const cacheName of cacheNames) {
                    if (this.isUserDataCache(cacheName)) {
                        await caches.delete(cacheName);
                        results.clearedItems.push(`Cache: ${cacheName}`);
                        clearedCount++;
                    }
                }

                console.log(`🧹 清理了 ${clearedCount} 个缓存存储`);
            }
        } catch (error) {
            console.warn('⚠️ 清理缓存存储时出现错误:', error);
            results.errors.push(`CacheStorage: ${error.message}`);
        }
    }

    /**
     * 清理全局变量
     */
    async clearGlobalVariables(results) {
        try {
            const globalVarsToClean = [
                'currentUser',
                'userCredential',
                'guest_user',
                'device_id',
                'user_session',
                'player_data'
            ];

            let clearedCount = 0;
            globalVarsToClean.forEach(varName => {
                if (window[varName]) {
                    delete window[varName];
                    results.clearedItems.push(`GlobalVar: ${varName}`);
                    clearedCount++;
                }
            });

            console.log(`🧹 清理了 ${clearedCount} 个全局变量`);
        } catch (error) {
            console.warn('⚠️ 清理全局变量时出现错误:', error);
            results.errors.push(`GlobalVariables: ${error.message}`);
        }
    }

    /**
     * 判断是否为用户数据相关的键
     */
    isUserDataKey(key) {
        if (!key) return false;
        
        const userDataPatterns = [
            /^sss_/,           // Split-Second Spark prefix
            /^tdw_/,           // Temporal Dream Weaver prefix
            /^qr_/,            // Quantum Resonance prefix
            /user/i,
            /player/i,
            /credential/i,
            /guest/i,
            /device_id/i,
            /current.*user/i,
            /system_currentUser/i,
            /system_userList/i
        ];

        return userDataPatterns.some(pattern => pattern.test(key));
    }

    /**
     * 判断是否为用户数据相关的数据库
     */
    isUserDataDatabase(dbName) {
        if (!dbName) return false;
        
        const userDataPatterns = [
            /SplitSecondSparkDB/i,
            /TemporalDreamWeaverDB/i,
            /QuantumResonanceDB/i,
            /user/i,
            /player/i,
            /game.*data/i
        ];

        return userDataPatterns.some(pattern => pattern.test(dbName));
    }

    /**
     * 判断是否为用户数据相关的Cookie
     */
    isUserDataCookie(cookieName) {
        if (!cookieName) return false;
        
        const userDataPatterns = [
            /user/i,
            /player/i,
            /session/i,
            /auth/i,
            /credential/i,
            /guest/i
        ];

        return userDataPatterns.some(pattern => pattern.test(cookieName));
    }

    /**
     * 判断是否为用户数据相关的缓存
     */
    isUserDataCache(cacheName) {
        if (!cacheName) return false;
        
        const userDataPatterns = [
            /user/i,
            /player/i,
            /game.*data/i,
            /split.*second.*spark/i
        ];

        return userDataPatterns.some(pattern => pattern.test(cacheName));
    }

    /**
     * 生成清理报告
     */
    generateCleanupReport(results) {
        const report = {
            summary: {
                success: results.success,
                totalItemsCleaned: results.clearedItems.length,
                totalErrors: results.errors.length,
                timestamp: results.timestamp
            },
            details: {
                clearedItems: results.clearedItems,
                errors: results.errors
            }
        };

        console.log('📋 数据清理报告:', report);
        return report;
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SecureDataCleaner;
} else {
    window.SecureDataCleaner = SecureDataCleaner;
}
