/**
 * 存储服务测试工具
 * 用于验证 EdgeOne 存储集成是否正常工作
 */

class StorageServiceTester {
    constructor() {
        this.testResults = [];
    }

    /**
     * 运行所有测试
     */
    async runAllTests() {
        console.log('🧪 开始存储服务集成测试...');
        
        this.testResults = [];
        
        // 测试 EdgeOne 存储检测
        await this.testEdgeOneDetection();
        
        // 测试存储服务初始化
        await this.testStorageServiceInit();
        
        // 测试存储操作
        await this.testStorageOperations();
        
        // 测试存储升级
        await this.testStorageUpgrade();
        
        // 输出测试结果
        this.printTestResults();
        
        return this.testResults;
    }

    /**
     * 测试 EdgeOne 存储检测
     */
    async testEdgeOneDetection() {
        const testName = 'EdgeOne 存储检测';
        console.log(`🔍 测试: ${testName}`);
        
        try {
            const hasEdgeOne = typeof window !== 'undefined' && window.edgeOneStorage;
            const isAvailable = hasEdgeOne && window.edgeOneStorage.isAvailable;
            const hasRequiredMethods = hasEdgeOne && 
                typeof window.edgeOneStorage.put === 'function' &&
                typeof window.edgeOneStorage.get === 'function' &&
                typeof window.edgeOneStorage.delete === 'function';
            
            this.addTestResult(testName, true, {
                hasEdgeOne,
                isAvailable,
                hasRequiredMethods,
                storageMode: hasEdgeOne ? window.edgeOneStorage.storageMode : 'N/A'
            });
            
            console.log(`✅ ${testName} 通过`);
        } catch (error) {
            this.addTestResult(testName, false, { error: error.message });
            console.error(`❌ ${testName} 失败:`, error);
        }
    }

    /**
     * 测试存储服务初始化
     */
    async testStorageServiceInit() {
        const testName = '存储服务初始化';
        console.log(`🔍 测试: ${testName}`);
        
        try {
            if (typeof storageService === 'undefined') {
                throw new Error('storageService 未定义');
            }
            
            // 获取初始化前的状态
            const beforeInit = {
                isInitialized: storageService.isInitialized,
                storageType: storageService.storageType
            };
            
            // 重新初始化存储服务
            await storageService.reinitialize();
            
            // 获取初始化后的状态
            const afterInit = {
                isInitialized: storageService.isInitialized,
                storageType: storageService.storageType
            };
            
            this.addTestResult(testName, true, {
                beforeInit,
                afterInit,
                storageInfo: storageService.getStorageInfo()
            });
            
            console.log(`✅ ${testName} 通过`);
        } catch (error) {
            this.addTestResult(testName, false, { error: error.message });
            console.error(`❌ ${testName} 失败:`, error);
        }
    }

    /**
     * 测试存储操作
     */
    async testStorageOperations() {
        const testName = '存储操作测试';
        console.log(`🔍 测试: ${testName}`);
        
        try {
            if (typeof storageService === 'undefined') {
                throw new Error('storageService 未定义');
            }
            
            const testKey = '__test_storage_operations__';
            const testValue = { 
                message: '存储测试数据', 
                timestamp: Date.now(),
                random: Math.random()
            };
            
            // 测试写入
            await storageService.put(testKey, testValue);
            console.log('📝 写入测试数据成功');
            
            // 测试读取
            const retrievedValue = await storageService.get(testKey);
            console.log('📖 读取测试数据成功');
            
            // 验证数据一致性
            const isDataConsistent = JSON.stringify(testValue) === JSON.stringify(retrievedValue);
            
            // 测试删除
            await storageService.delete(testKey);
            console.log('🗑️ 删除测试数据成功');
            
            // 验证删除
            const deletedValue = await storageService.get(testKey);
            const isDeleted = deletedValue === null;
            
            this.addTestResult(testName, isDataConsistent && isDeleted, {
                storageType: storageService.storageType,
                isDataConsistent,
                isDeleted,
                testValue,
                retrievedValue
            });
            
            console.log(`✅ ${testName} 通过`);
        } catch (error) {
            this.addTestResult(testName, false, { error: error.message });
            console.error(`❌ ${testName} 失败:`, error);
        }
    }

    /**
     * 测试存储升级
     */
    async testStorageUpgrade() {
        const testName = '存储升级测试';
        console.log(`🔍 测试: ${testName}`);
        
        try {
            if (typeof storageService === 'undefined') {
                throw new Error('storageService 未定义');
            }
            
            const canUpgrade = await storageService.checkForEdgeOneUpgrade();
            const currentStorageType = storageService.storageType;
            
            this.addTestResult(testName, true, {
                canUpgrade,
                currentStorageType,
                hasUpgradeMethod: typeof storageService.upgradeToEdgeOne === 'function'
            });
            
            console.log(`✅ ${testName} 通过`);
        } catch (error) {
            this.addTestResult(testName, false, { error: error.message });
            console.error(`❌ ${testName} 失败:`, error);
        }
    }

    /**
     * 添加测试结果
     */
    addTestResult(testName, passed, details = {}) {
        this.testResults.push({
            testName,
            passed,
            details,
            timestamp: new Date().toISOString()
        });
    }

    /**
     * 打印测试结果
     */
    printTestResults() {
        console.log('\n📊 存储服务测试结果汇总:');
        console.log('='.repeat(50));
        
        let passedCount = 0;
        let totalCount = this.testResults.length;
        
        this.testResults.forEach((result, index) => {
            const status = result.passed ? '✅ 通过' : '❌ 失败';
            console.log(`${index + 1}. ${result.testName}: ${status}`);
            
            if (result.passed) {
                passedCount++;
            }
            
            // 显示详细信息
            if (Object.keys(result.details).length > 0) {
                console.log('   详细信息:', JSON.stringify(result.details, null, 2));
            }
        });
        
        console.log('='.repeat(50));
        console.log(`📈 测试统计: ${passedCount}/${totalCount} 通过`);
        
        if (passedCount === totalCount) {
            console.log('🎉 所有测试通过！存储服务集成正常');
        } else {
            console.log('⚠️ 部分测试失败，请检查存储服务配置');
        }
    }

    /**
     * 获取存储状态报告
     */
    getStorageStatusReport() {
        const report = {
            timestamp: new Date().toISOString(),
            edgeOneStorage: {
                available: typeof window !== 'undefined' && !!window.edgeOneStorage,
                isAvailable: window.edgeOneStorage?.isAvailable,
                storageMode: window.edgeOneStorage?.storageMode,
                requestCount: window.edgeOneStorage?.requestCount
            },
            storageService: {
                available: typeof storageService !== 'undefined',
                isInitialized: storageService?.isInitialized,
                storageType: storageService?.storageType,
                storageInfo: storageService?.getStorageInfo()
            }
        };
        
        console.log('📋 存储状态报告:', JSON.stringify(report, null, 2));
        return report;
    }
}

// 创建全局测试实例
const storageServiceTester = new StorageServiceTester();

// 导出测试工具
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { StorageServiceTester, storageServiceTester };
} else {
    window.storageServiceTester = storageServiceTester;
}
