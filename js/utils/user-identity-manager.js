/**
 * Split-Second Spark - 用户身份管理系统
 * 确保玩家身份唯一性和跨设备数据同步
 */

/**
 * 用户身份管理器
 * 负责用户认证、身份验证和数据同步
 */
class UserIdentityManager {
    constructor(options = {}) {
        this.firebaseApp = null;
        this.auth = null;
        this.firestore = null;
        this.currentUser = null;
        this.isInitialized = false;
        
        // 配置选项
        this.options = {
            enableAnonymousAuth: options.enableAnonymousAuth !== false, // 默认启用匿名认证
            enableEmailAuth: options.enableEmailAuth || false,
            enableGuestMode: options.enableGuestMode !== false, // 默认启用访客模式
            autoUpgrade: options.autoUpgrade !== false, // 自动升级匿名用户
            dataEncryption: options.dataEncryption || false,
            ...options
        };
        
        // 用户数据缓存
        this.userDataCache = new Map();
        
        console.log('👤 用户身份管理器初始化中...', this.options);
    }

    /**
     * 初始化身份管理器
     */
    async init(firebaseConfig) {
        try {
            console.log('👤 初始化用户身份管理器...');
            
            // 动态导入 Firebase SDK
            const { initializeApp } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-app.js');
            const { getAuth, onAuthStateChanged, signInAnonymously, createUserWithEmailAndPassword, signInWithEmailAndPassword, linkWithCredential, EmailAuthProvider } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-auth.js');
            const { getFirestore, doc, setDoc, getDoc, updateDoc, collection, query, where, getDocs } = await import('https://www.gstatic.com/firebasejs/10.7.1/firebase-firestore.js');
            
            // 初始化 Firebase
            this.firebaseApp = initializeApp(firebaseConfig);
            this.auth = getAuth(this.firebaseApp);
            this.firestore = getFirestore(this.firebaseApp);
            
            // 保存 Firebase 方法引用
            this.firebaseMethods = {
                signInAnonymously,
                createUserWithEmailAndPassword,
                signInWithEmailAndPassword,
                linkWithCredential,
                EmailAuthProvider,
                doc,
                setDoc,
                getDoc,
                updateDoc,
                collection,
                query,
                where,
                getDocs
            };
            
            // 监听认证状态变化
            onAuthStateChanged(this.auth, (user) => {
                this.handleAuthStateChange(user);
            });
            
            this.isInitialized = true;
            console.log('✅ 用户身份管理器初始化完成');
            
            // 尝试自动登录
            await this.autoSignIn();
            
        } catch (error) {
            console.error('❌ 用户身份管理器初始化失败:', error);
            throw error;
        }
    }

    /**
     * 处理认证状态变化
     */
    async handleAuthStateChange(user) {
        console.log('👤 用户认证状态变化:', user ? '已登录' : '未登录');
        
        this.currentUser = user;
        
        if (user) {
            // 用户已登录，初始化用户数据
            await this.initializeUserData(user);
            
            // 触发用户登录事件
            this.dispatchEvent('userSignedIn', {
                user: this.getUserInfo(),
                isAnonymous: user.isAnonymous
            });
        } else {
            // 用户已登出
            this.userDataCache.clear();
            this.dispatchEvent('userSignedOut', {});
        }
    }

    /**
     * 自动登录
     */
    async autoSignIn() {
        try {
            if (this.currentUser) {
                console.log('👤 用户已登录:', this.currentUser.uid);
                return this.currentUser;
            }
            
            if (this.options.enableAnonymousAuth) {
                console.log('👤 尝试匿名登录...');
                const userCredential = await this.firebaseMethods.signInAnonymously(this.auth);
                console.log('✅ 匿名登录成功:', userCredential.user.uid);
                return userCredential.user;
            }
            
            if (this.options.enableGuestMode) {
                console.log('👤 启用访客模式');
                return this.createGuestUser();
            }
            
        } catch (error) {
            console.error('❌ 自动登录失败:', error);
            
            if (this.options.enableGuestMode) {
                console.log('👤 降级到访客模式');
                return this.createGuestUser();
            }
            
            throw error;
        }
    }

    /**
     * 创建访客用户
     */
    createGuestUser() {
        const guestId = this.generateGuestId();
        const guestUser = {
            uid: guestId,
            isAnonymous: true,
            isGuest: true,
            displayName: `访客_${guestId.slice(-6)}`,
            email: null,
            createdAt: new Date().toISOString()
        };
        
        // 保存到本地存储
        localStorage.setItem('guest_user', JSON.stringify(guestUser));
        
        this.currentUser = guestUser;
        console.log('👤 访客用户已创建:', guestUser.uid);
        
        return guestUser;
    }

    /**
     * 生成访客ID
     */
    generateGuestId() {
        const timestamp = Date.now().toString(36);
        const random = Math.random().toString(36).substr(2, 9);
        return `guest_${timestamp}_${random}`;
    }

    /**
     * 邮箱注册
     */
    async signUpWithEmail(email, password, displayName = null) {
        try {
            console.log('👤 邮箱注册中...', email);
            
            const userCredential = await this.firebaseMethods.createUserWithEmailAndPassword(this.auth, email, password);
            const user = userCredential.user;
            
            // 更新用户资料
            if (displayName) {
                await this.updateUserProfile({ displayName });
            }
            
            console.log('✅ 邮箱注册成功:', user.uid);
            return user;
            
        } catch (error) {
            console.error('❌ 邮箱注册失败:', error);
            throw this.handleAuthError(error);
        }
    }

    /**
     * 邮箱登录
     */
    async signInWithEmail(email, password) {
        try {
            console.log('👤 邮箱登录中...', email);
            
            const userCredential = await this.firebaseMethods.signInWithEmailAndPassword(this.auth, email, password);
            const user = userCredential.user;
            
            console.log('✅ 邮箱登录成功:', user.uid);
            return user;
            
        } catch (error) {
            console.error('❌ 邮箱登录失败:', error);
            throw this.handleAuthError(error);
        }
    }

    /**
     * 升级匿名用户到邮箱用户
     */
    async upgradeAnonymousUser(email, password, displayName = null) {
        try {
            if (!this.currentUser || !this.currentUser.isAnonymous) {
                throw new Error('当前用户不是匿名用户');
            }
            
            console.log('👤 升级匿名用户到邮箱用户...', email);
            
            // 创建邮箱凭证
            const credential = this.firebaseMethods.EmailAuthProvider.credential(email, password);
            
            // 链接凭证到当前匿名用户
            const userCredential = await this.firebaseMethods.linkWithCredential(this.currentUser, credential);
            const user = userCredential.user;
            
            // 更新用户资料
            if (displayName) {
                await this.updateUserProfile({ displayName });
            }
            
            console.log('✅ 匿名用户升级成功:', user.uid);
            return user;
            
        } catch (error) {
            console.error('❌ 匿名用户升级失败:', error);
            throw this.handleAuthError(error);
        }
    }

    /**
     * 初始化用户数据
     */
    async initializeUserData(user) {
        try {
            const userDocRef = this.firebaseMethods.doc(this.firestore, 'users', user.uid);
            const userDoc = await this.firebaseMethods.getDoc(userDocRef);
            
            let userData;
            
            if (!userDoc.exists()) {
                // 创建新用户数据
                userData = {
                    uid: user.uid,
                    email: user.email,
                    displayName: user.displayName || `玩家_${user.uid.slice(-6)}`,
                    isAnonymous: user.isAnonymous,
                    createdAt: new Date().toISOString(),
                    lastLoginAt: new Date().toISOString(),
                    gameData: {},
                    settings: {
                        language: 'zh-CN',
                        theme: 'default',
                        soundEnabled: true,
                        musicEnabled: true
                    },
                    statistics: {
                        totalPlayTime: 0,
                        gamesPlayed: 0,
                        highestScore: 0,
                        achievements: []
                    }
                };
                
                await this.firebaseMethods.setDoc(userDocRef, userData);
                console.log('👤 新用户数据已创建');
                
            } else {
                // 更新现有用户数据
                userData = userDoc.data();
                
                // 更新最后登录时间
                await this.firebaseMethods.updateDoc(userDocRef, {
                    lastLoginAt: new Date().toISOString()
                });
                
                console.log('👤 用户数据已加载');
            }
            
            // 缓存用户数据
            this.userDataCache.set(user.uid, userData);
            
            return userData;
            
        } catch (error) {
            console.error('❌ 用户数据初始化失败:', error);
            throw error;
        }
    }

    /**
     * 获取用户信息
     */
    getUserInfo() {
        if (!this.currentUser) {
            return null;
        }
        
        const cachedData = this.userDataCache.get(this.currentUser.uid);
        
        return {
            uid: this.currentUser.uid,
            email: this.currentUser.email,
            displayName: this.currentUser.displayName || cachedData?.displayName,
            isAnonymous: this.currentUser.isAnonymous,
            isGuest: this.currentUser.isGuest || false,
            createdAt: cachedData?.createdAt,
            lastLoginAt: cachedData?.lastLoginAt
        };
    }

    /**
     * 更新用户资料
     */
    async updateUserProfile(profileData) {
        try {
            if (!this.currentUser) {
                throw new Error('用户未登录');
            }
            
            const userDocRef = this.firebaseMethods.doc(this.firestore, 'users', this.currentUser.uid);
            
            const updateData = {
                ...profileData,
                updatedAt: new Date().toISOString()
            };
            
            await this.firebaseMethods.updateDoc(userDocRef, updateData);
            
            // 更新缓存
            const cachedData = this.userDataCache.get(this.currentUser.uid);
            if (cachedData) {
                Object.assign(cachedData, updateData);
            }
            
            console.log('✅ 用户资料已更新');
            
        } catch (error) {
            console.error('❌ 用户资料更新失败:', error);
            throw error;
        }
    }

    /**
     * 保存游戏数据
     */
    async saveGameData(gameId, gameData) {
        try {
            if (!this.currentUser) {
                throw new Error('用户未登录');
            }
            
            const userDocRef = this.firebaseMethods.doc(this.firestore, 'users', this.currentUser.uid);
            
            const updateData = {
                [`gameData.${gameId}`]: {
                    ...gameData,
                    lastSaved: new Date().toISOString()
                },
                updatedAt: new Date().toISOString()
            };
            
            await this.firebaseMethods.updateDoc(userDocRef, updateData);
            
            // 更新缓存
            const cachedData = this.userDataCache.get(this.currentUser.uid);
            if (cachedData) {
                if (!cachedData.gameData) {
                    cachedData.gameData = {};
                }
                cachedData.gameData[gameId] = updateData[`gameData.${gameId}`];
            }
            
            console.log(`✅ 游戏数据已保存 [${gameId}]`);
            
        } catch (error) {
            console.error(`❌ 游戏数据保存失败 [${gameId}]:`, error);
            throw error;
        }
    }

    /**
     * 加载游戏数据
     */
    async loadGameData(gameId) {
        try {
            if (!this.currentUser) {
                throw new Error('用户未登录');
            }
            
            // 先从缓存获取
            const cachedData = this.userDataCache.get(this.currentUser.uid);
            if (cachedData && cachedData.gameData && cachedData.gameData[gameId]) {
                console.log(`📥 从缓存加载游戏数据 [${gameId}]`);
                return cachedData.gameData[gameId];
            }
            
            // 从数据库获取
            const userDocRef = this.firebaseMethods.doc(this.firestore, 'users', this.currentUser.uid);
            const userDoc = await this.firebaseMethods.getDoc(userDocRef);
            
            if (userDoc.exists()) {
                const userData = userDoc.data();
                const gameData = userData.gameData?.[gameId] || null;
                
                console.log(`📥 从数据库加载游戏数据 [${gameId}]`);
                return gameData;
            }
            
            return null;
            
        } catch (error) {
            console.error(`❌ 游戏数据加载失败 [${gameId}]:`, error);
            throw error;
        }
    }

    /**
     * 检查用户名是否可用
     */
    async isUsernameAvailable(username) {
        try {
            const usersRef = this.firebaseMethods.collection(this.firestore, 'users');
            const q = this.firebaseMethods.query(usersRef, this.firebaseMethods.where('displayName', '==', username));
            const querySnapshot = await this.firebaseMethods.getDocs(q);
            
            return querySnapshot.empty;
            
        } catch (error) {
            console.error('❌ 用户名检查失败:', error);
            return false;
        }
    }

    /**
     * 生成唯一的用户标识符
     */
    generateUniqueUserId() {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substr(2, 9);
        const deviceId = this.getDeviceId();
        
        return `${timestamp}_${random}_${deviceId}`;
    }

    /**
     * 获取设备标识符
     */
    getDeviceId() {
        let deviceId = localStorage.getItem('device_id');
        
        if (!deviceId) {
            deviceId = 'dev_' + Math.random().toString(36).substr(2, 12);
            localStorage.setItem('device_id', deviceId);
        }
        
        return deviceId;
    }

    /**
     * 处理认证错误
     */
    handleAuthError(error) {
        const errorMessages = {
            'auth/email-already-in-use': '该邮箱已被使用',
            'auth/invalid-email': '邮箱格式无效',
            'auth/operation-not-allowed': '操作不被允许',
            'auth/weak-password': '密码强度不够',
            'auth/user-disabled': '用户账户已被禁用',
            'auth/user-not-found': '用户不存在',
            'auth/wrong-password': '密码错误',
            'auth/too-many-requests': '请求过于频繁，请稍后再试',
            'auth/network-request-failed': '网络连接失败'
        };
        
        const message = errorMessages[error.code] || error.message;
        
        return new Error(message);
    }

    /**
     * 事件分发
     */
    dispatchEvent(eventName, data) {
        const event = new CustomEvent(`userIdentity:${eventName}`, {
            detail: data
        });
        
        if (typeof window !== 'undefined') {
            window.dispatchEvent(event);
        }
        
        console.log(`📡 事件分发: ${eventName}`, data);
    }

    /**
     * 登出
     */
    async signOut() {
        try {
            if (this.auth && this.currentUser && !this.currentUser.isGuest) {
                await this.auth.signOut();
            } else {
                // 访客用户直接清理
                this.currentUser = null;
                this.userDataCache.clear();
                localStorage.removeItem('guest_user');
                this.dispatchEvent('userSignedOut', {});
            }
            
            console.log('👤 用户已登出');
            
        } catch (error) {
            console.error('❌ 登出失败:', error);
            throw error;
        }
    }

    /**
     * 获取当前用户状态
     */
    getCurrentUserStatus() {
        return {
            isSignedIn: !!this.currentUser,
            isAnonymous: this.currentUser?.isAnonymous || false,
            isGuest: this.currentUser?.isGuest || false,
            canUpgrade: this.currentUser?.isAnonymous && this.options.autoUpgrade,
            userInfo: this.getUserInfo()
        };
    }
}

// 导出用户身份管理器
if (typeof window !== 'undefined') {
    window.UserIdentityManager = UserIdentityManager;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = UserIdentityManager;
}
