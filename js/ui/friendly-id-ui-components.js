/**
 * Split-Second Spark - 用户友好标识符UI组件
 * 
 * 功能说明：
 * - 提供标识符显示和管理界面
 * - 支持标识符输入验证和格式化
 * - 实现设备管理和同步状态显示
 * - 提供冲突解决和历史记录界面
 * - 支持响应式设计和多语言
 */

/**
 * 用户友好标识符UI组件管理器
 */
class FriendlyIdUIComponents {
    constructor(friendlyIdManager, syncManager, options = {}) {
        this.friendlyIdManager = friendlyIdManager;
        this.syncManager = syncManager;
        
        // 配置选项
        this.options = {
            // UI选项
            theme: options.theme || 'modern',
            language: options.language || 'zh-CN',
            enableAnimations: options.enableAnimations !== false,
            
            // 显示选项
            showDeviceInfo: options.showDeviceInfo !== false,
            showSyncStatus: options.showSyncStatus !== false,
            showHistory: options.showHistory !== false,
            
            // 交互选项
            enableCopyToClipboard: options.enableCopyToClipboard !== false,
            enableQRCode: options.enableQRCode || false,
            enableSharing: options.enableSharing || false,
            
            ...options
        };

        // UI状态
        this.isInitialized = false;
        this.currentView = 'main';
        this.components = new Map();
        
        // 多语言文本
        this.texts = {
            'zh-CN': {
                title: '用户标识符',
                currentId: '当前标识符',
                generateNew: '生成新标识符',
                copyToClipboard: '复制到剪贴板',
                copied: '已复制',
                deviceManagement: '设备管理',
                syncStatus: '同步状态',
                history: '历史记录',
                conflicts: '冲突处理',
                settings: '设置',
                online: '在线',
                offline: '离线',
                syncing: '同步中',
                lastSync: '上次同步',
                never: '从未',
                devices: '设备',
                thisDevice: '当前设备',
                otherDevices: '其他设备',
                noHistory: '暂无历史记录',
                noConflicts: '暂无冲突',
                resolveConflict: '解决冲突',
                keepLocal: '保留本地',
                keepRemote: '保留远程',
                merge: '合并',
                cancel: '取消',
                confirm: '确认',
                error: '错误',
                success: '成功',
                warning: '警告',
                info: '信息'
            }
        };
        
        console.log('🎨 用户友好标识符UI组件初始化中...', this.options);
    }

    /**
     * 初始化UI组件
     */
    async init() {
        try {
            console.log('🚀 初始化用户友好标识符UI组件...');
            
            // 等待依赖组件初始化
            await this.waitForDependencies();
            
            // 创建主要UI组件
            this.createMainComponents();
            
            // 设置事件监听器
            this.setupEventListeners();
            
            // 初始化样式
            this.initializeStyles();
            
            this.isInitialized = true;
            console.log('✅ 用户友好标识符UI组件初始化完成');
            
        } catch (error) {
            console.error('❌ 用户友好标识符UI组件初始化失败:', error);
            throw error;
        }
    }

    /**
     * 等待依赖组件初始化
     */
    async waitForDependencies() {
        const maxWaitTime = 10000; // 10秒
        const startTime = Date.now();
        
        while (Date.now() - startTime < maxWaitTime) {
            if (this.friendlyIdManager.isInitialized) {
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error('依赖组件初始化超时');
    }

    /**
     * 创建主要UI组件
     */
    createMainComponents() {
        // 创建主容器
        this.createMainContainer();
        
        // 创建标识符显示组件
        this.createIdDisplayComponent();
        
        // 创建操作按钮组件
        this.createActionButtonsComponent();
        
        // 创建状态显示组件
        this.createStatusComponent();
        
        // 创建设备管理组件
        this.createDeviceManagementComponent();
        
        // 创建历史记录组件
        this.createHistoryComponent();
        
        // 创建冲突处理组件
        this.createConflictResolutionComponent();
    }

    /**
     * 创建主容器
     */
    createMainContainer() {
        const container = document.createElement('div');
        container.id = 'friendly-id-ui-container';
        container.className = `friendly-id-ui ${this.options.theme}`;
        
        container.innerHTML = `
            <div class="friendly-id-header">
                <h3 class="friendly-id-title">${this.getText('title')}</h3>
                <div class="friendly-id-status-indicator" id="friendly-id-status"></div>
            </div>
            <div class="friendly-id-content">
                <div class="friendly-id-main-view" id="friendly-id-main-view"></div>
                <div class="friendly-id-secondary-views" id="friendly-id-secondary-views"></div>
            </div>
            <div class="friendly-id-footer">
                <div class="friendly-id-navigation" id="friendly-id-navigation"></div>
            </div>
        `;
        
        this.components.set('container', container);
        
        // 如果页面中有指定的挂载点，则挂载到该位置
        const mountPoint = document.getElementById('friendly-id-mount-point') || document.body;
        mountPoint.appendChild(container);
    }

    /**
     * 创建标识符显示组件
     */
    createIdDisplayComponent() {
        const component = document.createElement('div');
        component.className = 'friendly-id-display';
        
        const currentId = this.friendlyIdManager.getCurrentId();
        const parsedId = currentId ? this.friendlyIdManager.parseId(currentId) : null;
        
        component.innerHTML = `
            <div class="friendly-id-current">
                <label class="friendly-id-label">${this.getText('currentId')}</label>
                <div class="friendly-id-value" id="friendly-id-current-value">
                    ${currentId || '未生成'}
                </div>
                ${parsedId ? `
                    <div class="friendly-id-breakdown">
                        <span class="friendly-id-part adjective">${parsedId.adjective}</span>
                        <span class="friendly-id-separator">-</span>
                        <span class="friendly-id-part noun">${parsedId.noun}</span>
                        <span class="friendly-id-separator">-</span>
                        <span class="friendly-id-part number">${parsedId.number}</span>
                    </div>
                ` : ''}
            </div>
        `;
        
        this.components.set('idDisplay', component);
        
        const mainView = document.getElementById('friendly-id-main-view');
        if (mainView) {
            mainView.appendChild(component);
        }
    }

    /**
     * 创建操作按钮组件
     */
    createActionButtonsComponent() {
        const component = document.createElement('div');
        component.className = 'friendly-id-actions';
        
        component.innerHTML = `
            <div class="friendly-id-button-group">
                <button class="friendly-id-button primary" id="friendly-id-generate-btn">
                    <span class="button-icon">🎲</span>
                    <span class="button-text">${this.getText('generateNew')}</span>
                </button>
                ${this.options.enableCopyToClipboard ? `
                    <button class="friendly-id-button secondary" id="friendly-id-copy-btn">
                        <span class="button-icon">📋</span>
                        <span class="button-text">${this.getText('copyToClipboard')}</span>
                    </button>
                ` : ''}
                ${this.options.enableQRCode ? `
                    <button class="friendly-id-button secondary" id="friendly-id-qr-btn">
                        <span class="button-icon">📱</span>
                        <span class="button-text">二维码</span>
                    </button>
                ` : ''}
            </div>
        `;
        
        this.components.set('actionButtons', component);
        
        const mainView = document.getElementById('friendly-id-main-view');
        if (mainView) {
            mainView.appendChild(component);
        }
        
        // 绑定事件
        this.bindActionButtonEvents(component);
    }

    /**
     * 创建状态显示组件
     */
    createStatusComponent() {
        const component = document.createElement('div');
        component.className = 'friendly-id-status-panel';
        
        const syncStats = this.syncManager ? this.syncManager.getStats() : null;
        
        component.innerHTML = `
            <div class="status-grid">
                ${this.options.showSyncStatus ? `
                    <div class="status-item sync-status">
                        <div class="status-icon" id="sync-status-icon">
                            ${syncStats?.isOnline ? '🌐' : '📴'}
                        </div>
                        <div class="status-info">
                            <div class="status-label">${this.getText('syncStatus')}</div>
                            <div class="status-value" id="sync-status-value">
                                ${syncStats?.isOnline ? this.getText('online') : this.getText('offline')}
                            </div>
                        </div>
                    </div>
                ` : ''}
                ${this.options.showDeviceInfo ? `
                    <div class="status-item device-info">
                        <div class="status-icon">📱</div>
                        <div class="status-info">
                            <div class="status-label">${this.getText('devices')}</div>
                            <div class="status-value" id="device-count-value">
                                ${syncStats?.deviceCount || 1}
                            </div>
                        </div>
                    </div>
                ` : ''}
                <div class="status-item last-sync">
                    <div class="status-icon">🕒</div>
                    <div class="status-info">
                        <div class="status-label">${this.getText('lastSync')}</div>
                        <div class="status-value" id="last-sync-value">
                            ${this.formatLastSyncTime(syncStats?.lastSyncTime)}
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        this.components.set('status', component);
        
        const mainView = document.getElementById('friendly-id-main-view');
        if (mainView) {
            mainView.appendChild(component);
        }
    }

    /**
     * 创建设备管理组件
     */
    createDeviceManagementComponent() {
        const component = document.createElement('div');
        component.className = 'friendly-id-device-management';
        component.style.display = 'none';
        
        component.innerHTML = `
            <div class="device-management-header">
                <h4>${this.getText('deviceManagement')}</h4>
                <button class="close-btn" id="device-management-close">×</button>
            </div>
            <div class="device-list" id="device-list">
                <div class="device-item current-device">
                    <div class="device-icon">📱</div>
                    <div class="device-info">
                        <div class="device-name">${this.getText('thisDevice')}</div>
                        <div class="device-details">当前设备</div>
                    </div>
                    <div class="device-status online">●</div>
                </div>
            </div>
        `;
        
        this.components.set('deviceManagement', component);
        
        const secondaryViews = document.getElementById('friendly-id-secondary-views');
        if (secondaryViews) {
            secondaryViews.appendChild(component);
        }
    }

    /**
     * 创建历史记录组件
     */
    createHistoryComponent() {
        const component = document.createElement('div');
        component.className = 'friendly-id-history';
        component.style.display = 'none';
        
        const history = this.friendlyIdManager.getIdHistory();
        
        component.innerHTML = `
            <div class="history-header">
                <h4>${this.getText('history')}</h4>
                <button class="close-btn" id="history-close">×</button>
            </div>
            <div class="history-list" id="history-list">
                ${history.length > 0 ? 
                    history.map(entry => `
                        <div class="history-item">
                            <div class="history-id">${entry.id}</div>
                            <div class="history-date">${this.formatDate(entry.replacedAt)}</div>
                        </div>
                    `).join('') :
                    `<div class="empty-state">${this.getText('noHistory')}</div>`
                }
            </div>
        `;
        
        this.components.set('history', component);
        
        const secondaryViews = document.getElementById('friendly-id-secondary-views');
        if (secondaryViews) {
            secondaryViews.appendChild(component);
        }
    }

    /**
     * 创建冲突处理组件
     */
    createConflictResolutionComponent() {
        const component = document.createElement('div');
        component.className = 'friendly-id-conflicts';
        component.style.display = 'none';
        
        component.innerHTML = `
            <div class="conflicts-header">
                <h4>${this.getText('conflicts')}</h4>
                <button class="close-btn" id="conflicts-close">×</button>
            </div>
            <div class="conflicts-list" id="conflicts-list">
                <div class="empty-state">${this.getText('noConflicts')}</div>
            </div>
        `;
        
        this.components.set('conflicts', component);
        
        const secondaryViews = document.getElementById('friendly-id-secondary-views');
        if (secondaryViews) {
            secondaryViews.appendChild(component);
        }
    }

    /**
     * 绑定操作按钮事件
     */
    bindActionButtonEvents(component) {
        // 生成新标识符按钮
        const generateBtn = component.querySelector('#friendly-id-generate-btn');
        if (generateBtn) {
            generateBtn.addEventListener('click', async () => {
                await this.handleGenerateNewId();
            });
        }
        
        // 复制到剪贴板按钮
        const copyBtn = component.querySelector('#friendly-id-copy-btn');
        if (copyBtn) {
            copyBtn.addEventListener('click', () => {
                this.handleCopyToClipboard();
            });
        }
        
        // 二维码按钮
        const qrBtn = component.querySelector('#friendly-id-qr-btn');
        if (qrBtn) {
            qrBtn.addEventListener('click', () => {
                this.handleShowQRCode();
            });
        }
    }

    /**
     * 处理生成新标识符
     */
    async handleGenerateNewId() {
        try {
            const generateBtn = document.getElementById('friendly-id-generate-btn');
            if (generateBtn) {
                generateBtn.disabled = true;
                generateBtn.innerHTML = '<span class="button-icon">⏳</span><span class="button-text">生成中...</span>';
            }
            
            // 生成新标识符
            const newId = await this.friendlyIdManager.generateNewId();
            
            // 更新显示
            this.updateIdDisplay(newId);
            
            // 显示成功消息
            this.showNotification(this.getText('success'), '新标识符生成成功', 'success');
            
        } catch (error) {
            console.error('❌ 生成新标识符失败:', error);
            this.showNotification(this.getText('error'), '生成新标识符失败', 'error');
        } finally {
            const generateBtn = document.getElementById('friendly-id-generate-btn');
            if (generateBtn) {
                generateBtn.disabled = false;
                generateBtn.innerHTML = `<span class="button-icon">🎲</span><span class="button-text">${this.getText('generateNew')}</span>`;
            }
        }
    }

    /**
     * 处理复制到剪贴板
     */
    async handleCopyToClipboard() {
        try {
            const currentId = this.friendlyIdManager.getCurrentId();
            if (!currentId) {
                this.showNotification(this.getText('warning'), '没有可复制的标识符', 'warning');
                return;
            }
            
            await navigator.clipboard.writeText(currentId);
            
            // 更新按钮状态
            const copyBtn = document.getElementById('friendly-id-copy-btn');
            if (copyBtn) {
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = `<span class="button-icon">✅</span><span class="button-text">${this.getText('copied')}</span>`;
                
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                }, 2000);
            }
            
            this.showNotification(this.getText('success'), '标识符已复制到剪贴板', 'success');
            
        } catch (error) {
            console.error('❌ 复制到剪贴板失败:', error);
            this.showNotification(this.getText('error'), '复制失败', 'error');
        }
    }

    /**
     * 处理显示二维码
     */
    handleShowQRCode() {
        const currentId = this.friendlyIdManager.getCurrentId();
        if (!currentId) {
            this.showNotification(this.getText('warning'), '没有可显示的标识符', 'warning');
            return;
        }
        
        // 这里可以集成二维码生成库
        console.log('🔲 显示二维码:', currentId);
        this.showNotification(this.getText('info'), '二维码功能开发中', 'info');
    }

    /**
     * 更新标识符显示
     */
    updateIdDisplay(newId) {
        const valueElement = document.getElementById('friendly-id-current-value');
        if (valueElement) {
            valueElement.textContent = newId;
        }
        
        // 更新分解显示
        const parsedId = this.friendlyIdManager.parseId(newId);
        const displayComponent = this.components.get('idDisplay');
        if (displayComponent && parsedId) {
            const breakdownElement = displayComponent.querySelector('.friendly-id-breakdown');
            if (breakdownElement) {
                breakdownElement.innerHTML = `
                    <span class="friendly-id-part adjective">${parsedId.adjective}</span>
                    <span class="friendly-id-separator">-</span>
                    <span class="friendly-id-part noun">${parsedId.noun}</span>
                    <span class="friendly-id-separator">-</span>
                    <span class="friendly-id-part number">${parsedId.number}</span>
                `;
            }
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听标识符变更事件
        window.addEventListener('friendlyIdManager:idChanged', (event) => {
            this.updateIdDisplay(event.detail.newId);
        });
        
        // 监听同步状态变更事件
        window.addEventListener('crossDeviceSync:statusChanged', (event) => {
            this.updateSyncStatus(event.detail);
        });
        
        // 监听冲突事件
        window.addEventListener('crossDeviceSync:conflict', (event) => {
            this.handleConflictNotification(event.detail);
        });
    }

    /**
     * 更新同步状态
     */
    updateSyncStatus(statusDetail) {
        const statusIcon = document.getElementById('sync-status-icon');
        const statusValue = document.getElementById('sync-status-value');
        
        if (statusIcon && statusValue) {
            if (statusDetail.isOnline) {
                statusIcon.textContent = statusDetail.isSyncing ? '🔄' : '🌐';
                statusValue.textContent = statusDetail.isSyncing ? this.getText('syncing') : this.getText('online');
            } else {
                statusIcon.textContent = '📴';
                statusValue.textContent = this.getText('offline');
            }
        }
    }

    /**
     * 处理冲突通知
     */
    handleConflictNotification(conflictDetail) {
        this.showNotification(
            this.getText('warning'),
            `检测到数据冲突: ${conflictDetail.conflict.key}`,
            'warning'
        );
    }

    /**
     * 显示通知
     */
    showNotification(title, message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `friendly-id-notification ${type}`;
        notification.innerHTML = `
            <div class="notification-icon">
                ${type === 'success' ? '✅' : 
                  type === 'error' ? '❌' : 
                  type === 'warning' ? '⚠️' : 'ℹ️'}
            </div>
            <div class="notification-content">
                <div class="notification-title">${title}</div>
                <div class="notification-message">${message}</div>
            </div>
            <button class="notification-close">×</button>
        `;
        
        // 添加到页面
        document.body.appendChild(notification);
        
        // 自动关闭
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 5000);
        
        // 点击关闭
        const closeBtn = notification.querySelector('.notification-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            });
        }
    }

    /**
     * 初始化样式
     */
    initializeStyles() {
        if (document.getElementById('friendly-id-ui-styles')) {
            return; // 样式已存在
        }
        
        const style = document.createElement('style');
        style.id = 'friendly-id-ui-styles';
        style.textContent = this.getUIStyles();
        document.head.appendChild(style);
    }

    /**
     * 获取UI样式
     */
    getUIStyles() {
        return `
            .friendly-id-ui {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                background: #ffffff;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
                padding: 20px;
                max-width: 500px;
                margin: 20px auto;
            }
            
            .friendly-id-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 20px;
                padding-bottom: 15px;
                border-bottom: 1px solid #e5e7eb;
            }
            
            .friendly-id-title {
                margin: 0;
                color: #1f2937;
                font-size: 18px;
                font-weight: 600;
            }
            
            .friendly-id-display {
                margin-bottom: 20px;
            }
            
            .friendly-id-label {
                display: block;
                font-size: 14px;
                color: #6b7280;
                margin-bottom: 8px;
            }
            
            .friendly-id-value {
                font-size: 20px;
                font-weight: 600;
                color: #1f2937;
                font-family: 'Monaco', 'Menlo', monospace;
                background: #f9fafb;
                padding: 12px 16px;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
                margin-bottom: 10px;
            }
            
            .friendly-id-breakdown {
                display: flex;
                align-items: center;
                gap: 4px;
                font-family: 'Monaco', 'Menlo', monospace;
            }
            
            .friendly-id-part {
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 14px;
                font-weight: 500;
            }
            
            .friendly-id-part.adjective {
                background: #dbeafe;
                color: #1e40af;
            }
            
            .friendly-id-part.noun {
                background: #dcfce7;
                color: #166534;
            }
            
            .friendly-id-part.number {
                background: #fef3c7;
                color: #92400e;
            }
            
            .friendly-id-separator {
                color: #9ca3af;
                font-weight: 400;
            }
            
            .friendly-id-button-group {
                display: flex;
                gap: 10px;
                margin-bottom: 20px;
            }
            
            .friendly-id-button {
                display: flex;
                align-items: center;
                gap: 8px;
                padding: 10px 16px;
                border: none;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s ease;
                flex: 1;
                justify-content: center;
            }
            
            .friendly-id-button.primary {
                background: #3b82f6;
                color: white;
            }
            
            .friendly-id-button.primary:hover {
                background: #2563eb;
            }
            
            .friendly-id-button.secondary {
                background: #f3f4f6;
                color: #374151;
                border: 1px solid #d1d5db;
            }
            
            .friendly-id-button.secondary:hover {
                background: #e5e7eb;
            }
            
            .friendly-id-button:disabled {
                opacity: 0.6;
                cursor: not-allowed;
            }
            
            .status-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
                gap: 15px;
                margin-bottom: 20px;
            }
            
            .status-item {
                display: flex;
                align-items: center;
                gap: 10px;
                padding: 12px;
                background: #f9fafb;
                border-radius: 8px;
                border: 1px solid #e5e7eb;
            }
            
            .status-icon {
                font-size: 20px;
            }
            
            .status-label {
                font-size: 12px;
                color: #6b7280;
                margin-bottom: 2px;
            }
            
            .status-value {
                font-size: 14px;
                font-weight: 600;
                color: #1f2937;
            }
            
            .friendly-id-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                display: flex;
                align-items: center;
                gap: 12px;
                padding: 16px;
                background: white;
                border-radius: 8px;
                box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
                border-left: 4px solid #3b82f6;
                max-width: 400px;
                z-index: 1000;
                animation: slideIn 0.3s ease;
            }
            
            .friendly-id-notification.success {
                border-left-color: #10b981;
            }
            
            .friendly-id-notification.error {
                border-left-color: #ef4444;
            }
            
            .friendly-id-notification.warning {
                border-left-color: #f59e0b;
            }
            
            .notification-icon {
                font-size: 20px;
            }
            
            .notification-title {
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 4px;
            }
            
            .notification-message {
                font-size: 14px;
                color: #6b7280;
            }
            
            .notification-close {
                background: none;
                border: none;
                font-size: 18px;
                color: #9ca3af;
                cursor: pointer;
                padding: 0;
                margin-left: auto;
            }
            
            @keyframes slideIn {
                from {
                    transform: translateX(100%);
                    opacity: 0;
                }
                to {
                    transform: translateX(0);
                    opacity: 1;
                }
            }
            
            @media (max-width: 640px) {
                .friendly-id-ui {
                    margin: 10px;
                    padding: 15px;
                }
                
                .friendly-id-button-group {
                    flex-direction: column;
                }
                
                .status-grid {
                    grid-template-columns: 1fr;
                }
                
                .friendly-id-notification {
                    left: 10px;
                    right: 10px;
                    max-width: none;
                }
            }
        `;
    }

    /**
     * 获取文本
     */
    getText(key) {
        return this.texts[this.options.language]?.[key] || key;
    }

    /**
     * 格式化日期
     */
    formatDate(dateString) {
        if (!dateString) return this.getText('never');
        
        const date = new Date(dateString);
        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);
        
        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        if (diffDays < 7) return `${diffDays}天前`;
        
        return date.toLocaleDateString('zh-CN');
    }

    /**
     * 格式化上次同步时间
     */
    formatLastSyncTime(lastSyncTime) {
        return this.formatDate(lastSyncTime);
    }

    /**
     * 销毁UI组件
     */
    destroy() {
        // 移除所有组件
        this.components.forEach(component => {
            if (component.parentNode) {
                component.parentNode.removeChild(component);
            }
        });
        
        this.components.clear();
        this.isInitialized = false;
        
        console.log('🗑️ 用户友好标识符UI组件已销毁');
    }
}

// 导出类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = FriendlyIdUIComponents;
} else if (typeof window !== 'undefined') {
    window.FriendlyIdUIComponents = FriendlyIdUIComponents;
}
