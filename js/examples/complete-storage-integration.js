/**
 * Split-Second Spark - 完整存储系统集成示例
 * 展示如何集成用户身份管理、云存储和本地存储
 */

/**
 * 完整的存储系统管理器
 * 统一管理用户认证、数据存储和同步
 */
class CompleteStorageSystem {
    constructor(firebaseConfig, options = {}) {
        this.firebaseConfig = firebaseConfig;
        this.options = {
            enableGuestMode: true,
            enableAnonymousAuth: true,
            enableEmailAuth: true,
            autoUpgrade: true,
            dataEncryption: false,
            compressionEnabled: true,
            enableDataVersioning: true,
            syncInterval: 5 * 60 * 1000, // 5分钟
            ...options
        };
        
        // 核心组件
        this.userManager = null;
        this.cloudStorage = null;
        this.localStorage = null;
        this.hybridStorage = null;
        this.currentStorage = null;
        
        // 状态管理
        this.isInitialized = false;
        this.currentUser = null;
        this.storageMode = 'local'; // 'local' | 'cloud' | 'hybrid'
        
        console.log('🚀 完整存储系统初始化中...', this.options);
    }

    /**
     * 初始化完整存储系统
     */
    async init() {
        try {
            console.log('🔧 开始初始化存储系统组件...');
            
            // 1. 初始化用户身份管理器
            await this.initUserManager();
            
            // 2. 初始化存储适配器
            await this.initStorageAdapters();
            
            // 3. 设置事件监听器
            this.setupEventListeners();
            
            // 4. 自动登录
            await this.autoSignIn();
            
            this.isInitialized = true;
            console.log('✅ 完整存储系统初始化完成');
            
            // 触发初始化完成事件
            this.dispatchEvent('storageSystem:initialized', {
                storageMode: this.storageMode,
                user: this.currentUser
            });
            
        } catch (error) {
            console.error('❌ 存储系统初始化失败:', error);
            throw error;
        }
    }

    /**
     * 初始化用户身份管理器
     */
    async initUserManager() {
        try {
            // 动态导入用户身份管理器
            const { default: UserIdentityManager } = await import('../utils/user-identity-manager.js');
            
            this.userManager = new UserIdentityManager({
                enableAnonymousAuth: this.options.enableAnonymousAuth,
                enableEmailAuth: this.options.enableEmailAuth,
                enableGuestMode: this.options.enableGuestMode,
                autoUpgrade: this.options.autoUpgrade
            });
            
            await this.userManager.init(this.firebaseConfig);
            console.log('✅ 用户身份管理器初始化完成');
            
        } catch (error) {
            console.warn('⚠️ 用户身份管理器初始化失败，使用本地模式:', error.message);
            this.storageMode = 'local';
        }
    }

    /**
     * 初始化存储适配器
     */
    async initStorageAdapters() {
        try {
            // 导入存储适配器
            const { default: UnifiedStorageService } = await import('../utils/unified-storage.js');
            const { default: UserCloudStorageAdapter } = await import('../utils/user-cloud-storage.js');
            const { FirebaseStorageAdapter, HybridStorageAdapter } = await import('../utils/cloud-storage-adapter.js');
            
            // 1. 本地存储（始终可用）
            this.localStorage = new UnifiedStorageService();
            await this.localStorage.init();
            
            if (this.userManager && this.userManager.isInitialized) {
                // 2. 用户云存储（推荐）
                this.cloudStorage = new UserCloudStorageAdapter(this.userManager, {
                    dataEncryption: this.options.dataEncryption,
                    compressionEnabled: this.options.compressionEnabled,
                    enableDataVersioning: this.options.enableDataVersioning
                });
                await this.cloudStorage.init();
                
                // 3. 混合存储
                const legacyCloudAdapter = new FirebaseStorageAdapter(this.firebaseConfig, this.userManager);
                await legacyCloudAdapter.init();
                
                this.hybridStorage = new HybridStorageAdapter(
                    this.localStorage, 
                    legacyCloudAdapter, 
                    this.userManager
                );
                await this.hybridStorage.init();
                
                // 默认使用云存储
                this.currentStorage = this.cloudStorage;
                this.storageMode = 'cloud';
                
            } else {
                // 仅本地存储
                this.currentStorage = this.localStorage;
                this.storageMode = 'local';
            }
            
            console.log(`✅ 存储适配器初始化完成，当前模式: ${this.storageMode}`);
            
        } catch (error) {
            console.error('❌ 存储适配器初始化失败:', error);
            // 降级到本地存储
            this.currentStorage = this.localStorage;
            this.storageMode = 'local';
        }
    }

    /**
     * 设置事件监听器
     */
    setupEventListeners() {
        // 监听用户登录事件
        window.addEventListener('userIdentity:userSignedIn', (event) => {
            const { user, isAnonymous } = event.detail;
            this.currentUser = user;
            
            // 切换到云存储模式
            if (this.cloudStorage) {
                this.currentStorage = this.cloudStorage;
                this.storageMode = 'cloud';
            }
            
            console.log(`👤 用户已登录，切换到${this.storageMode}模式:`, user.uid);
            
            // 触发存储模式变更事件
            this.dispatchEvent('storageSystem:modeChanged', {
                storageMode: this.storageMode,
                user: this.currentUser
            });
        });

        // 监听用户登出事件
        window.addEventListener('userIdentity:userSignedOut', () => {
            this.currentUser = null;
            
            // 切换到本地存储模式
            this.currentStorage = this.localStorage;
            this.storageMode = 'local';
            
            console.log('👤 用户已登出，切换到本地模式');
            
            // 触发存储模式变更事件
            this.dispatchEvent('storageSystem:modeChanged', {
                storageMode: this.storageMode,
                user: null
            });
        });
    }

    /**
     * 自动登录
     */
    async autoSignIn() {
        if (!this.userManager) return;
        
        try {
            const user = await this.userManager.autoSignIn();
            if (user) {
                this.currentUser = user;
                console.log('🔐 自动登录成功:', user.uid);
            }
        } catch (error) {
            console.warn('⚠️ 自动登录失败:', error.message);
        }
    }

    /**
     * 用户认证方法
     */
    async signInAsGuest() {
        if (!this.userManager) {
            throw new Error('用户管理器未初始化');
        }
        
        const user = this.userManager.createGuestUser();
        this.currentUser = user;
        
        // 访客模式使用本地存储
        this.currentStorage = this.localStorage;
        this.storageMode = 'local';
        
        return user;
    }

    async signInAnonymously() {
        if (!this.userManager) {
            throw new Error('用户管理器未初始化');
        }
        
        return await this.userManager.autoSignIn();
    }

    async signUpWithEmail(email, password, displayName) {
        if (!this.userManager) {
            throw new Error('用户管理器未初始化');
        }
        
        return await this.userManager.signUpWithEmail(email, password, displayName);
    }

    async signInWithEmail(email, password) {
        if (!this.userManager) {
            throw new Error('用户管理器未初始化');
        }
        
        return await this.userManager.signInWithEmail(email, password);
    }

    async upgradeAccount(email, password, displayName) {
        if (!this.userManager) {
            throw new Error('用户管理器未初始化');
        }
        
        return await this.userManager.upgradeAnonymousUser(email, password, displayName);
    }

    async signOut() {
        if (!this.userManager) {
            throw new Error('用户管理器未初始化');
        }
        
        return await this.userManager.signOut();
    }

    /**
     * 数据操作方法（统一接口）
     */
    async saveData(key, value) {
        if (!this.currentStorage) {
            throw new Error('存储系统未初始化');
        }
        
        try {
            await this.currentStorage.put(key, value);
            
            // 触发数据保存事件
            this.dispatchEvent('storageSystem:dataSaved', {
                key,
                value,
                storageMode: this.storageMode
            });
            
            return true;
        } catch (error) {
            console.error(`❌ 数据保存失败 [${key}]:`, error);
            throw error;
        }
    }

    async loadData(key) {
        if (!this.currentStorage) {
            throw new Error('存储系统未初始化');
        }
        
        try {
            const value = await this.currentStorage.get(key);
            
            // 触发数据加载事件
            this.dispatchEvent('storageSystem:dataLoaded', {
                key,
                value,
                storageMode: this.storageMode
            });
            
            return value;
        } catch (error) {
            console.error(`❌ 数据加载失败 [${key}]:`, error);
            throw error;
        }
    }

    async deleteData(key) {
        if (!this.currentStorage) {
            throw new Error('存储系统未初始化');
        }
        
        try {
            await this.currentStorage.delete(key);
            
            // 触发数据删除事件
            this.dispatchEvent('storageSystem:dataDeleted', {
                key,
                storageMode: this.storageMode
            });
            
            return true;
        } catch (error) {
            console.error(`❌ 数据删除失败 [${key}]:`, error);
            throw error;
        }
    }

    async listData(prefix = '') {
        if (!this.currentStorage) {
            throw new Error('存储系统未初始化');
        }
        
        return await this.currentStorage.list(prefix);
    }

    /**
     * 存储模式切换
     */
    async switchStorageMode(mode) {
        if (!this.isInitialized) {
            throw new Error('存储系统未初始化');
        }
        
        const previousMode = this.storageMode;
        
        switch (mode) {
            case 'local':
                this.currentStorage = this.localStorage;
                break;
            case 'cloud':
                if (!this.cloudStorage) {
                    throw new Error('云存储不可用');
                }
                this.currentStorage = this.cloudStorage;
                break;
            case 'hybrid':
                if (!this.hybridStorage) {
                    throw new Error('混合存储不可用');
                }
                this.currentStorage = this.hybridStorage;
                break;
            default:
                throw new Error(`不支持的存储模式: ${mode}`);
        }
        
        this.storageMode = mode;
        
        console.log(`🔄 存储模式已切换: ${previousMode} → ${mode}`);
        
        // 触发模式切换事件
        this.dispatchEvent('storageSystem:modeChanged', {
            previousMode,
            currentMode: mode,
            user: this.currentUser
        });
    }

    /**
     * 获取系统状态
     */
    getSystemStatus() {
        return {
            isInitialized: this.isInitialized,
            storageMode: this.storageMode,
            currentUser: this.currentUser ? {
                uid: this.currentUser.uid,
                displayName: this.currentUser.displayName,
                email: this.currentUser.email,
                isAnonymous: this.currentUser.isAnonymous,
                isGuest: this.currentUser.isGuest
            } : null,
            availableStorageModes: {
                local: !!this.localStorage,
                cloud: !!this.cloudStorage,
                hybrid: !!this.hybridStorage
            },
            userManager: {
                available: !!this.userManager,
                initialized: this.userManager?.isInitialized || false
            }
        };
    }

    /**
     * 获取存储统计信息
     */
    async getStorageStats() {
        if (!this.currentStorage) {
            return { error: '存储系统未初始化' };
        }
        
        try {
            const stats = await this.currentStorage.getStorageInfo();
            return {
                ...stats,
                systemStatus: this.getSystemStatus()
            };
        } catch (error) {
            return {
                error: error.message,
                systemStatus: this.getSystemStatus()
            };
        }
    }

    /**
     * 触发自定义事件
     */
    dispatchEvent(eventName, detail) {
        const event = new CustomEvent(eventName, { detail });
        window.dispatchEvent(event);
    }

    /**
     * 清理资源
     */
    destroy() {
        // 停止自动同步
        if (this.hybridStorage && this.hybridStorage.stopAutoSync) {
            this.hybridStorage.stopAutoSync();
        }
        
        // 清理引用
        this.userManager = null;
        this.cloudStorage = null;
        this.localStorage = null;
        this.hybridStorage = null;
        this.currentStorage = null;
        
        this.isInitialized = false;
        console.log('🧹 存储系统资源已清理');
    }
}

// 导出完整存储系统
if (typeof window !== 'undefined') {
    window.CompleteStorageSystem = CompleteStorageSystem;
}

if (typeof module !== 'undefined' && module.exports) {
    module.exports = CompleteStorageSystem;
}
