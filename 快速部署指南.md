# Split-Second Spark 快速部署指南

> 🚀 5分钟内完成静态部署的完整指南

## 📋 部署前准备

### 系统要求
- **Python 3.6+** (用于运行打包脚本)
- **现代浏览器** (Chrome 60+, Firefox 55+, Safari 12+, Edge 79+)
- **Web服务器** (任何静态文件服务器)

### 项目概述
Split-Second Spark 是一个纯前端游戏集合，包含：
- 🕰️ **时空织梦者** - 时间操控解谜游戏
- ⚡ **瞬光捕手** - 反应速度挑战游戏
- 🌌 **量子共鸣者** - 音乐节奏物理模拟游戏

## 🎯 一键部署方案

### Windows 用户
```bash
# 1. 双击运行打包脚本
build-static.bat

# 2. 按提示操作，自动生成部署包
# 3. 将生成的 split-second-spark-static.zip 上传到服务器
# 4. 解压并访问 index.html
```

### Linux/Mac 用户
```bash
# 1. 给脚本执行权限
chmod +x build-static.sh

# 2. 运行打包脚本
./build-static.sh

# 3. 按提示操作，自动生成部署包
# 4. 将生成的 split-second-spark-static.zip 上传到服务器
# 5. 解压并访问 index.html
```

### 手动打包 (所有平台)
```bash
# 运行Python打包脚本
python build-static.py

# 或者使用Python 3
python3 build-static.py
```

## 📦 部署包内容

打包后的 `dist/` 目录包含：
```
dist/
├── index.html              # 主入口页面
├── manifest.json           # PWA配置
├── deployment-info.json    # 部署信息
├── README.md              # 部署说明
├── verify-deployment.html  # 部署验证工具
├── styles/                # 样式文件
├── js/                    # JavaScript文件
├── assets/                # 静态资源
├── 时空织梦者/            # 游戏1
├── 瞬光捕手/              # 游戏2
└── 量子共鸣者/            # 游戏3
```

## 🌐 部署到不同平台

### 1. GitHub Pages
```bash
# 方法一：直接推送
git add dist/
git commit -m "Deploy to GitHub Pages"
git push origin main
# 在仓库设置中启用 Pages，选择 main 分支的 /dist 文件夹

# 方法二：使用 gh-pages 分支
git subtree push --prefix dist origin gh-pages
```

### 2. Vercel
```bash
# 安装 Vercel CLI
npm i -g vercel

# 部署 dist 目录
cd dist
vercel --prod
```

### 3. Netlify
```bash
# 方法一：拖拽部署
# 直接将 dist 文件夹拖拽到 netlify.com

# 方法二：CLI部署
npm install -g netlify-cli
cd dist
netlify deploy --prod
```

### 4. 传统Web服务器

#### Nginx 配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/dist;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/css application/javascript application/json;
    
    # 缓存静态资源
    location ~* \.(js|css|png|jpg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # PWA支持
    location /manifest.json {
        add_header Content-Type application/manifest+json;
    }
}
```

#### Apache 配置
```apache
# .htaccess
RewriteEngine On

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/css application/javascript application/json
</IfModule>

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
    ExpiresByType image/png "access plus 1 year"
</IfModule>

# PWA支持
<Files "manifest.json">
    Header set Content-Type "application/manifest+json"
</Files>
```

## 🔍 部署验证

### 自动验证
```bash
# 部署完成后，访问验证页面
http://your-domain.com/verify-deployment.html
```

### 手动验证清单
- [ ] 主页面正常加载 (`/index.html`)
- [ ] 三个游戏都可以访问
  - [ ] 时空织梦者 (`/时空织梦者/index.html`)
  - [ ] 瞬光捕手 (`/瞬光捕手/index.html`)
  - [ ] 量子共鸣者 (`/量子共鸣者/index.html`)
- [ ] PWA功能正常 (可安装到桌面)
- [ ] 响应式设计在移动端正常
- [ ] 音频功能正常 (量子共鸣者)
- [ ] 数据存储功能正常

## 🚨 常见问题解决

### 问题1：页面空白或无法加载
**原因**: 文件路径错误或服务器配置问题
**解决方案**:
```bash
# 检查文件是否完整
ls -la dist/

# 检查服务器错误日志
# 确保服务器支持中文文件名
```

### 问题2：游戏无法启动
**原因**: JavaScript错误或浏览器兼容性问题
**解决方案**:
```bash
# 打开浏览器开发者工具 (F12)
# 查看 Console 标签页的错误信息
# 确保浏览器版本符合要求
```

### 问题3：音频无法播放
**原因**: 浏览器安全策略或缺少HTTPS
**解决方案**:
```bash
# 使用HTTPS部署 (推荐)
# 或在本地测试时点击页面后再播放音频
```

### 问题4：PWA无法安装
**原因**: 缺少HTTPS或manifest.json配置错误
**解决方案**:
```bash
# 确保使用HTTPS
# 检查 manifest.json 文件是否正确
# 确保所有图标文件都存在
```

## 📈 性能优化建议

### 1. 启用压缩
```bash
# 服务器端启用 gzip 压缩
# 可以减少 60-80% 的传输大小
```

### 2. 配置缓存
```bash
# 静态资源设置长期缓存
# HTML文件设置短期缓存
```

### 3. 使用CDN
```bash
# 将静态资源部署到CDN
# 提高全球访问速度
```

### 4. 图片优化
```bash
# 使用WebP格式 (如果支持)
# 压缩PNG/JPG图片
# 生成不同尺寸的响应式图片
```

## 🎉 部署完成

部署成功后，您可以：

1. **访问主页**: `http://your-domain.com/index.html`
2. **验证部署**: `http://your-domain.com/verify-deployment.html`
3. **直接游戏**: 
   - 时空织梦者: `http://your-domain.com/时空织梦者/index.html`
   - 瞬光捕手: `http://your-domain.com/瞬光捕手/index.html`
   - 量子共鸣者: `http://your-domain.com/量子共鸣者/index.html`

## 📞 技术支持

如果遇到问题，请：
1. 查看浏览器控制台错误信息
2. 运行部署验证工具
3. 检查服务器配置和日志
4. 确认浏览器兼容性

---

**🎮 享受 Split-Second Spark 带来的精彩游戏体验！**
