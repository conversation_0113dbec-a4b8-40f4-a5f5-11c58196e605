<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>版本信息系统演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 100%);
            color: #eee;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            color: #64ffda;
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            color: #ccc;
            font-size: 1.2em;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 12px;
            padding: 30px;
            margin-bottom: 30px;
            border: 1px solid rgba(100, 255, 218, 0.2);
        }

        .demo-section h2 {
            color: #64ffda;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .demo-card {
            background: rgba(255, 255, 255, 0.03);
            border-radius: 8px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .demo-card h3 {
            color: #64ffda;
            margin-bottom: 15px;
            font-size: 1.2em;
        }

        .demo-card p {
            color: #ccc;
            margin-bottom: 15px;
            line-height: 1.6;
        }

        .btn {
            background: #0f3460;
            color: #64ffda;
            border: 1px solid #64ffda;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.2s;
            margin: 5px;
        }

        .btn:hover {
            background: #64ffda;
            color: #0f3460;
        }

        .btn-secondary {
            background: #333;
            color: #fff;
            border: 1px solid #666;
        }

        .btn-secondary:hover {
            background: #555;
        }

        .code-block {
            background: #1a1a2e;
            border: 1px solid #333;
            border-radius: 6px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }

        .version-info-display {
            background: #16213e;
            border-radius: 8px;
            padding: 20px;
            margin: 15px 0;
        }

        .version-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .version-item:last-child {
            border-bottom: none;
        }

        .version-label {
            color: #ccc;
            font-weight: 500;
        }

        .version-value {
            color: #64ffda;
            font-family: 'Courier New', monospace;
        }

        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-clean {
            background: #51cf66;
        }

        .status-dirty {
            background: #ff6b6b;
        }

        .footer {
            text-align: center;
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            color: #666;
        }

        @media (max-width: 768px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
            
            .header h1 {
                font-size: 2em;
            }
            
            .demo-section {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎮 版本信息系统演示</h1>
            <p>Split-Second Spark 版本信息自动生成和显示系统</p>
        </div>

        <div class="demo-section">
            <h2>📋 当前版本信息</h2>
            <div id="current-version-info" class="version-info-display">
                <div class="version-item">
                    <span class="version-label">加载状态:</span>
                    <span class="version-value">正在加载...</span>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🚀 功能演示</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <h3>版本信息模态框</h3>
                    <p>显示完整的版本信息，包括Git信息、构建环境等详细数据。</p>
                    <button class="btn" onclick="showVersionModal()">显示版本信息</button>
                </div>

                <div class="demo-card">
                    <h3>控制台日志</h3>
                    <p>在浏览器控制台中显示格式化的版本信息。</p>
                    <button class="btn" onclick="logVersionInfo()">输出到控制台</button>
                </div>

                <div class="demo-card">
                    <h3>复制版本信息</h3>
                    <p>将版本信息复制到剪贴板，方便分享和报告问题。</p>
                    <button class="btn" onclick="copyVersionInfo()">复制版本信息</button>
                </div>

                <div class="demo-card">
                    <h3>自定义版本按钮</h3>
                    <p>创建自定义样式的版本信息按钮。</p>
                    <button class="btn" onclick="createCustomButton()">创建自定义按钮</button>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>💻 代码示例</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <h3>基本用法</h3>
                    <div class="code-block">// 获取版本信息
const version = VersionInfo.getVersion();
const buildTime = VersionInfo.getBuildTime();
const commit = VersionInfo.getCommitHashShort();

console.log(`版本: ${version}`);
console.log(`构建时间: ${buildTime}`);
console.log(`提交: ${commit}`);</div>
                </div>

                <div class="demo-card">
                    <h3>显示版本模态框</h3>
                    <div class="code-block">// 显示版本信息模态框
versionDisplay.showVersionModal();

// 或者创建自定义按钮
const button = versionDisplay.createVersionButton({
    text: '关于',
    style: { bottom: '20px', left: '20px' }
});
document.body.appendChild(button);</div>
                </div>

                <div class="demo-card">
                    <h3>异步加载版本信息</h3>
                    <div class="code-block">// 从JSON文件加载版本信息
async function loadVersion() {
    try {
        const response = await fetch('/version.json');
        const versionInfo = await response.json();
        return versionInfo;
    } catch (error) {
        console.error('加载失败:', error);
        return null;
    }
}</div>
                </div>

                <div class="demo-card">
                    <h3>格式化版本字符串</h3>
                    <div class="code-block">// 获取格式化的版本字符串
const versionString = VersionInfo.formatVersionString();
// 输出: v1.0.0 (a1b2c3d4) - 2025-08-07

// 更新页面标题
document.title = `Split-Second Spark ${versionString}`;</div>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🔧 API 测试</h2>
            <div class="demo-grid">
                <div class="demo-card">
                    <h3>版本信息API</h3>
                    <button class="btn" onclick="testVersionAPI()">测试版本API</button>
                    <button class="btn btn-secondary" onclick="testGitAPI()">测试Git API</button>
                    <button class="btn btn-secondary" onclick="testEnvironmentAPI()">测试环境API</button>
                    <div id="api-results" class="code-block" style="margin-top: 15px; min-height: 100px;">
                        点击按钮测试API功能...
                    </div>
                </div>
            </div>
        </div>

        <div class="footer">
            <p>Split-Second Spark 版本信息系统 - 自动生成于构建时</p>
            <p id="footer-version">版本信息加载中...</p>
        </div>
    </div>

    <!-- 引入版本信息模块 -->
    <script src="js/version.js"></script>
    <script src="js/utils/version-display.js"></script>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeDemo();
        });

        // 初始化演示页面
        function initializeDemo() {
            // 等待版本信息加载
            setTimeout(() => {
                updateCurrentVersionInfo();
                updateFooterVersion();
            }, 500);
        }

        // 更新当前版本信息显示
        function updateCurrentVersionInfo() {
            const container = document.getElementById('current-version-info');
            
            if (typeof VersionInfo !== 'undefined') {
                const info = VersionInfo.getVersionInfo();
                const git = info.git || {};
                
                container.innerHTML = `
                    <div class="version-item">
                        <span class="version-label">版本号:</span>
                        <span class="version-value">${info.version || 'N/A'}</span>
                    </div>
                    <div class="version-item">
                        <span class="version-label">构建时间:</span>
                        <span class="version-value">${info.build_time_formatted || 'N/A'}</span>
                    </div>
                    <div class="version-item">
                        <span class="version-label">Git提交:</span>
                        <span class="version-value">${git.commit_hash_short || 'N/A'}</span>
                    </div>
                    <div class="version-item">
                        <span class="version-label">Git分支:</span>
                        <span class="version-value">${git.branch || 'N/A'}</span>
                    </div>
                    <div class="version-item">
                        <span class="version-label">工作区状态:</span>
                        <span class="version-value">
                            <span class="status-indicator ${git.is_dirty ? 'status-dirty' : 'status-clean'}"></span>
                            ${git.is_dirty ? '有未提交更改' : '干净'}
                        </span>
                    </div>
                `;
            } else {
                container.innerHTML = `
                    <div class="version-item">
                        <span class="version-label">状态:</span>
                        <span class="version-value" style="color: #ff6b6b;">版本信息模块未加载</span>
                    </div>
                `;
            }
        }

        // 更新页脚版本信息
        function updateFooterVersion() {
            const footerVersion = document.getElementById('footer-version');
            
            if (typeof VersionInfo !== 'undefined') {
                const versionString = VersionInfo.formatVersionString();
                footerVersion.textContent = versionString;
            } else {
                footerVersion.textContent = '版本信息不可用';
            }
        }

        // 演示函数
        function showVersionModal() {
            if (typeof versionDisplay !== 'undefined') {
                versionDisplay.showVersionModal();
            } else {
                alert('版本显示组件未加载');
            }
        }

        function logVersionInfo() {
            if (typeof VersionInfo !== 'undefined') {
                VersionInfo.logVersionInfo();
                alert('版本信息已输出到控制台，请按F12查看');
            } else {
                console.log('版本信息模块未加载');
                alert('版本信息模块未加载');
            }
        }

        function copyVersionInfo() {
            if (typeof versionDisplay !== 'undefined') {
                versionDisplay.copyVersionInfo();
            } else {
                alert('版本显示组件未加载');
            }
        }

        function createCustomButton() {
            if (typeof versionDisplay !== 'undefined') {
                const button = versionDisplay.createVersionButton({
                    text: '🎮 游戏版本',
                    style: {
                        bottom: '80px',
                        left: '20px',
                        background: '#ff6b6b',
                        borderColor: '#ff6b6b',
                        color: '#fff'
                    }
                });
                document.body.appendChild(button);
                
                // 5秒后自动移除
                setTimeout(() => {
                    if (button.parentNode) {
                        button.remove();
                    }
                }, 5000);
                
                alert('自定义版本按钮已创建（5秒后自动移除）');
            } else {
                alert('版本显示组件未加载');
            }
        }

        // API测试函数
        function testVersionAPI() {
            const results = document.getElementById('api-results');
            
            if (typeof VersionInfo !== 'undefined') {
                const info = VersionInfo.getVersionInfo();
                results.innerHTML = `<strong>版本信息API测试结果:</strong><br>
版本号: ${VersionInfo.getVersion()}<br>
构建时间: ${VersionInfo.getBuildTime()}<br>
格式化版本: ${VersionInfo.formatVersionString()}<br>
是否开发版本: ${VersionInfo.isDevelopmentBuild()}`;
            } else {
                results.innerHTML = '<span style="color: #ff6b6b;">版本信息模块未加载</span>';
            }
        }

        function testGitAPI() {
            const results = document.getElementById('api-results');
            
            if (typeof VersionInfo !== 'undefined') {
                results.innerHTML = `<strong>Git信息API测试结果:</strong><br>
提交哈希: ${VersionInfo.getCommitHash() || 'N/A'}<br>
短哈希: ${VersionInfo.getCommitHashShort() || 'N/A'}<br>
分支: ${VersionInfo.getBranch() || 'N/A'}<br>
是否开发版本: ${VersionInfo.isDevelopmentBuild()}`;
            } else {
                results.innerHTML = '<span style="color: #ff6b6b;">版本信息模块未加载</span>';
            }
        }

        function testEnvironmentAPI() {
            const results = document.getElementById('api-results');
            
            if (typeof VersionInfo !== 'undefined') {
                const info = VersionInfo.getVersionInfo();
                const env = info.build_environment || {};
                results.innerHTML = `<strong>构建环境API测试结果:</strong><br>
操作系统: ${env.system || 'N/A'}<br>
Python版本: ${env.python_version || 'N/A'}<br>
构建工具: ${env.build_tool || 'N/A'}<br>
主机名: ${env.hostname || 'N/A'}`;
            } else {
                results.innerHTML = '<span style="color: #ff6b6b;">版本信息模块未加载</span>';
            }
        }
    </script>
</body>
</html>
