# Split-Second Spark 部署使用指南

> 完整的部署、配置和使用说明

## 📋 目录

- [系统要求](#系统要求)
- [快速部署](#快速部署)
- [详细部署步骤](#详细部署步骤)
- [配置说明](#配置说明)
- [使用指南](#使用指南)
- [故障排除](#故障排除)
- [性能优化](#性能优化)

## 🖥️ 系统要求

### 最低要求
- **浏览器**: Chrome 60+, Firefox 55+, Safari 12+, Edge 79+
- **JavaScript**: 必须启用ES6+支持
- **存储**: 至少50MB可用存储空间
- **网络**: 初次加载需要网络连接

### 推荐配置
- **浏览器**: 最新版本的现代浏览器
- **内存**: 4GB RAM或更多
- **存储**: 100MB可用存储空间
- **音频**: 耳机或音响设备（量子共鸣者游戏）
- **输入**: 鼠标、键盘、触摸屏或游戏手柄

### 设备兼容性
- **PC**: Windows 10+, macOS 10.14+, Linux (Ubuntu 18.04+)
- **移动设备**: iOS 12+, Android 8.0+
- **平板**: iPad (iOS 12+), Android平板 (8.0+)

## 🚀 快速部署

### 方法一：直接运行（最简单）
```bash
# 1. 下载项目文件
# 2. 解压到任意目录
# 3. 双击 index.html 文件
# 4. 游戏将在默认浏览器中打开
```

### 方法二：本地服务器（推荐）
```bash
# 使用Python 3
cd split-second-spark
python -m http.server 8000

# 使用Python 2
python -m SimpleHTTPServer 8000

# 使用Node.js
npx http-server -p 8000

# 访问 http://localhost:8000
```

### 方法三：在线部署
```bash
# GitHub Pages
git add .
git commit -m "Deploy to GitHub Pages"
git push origin main
# 在GitHub仓库设置中启用Pages

# Vercel
npm i -g vercel
vercel --prod

# Netlify
# 直接拖拽项目文件夹到 netlify.com
```

## 📁 详细部署步骤

### 1. 准备项目文件

#### 下载项目
```bash
# 方法一：Git克隆
git clone <repository-url>
cd split-second-spark

# 方法二：下载ZIP
# 从GitHub下载ZIP文件并解压
```

#### 验证文件结构
```
split-second-spark/
├── index.html              # ✅ 主入口文件
├── manifest.json           # ✅ PWA配置
├── styles/                 # ✅ 样式文件
├── js/                     # ✅ JavaScript文件
├── assets/                 # ✅ 资源文件
├── 量子共鸣者/             # ✅ 量子共鸣者游戏
├── 瞬光捕手/               # ✅ 瞬光捕手游戏
├── 时空织梦者/             # ✅ 时空织梦者游戏
└── README.md               # ✅ 项目说明
```

### 2. 本地开发环境

#### 使用VS Code + Live Server
```bash
# 1. 安装VS Code
# 2. 安装Live Server扩展
# 3. 右键index.html -> "Open with Live Server"
# 4. 自动在浏览器中打开并实时刷新
```

#### 使用其他编辑器
```bash
# Sublime Text + 浏览器
# 1. 用Sublime Text打开项目
# 2. 启动本地服务器
# 3. 在浏览器中访问

# WebStorm
# 1. 用WebStorm打开项目
# 2. 右键index.html -> "Open in Browser"
```

### 3. 生产环境部署

#### 静态文件服务器
```bash
# Nginx配置
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/split-second-spark;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/css application/javascript;
    
    # 缓存策略
    location ~* \.(js|css|png|jpg|gif|ico)$ {
        expires 1y;
        add_header Cache-Control "public";
    }
}
```

#### Apache配置
```apache
# .htaccess文件
RewriteEngine On

# 启用压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/css application/javascript
</IfModule>

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 year"
    ExpiresByType application/javascript "access plus 1 year"
</IfModule>
```

### 4. CDN部署优化

#### 资源分离
```html
<!-- 将静态资源部署到CDN -->
<link rel="preload" href="https://cdn.example.com/styles/main.css" as="style">
<link rel="preload" href="https://cdn.example.com/js/main.js" as="script">
```

#### 推荐CDN服务
- **Cloudflare**: 全球CDN，免费套餐
- **jsDelivr**: 开源项目专用CDN
- **阿里云CDN**: 国内访问优化
- **腾讯云CDN**: 国内高速访问

## ⚙️ 配置说明

### 1. 基础配置

#### PWA配置 (manifest.json)
```json
{
  "name": "Split-Second Spark - 游戏选择",
  "short_name": "Split-Second Spark",
  "description": "捕捉决定性瞬间，引燃无限可能",
  "start_url": "/",
  "display": "standalone",
  "theme_color": "#0f0f23",
  "background_color": "#0f0f23"
}
```

#### 语言配置
```javascript
// 在js/utils/i18n.js中配置支持的语言
const supportedLanguages = {
    'zh-CN': '中文',
    'en-US': 'English'
};

// 添加新语言翻译
this.translations['ja-JP'] = {
    'game.title': 'スプリットセカンドスパーク',
    // ... 其他翻译
};
```

### 2. 游戏配置

#### 瞬光捕手配置
```javascript
// 在瞬光捕手/js/core/game-engine.js中调整
const gameConfig = {
    sparkSpawnRate: 2000,    // 光点生成间隔(ms)
    sparkSpeed: 1.0,         // 光点移动速度
    sparkCount: 1,           // 同时光点数量
    perfectWindow: 100,      // 完美时机窗口(ms)
    goodWindow: 200,         // 良好时机窗口(ms)
    comboMultiplier: 1.5     // 连击倍数
};
```

#### 量子共鸣者配置
```javascript
// 在量子共鸣者/js/core/quantum-engine.js中调整
const quantumConfig = {
    resonanceThreshold: 0.1,  // 共鸣阈值
    energyDecayRate: 0.95,    // 能量衰减率
    particleCount: 50,        // 粒子数量
    frequencyRange: [20, 2000], // 频率范围(Hz)
    chainReactionDelay: 100   // 连锁反应延迟(ms)
};
```

#### 时空织梦者配置
```javascript
// 在时空织梦者/js/core/time-engine.js中调整
const timeConfig = {
    maxRewindSteps: 100,     // 最大回退步数
    timeAcceleration: 2.0,   // 时间加速倍数
    pauseDuration: 5000,     // 暂停持续时间(ms)
    dreamWeavingSpeed: 1.0   // 梦境编织速度
};
```

### 3. 性能配置

#### 渲染设置
```javascript
// 在各游戏的渲染引擎中调整
const renderConfig = {
    targetFPS: 60,           // 目标帧率
    maxParticles: 1000,      // 最大粒子数
    effectQuality: 'high',   // 效果质量: high/medium/low
    enableBloom: true,       // 启用辉光效果
    enableMotionBlur: false  // 启用运动模糊
};
```

#### 存储设置
```javascript
// 在js/utils/storage.js中配置
const storageConfig = {
    preferredBackend: 'indexeddb', // 首选存储后端
    maxStorageSize: 50 * 1024 * 1024, // 最大存储大小(50MB)
    compressionEnabled: true,      // 启用数据压缩
    encryptionEnabled: false       // 启用数据加密
};
```

## 📖 使用指南

### 1. 首次启动

#### 初始化步骤
1. **打开游戏**: 在浏览器中访问index.html
2. **选择语言**: 点击右上角语言按钮切换中英文
3. **调整设置**: 点击设置按钮配置性能和显示选项
4. **选择游戏**: 点击游戏卡片开始体验

#### 权限设置
- **存储权限**: 允许网站存储数据以保存游戏进度
- **音频权限**: 允许访问麦克风（量子共鸣者需要）
- **通知权限**: 允许显示游戏通知（可选）

### 2. 游戏操作

#### 通用操作
- **ESC键**: 暂停游戏或返回主菜单
- **F11键**: 切换全屏模式
- **Tab键**: 在界面元素间切换焦点
- **Enter键**: 确认选择或开始游戏

#### 瞬光捕手操作
- **鼠标左键**: 点击捕捉光点
- **空格键/回车键**: 在画布中心捕捉光点
- **P键**: 暂停游戏
- **触摸**: 移动端触摸屏幕捕捉光点

#### 量子共鸣者操作
- **鼠标点击**: 激活粒子
- **方向键**: 调节频率
- **滚轮**: 缩放视图
- **麦克风**: 通过声音控制频率

#### 时空织梦者操作
- **鼠标拖拽**: 连接游戏元素
- **空格键**: 暂停/恢复时间
- **R键**: 开始时间倒流
- **F键**: 时间快进

### 3. 数据管理

#### 游戏存档
- **自动保存**: 游戏进度自动保存到本地
- **手动备份**: 在设置中导出游戏数据
- **数据恢复**: 导入之前备份的游戏数据
- **清除数据**: 重置所有游戏进度和设置

#### 多玩家支持
- **创建账号**: 在游戏中创建新的玩家账号
- **切换账号**: 在不同玩家账号间切换
- **独立进度**: 每个账号的游戏进度独立保存

### 4. 自定义设置

#### 显示设置
- **主题**: 深色/浅色/自动
- **语言**: 中文/英文
- **分辨率**: 自动适配屏幕分辨率
- **全屏**: 支持全屏游戏模式

#### 性能设置
- **视觉效果**: 高/中/低三档效果质量
- **帧率限制**: 60FPS/30FPS/自动
- **粒子数量**: 根据设备性能调整
- **音频质量**: 高/中/低音频质量

#### 控制设置
- **鼠标灵敏度**: 调整鼠标操作灵敏度
- **键盘映射**: 自定义键盘快捷键
- **触摸设置**: 调整触摸操作参数
- **手柄支持**: 配置游戏手柄操作

## 🐛 故障排除

### 常见问题解决方案

#### 1. 游戏无法启动
**问题描述**: 页面空白或显示错误信息

**解决步骤**:
```bash
# 1. 检查浏览器控制台
# 按F12打开开发者工具，查看Console标签页的错误信息

# 2. 验证文件完整性
# 确保所有必要文件都存在且未损坏

# 3. 清除浏览器缓存
# Ctrl+Shift+Delete 清除缓存和Cookie

# 4. 尝试隐私模式
# 在隐私/无痕模式下测试游戏
```

**常见错误及解决方案**:
- `Uncaught SyntaxError`: 浏览器不支持ES6+，升级浏览器
- `Failed to load resource`: 文件路径错误，检查文件结构
- `Script error`: 跨域问题，使用本地服务器运行

#### 2. 数据无法保存
**问题描述**: 游戏进度或设置无法保存

**解决步骤**:
```javascript
// 1. 检查存储权限
if (!localStorage) {
    console.error('localStorage不可用');
}

// 2. 检查存储空间
navigator.storage.estimate().then(estimate => {
    console.log('可用存储:', estimate.quota);
    console.log('已用存储:', estimate.usage);
});

// 3. 测试存储功能
try {
    localStorage.setItem('test', 'value');
    localStorage.removeItem('test');
    console.log('localStorage正常');
} catch (e) {
    console.error('localStorage错误:', e);
}
```

**解决方案**:
- 检查浏览器隐私设置，允许本地存储
- 清理浏览器存储空间
- 尝试使用不同的浏览器
- 检查是否在隐私模式下运行

#### 3. 性能问题
**问题描述**: 游戏卡顿、帧率低或响应慢

**性能诊断**:
```javascript
// 在浏览器控制台中执行
// 1. 检查帧率
setInterval(() => {
    console.log('FPS:', Math.round(1000 / (performance.now() - lastTime)));
    lastTime = performance.now();
}, 1000);

// 2. 检查内存使用
console.log('内存使用:', performance.memory);

// 3. 检查CPU使用
console.time('render');
// ... 渲染代码 ...
console.timeEnd('render');
```

**优化建议**:
- 降低视觉效果设置到"中"或"低"
- 关闭其他占用资源的标签页和程序
- 降低浏览器缩放比例到100%
- 使用性能更好的浏览器（推荐Chrome）

#### 4. 音频问题（量子共鸣者）
**问题描述**: 无声音、音频异常或麦克风无法使用

**音频诊断**:
```javascript
// 检查Web Audio API支持
if (!window.AudioContext && !window.webkitAudioContext) {
    console.error('浏览器不支持Web Audio API');
}

// 检查麦克风权限
navigator.mediaDevices.getUserMedia({ audio: true })
    .then(stream => {
        console.log('麦克风权限已获取');
        stream.getTracks().forEach(track => track.stop());
    })
    .catch(err => {
        console.error('麦克风权限被拒绝:', err);
    });
```

**解决方案**:
- 检查设备音量和浏览器音频设置
- 允许网站访问麦克风权限
- 尝试使用耳机或外接音响
- 在HTTPS环境下运行（麦克风需要安全上下文）

#### 5. 移动端问题
**问题描述**: 移动设备上触摸无响应或显示异常

**移动端调试**:
```javascript
// 启用移动端调试
document.addEventListener('touchstart', function(e) {
    console.log('触摸开始:', e.touches.length);
});

// 检查视口设置
console.log('视口宽度:', window.innerWidth);
console.log('设备像素比:', window.devicePixelRatio);
```

**解决方案**:
- 确保viewport meta标签正确设置
- 检查CSS touch-action属性
- 测试不同的触摸手势
- 尝试横屏和竖屏模式

### 高级故障排除

#### 开启调试模式
```javascript
// 在浏览器控制台中执行
localStorage.setItem('debug', 'true');
localStorage.setItem('verbose', 'true');
location.reload();

// 查看详细日志
console.log('调试模式已启用');
```

#### 性能分析
```javascript
// 启用性能监控
performance.mark('game-start');
// ... 游戏运行 ...
performance.mark('game-end');
performance.measure('game-duration', 'game-start', 'game-end');

// 查看性能数据
console.log(performance.getEntriesByType('measure'));
```

#### 网络问题诊断
```javascript
// 检查网络状态
console.log('在线状态:', navigator.onLine);

// 监听网络变化
window.addEventListener('online', () => {
    console.log('网络已连接');
});

window.addEventListener('offline', () => {
    console.log('网络已断开');
});
```

## ⚡ 性能优化

### 1. 浏览器优化

#### Chrome优化设置
```bash
# 启动Chrome时添加参数
chrome --enable-gpu-rasterization \
       --enable-zero-copy \
       --enable-features=VaapiVideoDecoder \
       --disable-background-timer-throttling
```

#### Firefox优化设置
```
# 在about:config中设置
gfx.webrender.enabled = true
layers.acceleration.force-enabled = true
dom.event.contextmenu.enabled = false
```

### 2. 代码优化

#### JavaScript性能优化
```javascript
// 使用requestAnimationFrame
function gameLoop() {
    // 游戏逻辑
    requestAnimationFrame(gameLoop);
}

// 对象池模式减少垃圾回收
class ObjectPool {
    constructor(createFn, resetFn) {
        this.createFn = createFn;
        this.resetFn = resetFn;
        this.pool = [];
    }

    get() {
        return this.pool.pop() || this.createFn();
    }

    release(obj) {
        this.resetFn(obj);
        this.pool.push(obj);
    }
}
```

#### Canvas渲染优化
```javascript
// 使用离屏Canvas
const offscreenCanvas = new OffscreenCanvas(800, 600);
const offscreenCtx = offscreenCanvas.getContext('2d');

// 批量绘制
function batchRender(objects) {
    ctx.save();
    objects.forEach(obj => {
        // 绘制对象
    });
    ctx.restore();
}

// 脏矩形更新
function updateDirtyRegions(dirtyRects) {
    dirtyRects.forEach(rect => {
        ctx.clearRect(rect.x, rect.y, rect.width, rect.height);
        // 重绘该区域
    });
}
```

### 3. 资源优化

#### 图片优化
```bash
# 使用WebP格式
cwebp input.png -q 80 -o output.webp

# 压缩PNG
pngquant --quality=65-80 input.png --output output.png

# 生成不同尺寸
convert input.png -resize 50% output-50.png
```

#### 音频优化
```bash
# 压缩音频文件
ffmpeg -i input.wav -c:a libvorbis -q:a 4 output.ogg
ffmpeg -i input.wav -c:a aac -b:a 128k output.m4a
```

### 4. 缓存策略

#### Service Worker缓存
```javascript
// 注册Service Worker
if ('serviceWorker' in navigator) {
    navigator.serviceWorker.register('/sw.js');
}

// sw.js - 缓存策略
const CACHE_NAME = 'split-second-spark-v1';
const urlsToCache = [
    '/',
    '/styles/main.css',
    '/js/main.js',
    // ... 其他资源
];

self.addEventListener('install', event => {
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(cache => cache.addAll(urlsToCache))
    );
});
```

#### HTTP缓存头
```nginx
# Nginx配置
location ~* \.(js|css|png|jpg|gif|ico|svg)$ {
    expires 1y;
    add_header Cache-Control "public, immutable";
    add_header Vary "Accept-Encoding";
}

location ~* \.(html)$ {
    expires 1h;
    add_header Cache-Control "public, must-revalidate";
}
```

### 5. 监控和分析

#### 性能监控
```javascript
// 自定义性能监控
class PerformanceMonitor {
    constructor() {
        this.metrics = {};
        this.startTime = performance.now();
    }

    mark(name) {
        this.metrics[name] = performance.now() - this.startTime;
    }

    measure(name, startMark, endMark) {
        const duration = this.metrics[endMark] - this.metrics[startMark];
        console.log(`${name}: ${duration.toFixed(2)}ms`);
        return duration;
    }

    getReport() {
        return {
            fps: this.calculateFPS(),
            memory: performance.memory,
            timing: performance.timing,
            metrics: this.metrics
        };
    }
}
```

#### 错误监控
```javascript
// 全局错误处理
window.addEventListener('error', (event) => {
    console.error('JavaScript错误:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error
    });
});

// Promise错误处理
window.addEventListener('unhandledrejection', (event) => {
    console.error('未处理的Promise拒绝:', event.reason);
});
```

## 📊 部署检查清单

### 部署前检查
- [ ] 所有文件完整且路径正确
- [ ] 浏览器兼容性测试通过
- [ ] 移动端响应式测试通过
- [ ] 性能测试达到要求
- [ ] 错误处理机制完善
- [ ] 国际化功能正常
- [ ] PWA功能测试通过

### 部署后验证
- [ ] 主页面正常加载
- [ ] 所有游戏可以正常启动
- [ ] 数据存储功能正常
- [ ] 音频功能正常（量子共鸣者）
- [ ] 触摸操作正常（移动端）
- [ ] 性能表现符合预期
- [ ] 错误日志无异常

### 持续监控
- [ ] 定期检查游戏功能
- [ ] 监控性能指标
- [ ] 收集用户反馈
- [ ] 更新浏览器兼容性
- [ ] 备份用户数据
- [ ] 安全性检查

---

**🎯 部署完成后，您就可以享受Split-Second Spark带来的精彩游戏体验了！**

如有任何问题，请参考故障排除部分或联系技术支持。
