# Split-Second Spark 存储方案使用指南

## 📦 概述

Split-Second Spark 项目提供了灵活的存储解决方案，支持多种存储后端和配置选项，满足不同场景的需求。

## 🗄️ 支持的存储方案

### 1. 本地存储方案

#### IndexedDB（推荐）
- **优点**: 大容量存储，支持复杂数据结构，异步操作
- **缺点**: 较复杂的API，部分旧浏览器不支持
- **适用场景**: 需要存储大量游戏数据的场景

#### localStorage
- **优点**: 简单易用，广泛支持
- **缺点**: 容量限制（通常5-10MB），同步操作可能阻塞UI
- **适用场景**: 存储少量配置和设置数据

#### 内存存储
- **优点**: 速度最快，无容量限制
- **缺点**: 页面刷新后数据丢失
- **适用场景**: 临时数据存储，隐私模式

### 2. 云存储方案

#### Firebase Firestore
- **优点**: 实时同步，跨设备数据共享，自动备份
- **缺点**: 需要网络连接，可能产生费用
- **适用场景**: 需要跨设备同步的用户数据

#### 混合存储（本地+云端）
- **优点**: 结合本地和云端优势，离线可用，自动同步
- **缺点**: 配置较复杂，需要处理同步冲突
- **适用场景**: 最佳用户体验，推荐方案

## 🚀 快速开始

### 基础使用

```javascript
// 1. 导入存储配置管理器
import { StorageConfigManager } from './js/config/storage-config.js';

// 2. 获取配置管理器实例
const configManager = StorageConfigManager.getInstance();

// 3. 自动选择最佳配置
const selectedConfig = configManager.autoSelectConfig();
console.log('自动选择的配置:', selectedConfig);

// 4. 检查存储服务是否已存在，如果存在则引导用户设置身份
let storageService;
const existingService = configManager.getCurrentStorageService();

if (existingService) {
    console.log('🔍 检测到已存在的存储服务');

    // 检查是否为云存储配置
    const currentConfig = configManager.getCurrentConfig();
    const isCloudStorage = currentConfig && (
        currentConfig.name === 'cloud' ||
        currentConfig.name === 'hybrid' ||
        currentConfig.adapters.some(adapter => adapter.type === 'firebase' || adapter.type === 'user-cloud')
    );

    if (isCloudStorage) {
        console.log('☁️ 当前配置支持云存储，检查用户身份状态...');

        // 检查用户身份状态
        const hasUserIdentity = await checkUserIdentityStatus();

        if (!hasUserIdentity) {
            console.log('⚠️ 需要设置用户身份以使用云存储功能');

            // 显示用户身份设置提示
            const shouldSetupIdentity = await promptUserIdentitySetup();

            if (shouldSetupIdentity) {
                // 引导用户设置身份
                const userIdentity = await setupUserIdentity();

                if (userIdentity) {
                    console.log('✅ 用户身份设置完成，重新创建存储服务');
                    storageService = await configManager.createStorageService();
                } else {
                    console.log('❌ 用户身份设置失败，使用现有服务');
                    storageService = existingService;
                }
            } else {
                console.log('👤 用户选择跳过身份设置，使用现有服务');
                storageService = existingService;
            }
        } else {
            console.log('✅ 用户身份已设置，使用现有服务');
            storageService = existingService;
        }
    } else {
        console.log('📱 当前为本地存储配置，使用现有服务');
        storageService = existingService;
    }
} else {
    console.log('🆕 创建新的存储服务');
    storageService = await configManager.createStorageService();
}

// 5. 使用存储服务
await storageService.put('player-name', '玩家1');
const playerName = await storageService.get('player-name');
console.log('玩家姓名:', playerName);

// 辅助函数：检查用户身份状态
async function checkUserIdentityStatus() {
    try {
        // 检查是否有用户身份管理器
        if (window.userIdentityManager) {
            const userInfo = window.userIdentityManager.getUserInfo();
            return userInfo && !userInfo.isGuest;
        }

        // 检查 Firebase 认证状态
        if (window.firebase && window.firebase.auth) {
            const user = window.firebase.auth().currentUser;
            return user && !user.isAnonymous;
        }

        return false;
    } catch (error) {
        console.warn('检查用户身份状态失败:', error);
        return false;
    }
}

// 辅助函数：提示用户设置身份
async function promptUserIdentitySetup() {
    return new Promise((resolve) => {
        // 创建模态对话框
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.7); display: flex; align-items: center;
            justify-content: center; z-index: 10000; font-family: Arial, sans-serif;
        `;

        modal.innerHTML = `
            <div style="background: white; padding: 30px; border-radius: 10px; max-width: 400px; text-align: center;">
                <h3 style="color: #333; margin-bottom: 20px;">🔐 启用云存储功能</h3>
                <p style="color: #666; margin-bottom: 25px; line-height: 1.5;">
                    检测到您的游戏配置支持云存储功能，可以在多个设备间同步游戏数据。
                    <br><br>
                    是否现在设置用户身份以启用云存储？
                </p>
                <div style="display: flex; gap: 15px; justify-content: center;">
                    <button id="setupIdentity" style="
                        background: #4CAF50; color: white; border: none; padding: 12px 24px;
                        border-radius: 5px; cursor: pointer; font-size: 16px;
                    ">设置身份</button>
                    <button id="skipSetup" style="
                        background: #f44336; color: white; border: none; padding: 12px 24px;
                        border-radius: 5px; cursor: pointer; font-size: 16px;
                    ">暂时跳过</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        modal.querySelector('#setupIdentity').onclick = () => {
            document.body.removeChild(modal);
            resolve(true);
        };

        modal.querySelector('#skipSetup').onclick = () => {
            document.body.removeChild(modal);
            resolve(false);
        };
    });
}

// 辅助函数：设置用户身份
async function setupUserIdentity() {
    try {
        // 如果有用户身份管理器，使用它
        if (window.userIdentityManager) {
            return await showUserIdentityDialog();
        }

        // 否则使用简单的 Firebase 认证
        if (window.firebase && window.firebase.auth) {
            return await window.firebase.auth().signInAnonymously();
        }

        console.warn('未找到可用的用户身份管理系统');
        return null;

    } catch (error) {
        console.error('用户身份设置失败:', error);
        return null;
    }
}

// 辅助函数：显示用户身份设置对话框
async function showUserIdentityDialog() {
    return new Promise((resolve) => {
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            background: rgba(0,0,0,0.7); display: flex; align-items: center;
            justify-content: center; z-index: 10000; font-family: Arial, sans-serif;
        `;

        modal.innerHTML = `
            <div style="background: white; padding: 30px; border-radius: 10px; max-width: 450px;">
                <h3 style="color: #333; margin-bottom: 20px; text-align: center;">👤 设置用户身份</h3>

                <div style="margin-bottom: 20px;">
                    <button id="guestMode" style="
                        width: 100%; background: #2196F3; color: white; border: none;
                        padding: 15px; border-radius: 5px; cursor: pointer; font-size: 16px;
                        margin-bottom: 10px;
                    ">🎮 访客模式（本地存储）</button>

                    <button id="anonymousMode" style="
                        width: 100%; background: #FF9800; color: white; border: none;
                        padding: 15px; border-radius: 5px; cursor: pointer; font-size: 16px;
                        margin-bottom: 10px;
                    ">🎭 匿名登录（云端同步）</button>

                    <button id="emailMode" style="
                        width: 100%; background: #4CAF50; color: white; border: none;
                        padding: 15px; border-radius: 5px; cursor: pointer; font-size: 16px;
                        margin-bottom: 15px;
                    ">📧 邮箱注册（完整功能）</button>
                </div>

                <div id="emailForm" style="display: none; margin-bottom: 20px;">
                    <input type="email" id="userEmail" placeholder="邮箱地址" style="
                        width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px;
                        margin-bottom: 10px; box-sizing: border-box;
                    ">
                    <input type="password" id="userPassword" placeholder="密码" style="
                        width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px;
                        margin-bottom: 10px; box-sizing: border-box;
                    ">
                    <input type="text" id="displayName" placeholder="显示名称" style="
                        width: 100%; padding: 12px; border: 1px solid #ddd; border-radius: 5px;
                        margin-bottom: 15px; box-sizing: border-box;
                    ">
                    <button id="confirmEmail" style="
                        width: 100%; background: #4CAF50; color: white; border: none;
                        padding: 12px; border-radius: 5px; cursor: pointer; font-size: 16px;
                    ">确认注册</button>
                </div>

                <button id="cancelSetup" style="
                    width: 100%; background: #f44336; color: white; border: none;
                    padding: 12px; border-radius: 5px; cursor: pointer; font-size: 16px;
                ">取消</button>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        modal.querySelector('#guestMode').onclick = async () => {
            try {
                const user = window.userIdentityManager.createGuestUser();
                document.body.removeChild(modal);
                resolve(user);
            } catch (error) {
                console.error('访客模式设置失败:', error);
                resolve(null);
            }
        };

        modal.querySelector('#anonymousMode').onclick = async () => {
            try {
                const user = await window.userIdentityManager.autoSignIn();
                document.body.removeChild(modal);
                resolve(user);
            } catch (error) {
                console.error('匿名登录失败:', error);
                resolve(null);
            }
        };

        modal.querySelector('#emailMode').onclick = () => {
            modal.querySelector('#emailForm').style.display = 'block';
        };

        modal.querySelector('#confirmEmail').onclick = async () => {
            const email = modal.querySelector('#userEmail').value.trim();
            const password = modal.querySelector('#userPassword').value.trim();
            const displayName = modal.querySelector('#displayName').value.trim();

            if (!email || !password) {
                alert('请填写邮箱和密码');
                return;
            }

            try {
                const user = await window.userIdentityManager.signUpWithEmail(email, password, displayName);
                document.body.removeChild(modal);
                resolve(user);
            } catch (error) {
                console.error('邮箱注册失败:', error);
                alert('注册失败: ' + error.message);
            }
        };

        modal.querySelector('#cancelSetup').onclick = () => {
            document.body.removeChild(modal);
            resolve(null);
        };
    });
}
```

### 使用特定配置

```javascript
// 使用高性能配置
const performanceStorage = await configManager.createStorageService('performance');

// 使用隐私模式配置
const privacyStorage = await configManager.createStorageService('privacy');

// 使用云存储配置
const cloudStorage = await configManager.createStorageService('firebase');
```

## ⚙️ 配置选项详解

### 本地存储配置

```javascript
const localConfig = {
    dbName: 'SplitSecondSparkDB',
    dbVersion: 1,
    enableCloudSync: false,
    adapterPriority: ['indexeddb', 'localstorage', 'memory']
};
```

### Firebase 云存储配置

```javascript
const firebaseConfig = {
    dbName: 'SplitSecondSparkDB',
    dbVersion: 1,
    enableCloudSync: true,
    syncInterval: 5 * 60 * 1000, // 5分钟同步一次
    adapterPriority: ['hybrid', 'indexeddb', 'localstorage', 'memory'],
    cloudConfig: {
        type: 'firebase',
        config: {
            apiKey: "your-api-key",
            authDomain: "your-project.firebaseapp.com",
            projectId: "your-project-id",
            storageBucket: "your-project.appspot.com",
            messagingSenderId: "123456789",
            appId: "your-app-id"
        }
    }
};
```

### 自定义配置

```javascript
// 添加自定义配置
const customConfig = {
    dbName: 'MyCustomDB',
    dbVersion: 2,
    enableCloudSync: false,
    adapterPriority: ['indexeddb'],
    cacheSize: 500,
    compressionEnabled: true
};

configManager.config.addConfig('custom', customConfig);
const customStorage = await configManager.createStorageService('custom');
```

## 🔧 高级功能

### 数据同步

```javascript
// 仅在混合存储模式下可用
if (storageService.hybridMode) {
    // 手动同步数据
    await storageService.syncData();
    
    // 获取同步状态
    const stats = await storageService.getStorageInfo();
    console.log('存储统计:', stats);
}
```

### 批量操作

```javascript
// 批量保存数据
const gameData = {
    'player-level': 10,
    'player-score': 15000,
    'player-achievements': ['first-win', 'speed-demon'],
    'game-settings': {
        volume: 0.8,
        difficulty: 'normal'
    }
};

for (const [key, value] of Object.entries(gameData)) {
    await storageService.put(key, value);
}

// 批量读取数据
const keys = await storageService.list('player-');
const playerData = {};
for (const key of keys) {
    playerData[key] = await storageService.get(key);
}
console.log('玩家数据:', playerData);
```

### 数据迁移

```javascript
// 从旧存储迁移到新存储
async function migrateData(oldStorage, newStorage) {
    console.log('🔄 开始数据迁移...');
    
    const keys = await oldStorage.list();
    let migratedCount = 0;
    
    for (const key of keys) {
        try {
            const value = await oldStorage.get(key);
            await newStorage.put(key, value);
            migratedCount++;
        } catch (error) {
            console.error(`迁移失败 [${key}]:`, error);
        }
    }
    
    console.log(`✅ 数据迁移完成，共迁移 ${migratedCount} 条数据`);
}

// 使用示例
const oldStorage = await configManager.createStorageService('local');
const newStorage = await configManager.createStorageService('firebase');
await migrateData(oldStorage, newStorage);
```

## 🛠️ 故障排除

### 常见问题

#### 1. IndexedDB 初始化失败
```javascript
// 检查浏览器支持
if (!window.indexedDB) {
    console.warn('浏览器不支持 IndexedDB');
    // 自动降级到 localStorage
}

// 检查存储空间
if ('storage' in navigator && 'estimate' in navigator.storage) {
    const estimate = await navigator.storage.estimate();
    console.log('存储空间:', estimate);
}
```

#### 2. 云存储连接失败
```javascript
// 检查网络连接
if (!navigator.onLine) {
    console.warn('网络连接不可用，使用本地存储');
}

// 检查 Firebase 配置
try {
    await storageService.cloudAdapter.init();
} catch (error) {
    console.error('云存储初始化失败:', error);
    // 降级到本地存储
}
```

#### 3. 数据同步冲突
```javascript
// 处理同步冲突
storageService.on('syncConflict', (conflict) => {
    console.log('同步冲突:', conflict);
    
    // 选择解决策略
    if (conflict.local.timestamp > conflict.remote.timestamp) {
        // 使用本地数据
        return conflict.local.value;
    } else {
        // 使用远程数据
        return conflict.remote.value;
    }
});
```

### 调试工具

```javascript
// 启用详细日志
localStorage.setItem('storage-debug', 'true');

// 查看存储统计
const stats = await storageService.getStorageInfo();
console.table(stats);

// 检查数据完整性
const keys = await storageService.list();
for (const key of keys) {
    const value = await storageService.get(key);
    if (value === null) {
        console.warn(`数据丢失: ${key}`);
    }
}
```

## 📊 性能优化

### 最佳实践

1. **选择合适的存储方案**
   - 小量数据：localStorage
   - 大量数据：IndexedDB
   - 跨设备同步：混合存储

2. **优化数据结构**
   ```javascript
   // 好的做法：扁平化数据结构
   await storageService.put('player-level', 10);
   await storageService.put('player-score', 15000);
   
   // 避免：深层嵌套对象
   await storageService.put('player-data', {
       profile: {
           stats: {
               level: 10,
               score: 15000
           }
       }
   });
   ```

3. **批量操作优化**
   ```javascript
   // 使用事务进行批量操作
   const transaction = await storageService.beginTransaction();
   try {
       await transaction.put('key1', 'value1');
       await transaction.put('key2', 'value2');
       await transaction.commit();
   } catch (error) {
       await transaction.rollback();
   }
   ```

4. **缓存策略**
   ```javascript
   // 实现简单的内存缓存
   class CachedStorageService {
       constructor(storageService) {
           this.storage = storageService;
           this.cache = new Map();
       }
       
       async get(key) {
           if (this.cache.has(key)) {
               return this.cache.get(key);
           }
           
           const value = await this.storage.get(key);
           this.cache.set(key, value);
           return value;
       }
       
       async put(key, value) {
           this.cache.set(key, value);
           return await this.storage.put(key, value);
       }
   }
   ```

## 🔒 安全考虑

### 数据加密

```javascript
// 敏感数据加密存储
class EncryptedStorageService {
    constructor(storageService, encryptionKey) {
        this.storage = storageService;
        this.key = encryptionKey;
    }
    
    async put(key, value) {
        const encrypted = this.encrypt(JSON.stringify(value));
        return await this.storage.put(key, encrypted);
    }
    
    async get(key) {
        const encrypted = await this.storage.get(key);
        if (!encrypted) return null;
        
        try {
            const decrypted = this.decrypt(encrypted);
            return JSON.parse(decrypted);
        } catch (error) {
            console.error('解密失败:', error);
            return null;
        }
    }
    
    encrypt(data) {
        // 实现加密逻辑
        return btoa(data); // 简单示例，实际应使用更强的加密
    }
    
    decrypt(data) {
        // 实现解密逻辑
        return atob(data); // 简单示例，实际应使用更强的解密
    }
}
```

### 数据验证

```javascript
// 数据完整性验证
class ValidatedStorageService {
    constructor(storageService) {
        this.storage = storageService;
    }
    
    async put(key, value) {
        const checksum = this.calculateChecksum(value);
        const wrappedValue = {
            data: value,
            checksum: checksum,
            timestamp: Date.now()
        };
        
        return await this.storage.put(key, wrappedValue);
    }
    
    async get(key) {
        const wrappedValue = await this.storage.get(key);
        if (!wrappedValue) return null;
        
        const expectedChecksum = this.calculateChecksum(wrappedValue.data);
        if (wrappedValue.checksum !== expectedChecksum) {
            console.error('数据完整性验证失败:', key);
            return null;
        }
        
        return wrappedValue.data;
    }
    
    calculateChecksum(data) {
        // 简单的校验和计算
        const str = JSON.stringify(data);
        let hash = 0;
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // 转换为32位整数
        }
        return hash;
    }
}
```

## 📈 监控和分析

### 使用统计

```javascript
// 存储使用情况监控
class StorageMonitor {
    constructor(storageService) {
        this.storage = storageService;
        this.stats = {
            reads: 0,
            writes: 0,
            deletes: 0,
            errors: 0
        };
    }
    
    async get(key) {
        try {
            this.stats.reads++;
            return await this.storage.get(key);
        } catch (error) {
            this.stats.errors++;
            throw error;
        }
    }
    
    async put(key, value) {
        try {
            this.stats.writes++;
            return await this.storage.put(key, value);
        } catch (error) {
            this.stats.errors++;
            throw error;
        }
    }
    
    getStats() {
        return { ...this.stats };
    }
}
```

## 🎯 总结

Split-Second Spark 的存储系统提供了：

1. **灵活性**: 支持多种存储后端和配置
2. **可靠性**: 自动降级和错误恢复机制
3. **性能**: 优化的数据访问和缓存策略
4. **安全性**: 数据加密和完整性验证
5. **易用性**: 简单统一的API接口

选择合适的存储方案可以显著提升游戏的用户体验和数据安全性。建议根据具体需求选择最适合的配置方案。
