<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Split-Second Spark - 存储方案演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.8;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-section h2 {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #4ecdc4;
        }

        .config-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .config-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
            transition: all 0.3s ease;
        }

        .config-card:hover {
            background: rgba(255, 255, 255, 0.1);
            transform: translateY(-5px);
        }

        .config-card h3 {
            color: #ff6b6b;
            margin-bottom: 10px;
        }

        .config-card p {
            opacity: 0.8;
            margin-bottom: 15px;
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .demo-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }

        .input-group {
            display: flex;
            gap: 10px;
            margin-bottom: 15px;
            align-items: center;
        }

        .input-group input {
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            flex: 1;
        }

        .input-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .log-area {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-info {
            color: #4ecdc4;
        }

        .log-success {
            color: #51cf66;
        }

        .log-warning {
            color: #ffd43b;
        }

        .log-error {
            color: #ff6b6b;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 20px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            text-align: center;
        }

        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #4ecdc4;
        }

        .stat-label {
            opacity: 0.8;
            margin-top: 5px;
        }

        .recommendations {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .recommendation {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            border-left: 4px solid #4ecdc4;
        }

        .recommendation.high {
            border-left-color: #ff6b6b;
        }

        .recommendation.medium {
            border-left-color: #ffd43b;
        }

        .recommendation.low {
            border-left-color: #51cf66;
        }

        @media (max-width: 768px) {
            .config-grid {
                grid-template-columns: 1fr;
            }
            
            .demo-controls {
                flex-direction: column;
            }
            
            .input-group {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🗄️ 存储方案演示</h1>
            <p>体验 Split-Second Spark 的多种存储解决方案</p>
        </div>

        <!-- 配置选择 -->
        <div class="demo-section">
            <h2>📋 存储配置选择</h2>
            <div class="config-grid">
                <div class="config-card">
                    <h3>🏠 本地存储</h3>
                    <p>使用 IndexedDB 或 localStorage 进行本地数据存储</p>
                    <button class="btn" onclick="selectConfig('local')">选择本地存储</button>
                </div>
                <div class="config-card">
                    <h3>☁️ 云存储</h3>
                    <p>使用 Firebase 进行云端数据同步</p>
                    <button class="btn" onclick="selectConfig('firebase')" disabled>选择云存储</button>
                    <small>需要配置 Firebase</small>
                </div>
                <div class="config-card">
                    <h3>⚡ 高性能</h3>
                    <p>优化的 IndexedDB 配置，适合大量数据</p>
                    <button class="btn" onclick="selectConfig('performance')">选择高性能</button>
                </div>
                <div class="config-card">
                    <h3>🔒 隐私模式</h3>
                    <p>仅使用内存存储，不保存到磁盘</p>
                    <button class="btn" onclick="selectConfig('privacy')">选择隐私模式</button>
                </div>
            </div>
            
            <div class="demo-controls">
                <button class="btn" onclick="autoSelectConfig()">🤖 自动选择配置</button>
                <button class="btn" onclick="showRecommendations()">💡 获取推荐</button>
                <button class="btn" onclick="showCurrentConfig()">📊 查看当前配置</button>
            </div>
        </div>

        <!-- 数据操作演示 -->
        <div class="demo-section">
            <h2>💾 数据操作演示</h2>
            
            <div class="input-group">
                <input type="text" id="dataKey" placeholder="输入键名，例如：player-name">
                <input type="text" id="dataValue" placeholder="输入值，例如：玩家1">
                <button class="btn" onclick="saveData()">💾 保存数据</button>
            </div>
            
            <div class="input-group">
                <input type="text" id="loadKey" placeholder="输入要读取的键名">
                <button class="btn" onclick="loadData()">📥 读取数据</button>
                <button class="btn" onclick="deleteData()">🗑️ 删除数据</button>
            </div>
            
            <div class="demo-controls">
                <button class="btn" onclick="listAllData()">📋 列出所有数据</button>
                <button class="btn" onclick="clearAllData()">🧹 清空所有数据</button>
                <button class="btn" onclick="generateTestData()">🎲 生成测试数据</button>
                <button class="btn" onclick="syncData()" id="syncBtn" disabled>🔄 同步数据</button>
            </div>
        </div>

        <!-- 存储统计 -->
        <div class="demo-section">
            <h2>📊 存储统计信息</h2>
            <div class="stats-grid" id="statsGrid">
                <!-- 统计信息将在这里显示 -->
            </div>
            <button class="btn" onclick="refreshStats()">🔄 刷新统计</button>
        </div>

        <!-- 推荐信息 -->
        <div class="demo-section">
            <h2>💡 配置推荐</h2>
            <div class="recommendations" id="recommendations">
                <!-- 推荐信息将在这里显示 -->
            </div>
        </div>

        <!-- 日志输出 -->
        <div class="demo-section">
            <h2>📝 操作日志</h2>
            <div class="log-area" id="logArea">
                <div class="log-entry log-info">🗄️ 存储演示系统已准备就绪</div>
            </div>
            <button class="btn" onclick="clearLog()">🧹 清空日志</button>
        </div>
    </div>

    <!-- 引入存储相关脚本 -->
    <script type="module">
        // 全局变量
        let currentStorageService = null;
        let configManager = null;

        // 初始化演示系统
        async function initDemo() {
            try {
                // 动态导入配置管理器
                const { StorageConfigManager } = await import('./js/config/storage-config.js');
                configManager = StorageConfigManager.getInstance();
                
                log('✅ 存储演示系统初始化完成', 'success');
                
                // 显示推荐配置
                showRecommendations();
                
                // 自动选择配置
                await autoSelectConfig();
                
            } catch (error) {
                log(`❌ 初始化失败: ${error.message}`, 'error');
                console.error('初始化错误:', error);
            }
        }

        // 选择配置
        window.selectConfig = async function(configName) {
            try {
                log(`🔄 正在切换到配置: ${configName}`, 'info');
                
                currentStorageService = await configManager.createStorageService(configName);
                
                log(`✅ 已切换到 ${configName} 配置`, 'success');
                
                // 更新同步按钮状态
                const syncBtn = document.getElementById('syncBtn');
                syncBtn.disabled = !currentStorageService.hybridMode;
                
                // 刷新统计信息
                refreshStats();
                
            } catch (error) {
                log(`❌ 配置切换失败: ${error.message}`, 'error');
            }
        };

        // 自动选择配置
        window.autoSelectConfig = async function() {
            try {
                const selectedConfig = configManager.autoSelectConfig();
                log(`🤖 自动选择配置: ${selectedConfig}`, 'info');
                
                await selectConfig(selectedConfig);
                
            } catch (error) {
                log(`❌ 自动配置选择失败: ${error.message}`, 'error');
            }
        };

        // 显示推荐
        window.showRecommendations = function() {
            const recommendations = configManager.getRecommendations();
            const container = document.getElementById('recommendations');
            
            container.innerHTML = '';
            
            recommendations.forEach(rec => {
                const div = document.createElement('div');
                div.className = `recommendation ${rec.priority}`;
                div.innerHTML = `
                    <strong>${rec.config}</strong> - ${rec.reason}
                    <br><small>优先级: ${rec.priority}</small>
                `;
                container.appendChild(div);
            });
            
            log('💡 已显示配置推荐', 'info');
        };

        // 显示当前配置
        window.showCurrentConfig = function() {
            if (configManager) {
                configManager.displayConfigInfo();
                log('📊 配置信息已输出到控制台', 'info');
            }
        };

        // 保存数据
        window.saveData = async function() {
            if (!currentStorageService) {
                log('❌ 请先选择存储配置', 'error');
                return;
            }
            
            const key = document.getElementById('dataKey').value.trim();
            const value = document.getElementById('dataValue').value.trim();
            
            if (!key || !value) {
                log('❌ 请输入键名和值', 'error');
                return;
            }
            
            try {
                await currentStorageService.put(key, value);
                log(`✅ 数据已保存: ${key} = ${value}`, 'success');
                
                // 清空输入框
                document.getElementById('dataKey').value = '';
                document.getElementById('dataValue').value = '';
                
                refreshStats();
                
            } catch (error) {
                log(`❌ 保存失败: ${error.message}`, 'error');
            }
        };

        // 读取数据
        window.loadData = async function() {
            if (!currentStorageService) {
                log('❌ 请先选择存储配置', 'error');
                return;
            }
            
            const key = document.getElementById('loadKey').value.trim();
            
            if (!key) {
                log('❌ 请输入键名', 'error');
                return;
            }
            
            try {
                const value = await currentStorageService.get(key);
                
                if (value !== null) {
                    log(`📥 读取成功: ${key} = ${JSON.stringify(value)}`, 'success');
                } else {
                    log(`⚠️ 未找到数据: ${key}`, 'warning');
                }
                
            } catch (error) {
                log(`❌ 读取失败: ${error.message}`, 'error');
            }
        };

        // 删除数据
        window.deleteData = async function() {
            if (!currentStorageService) {
                log('❌ 请先选择存储配置', 'error');
                return;
            }
            
            const key = document.getElementById('loadKey').value.trim();
            
            if (!key) {
                log('❌ 请输入键名', 'error');
                return;
            }
            
            try {
                await currentStorageService.delete(key);
                log(`🗑️ 数据已删除: ${key}`, 'success');
                
                document.getElementById('loadKey').value = '';
                refreshStats();
                
            } catch (error) {
                log(`❌ 删除失败: ${error.message}`, 'error');
            }
        };

        // 列出所有数据
        window.listAllData = async function() {
            if (!currentStorageService) {
                log('❌ 请先选择存储配置', 'error');
                return;
            }
            
            try {
                const keys = await currentStorageService.list();
                
                if (keys.length === 0) {
                    log('📋 没有找到任何数据', 'info');
                } else {
                    log(`📋 找到 ${keys.length} 条数据:`, 'info');
                    keys.forEach(key => {
                        log(`  - ${key}`, 'info');
                    });
                }
                
            } catch (error) {
                log(`❌ 列表获取失败: ${error.message}`, 'error');
            }
        };

        // 清空所有数据
        window.clearAllData = async function() {
            if (!currentStorageService) {
                log('❌ 请先选择存储配置', 'error');
                return;
            }
            
            if (!confirm('确定要清空所有数据吗？此操作不可恢复！')) {
                return;
            }
            
            try {
                await currentStorageService.clear();
                log('🧹 所有数据已清空', 'success');
                
                refreshStats();
                
            } catch (error) {
                log(`❌ 清空失败: ${error.message}`, 'error');
            }
        };

        // 生成测试数据
        window.generateTestData = async function() {
            if (!currentStorageService) {
                log('❌ 请先选择存储配置', 'error');
                return;
            }
            
            const testData = {
                'player-name': '测试玩家',
                'player-level': 10,
                'player-score': 15000,
                'game-settings': {
                    volume: 0.8,
                    difficulty: 'normal',
                    language: 'zh-CN'
                },
                'player-achievements': ['first-win', 'speed-demon', 'perfect-score'],
                'last-played': new Date().toISOString()
            };
            
            try {
                let count = 0;
                for (const [key, value] of Object.entries(testData)) {
                    await currentStorageService.put(key, value);
                    count++;
                }
                
                log(`🎲 已生成 ${count} 条测试数据`, 'success');
                refreshStats();
                
            } catch (error) {
                log(`❌ 测试数据生成失败: ${error.message}`, 'error');
            }
        };

        // 同步数据
        window.syncData = async function() {
            if (!currentStorageService || !currentStorageService.hybridMode) {
                log('❌ 当前配置不支持数据同步', 'error');
                return;
            }
            
            try {
                log('🔄 开始数据同步...', 'info');
                await currentStorageService.syncData();
                log('✅ 数据同步完成', 'success');
                
            } catch (error) {
                log(`❌ 数据同步失败: ${error.message}`, 'error');
            }
        };

        // 刷新统计信息
        window.refreshStats = async function() {
            if (!currentStorageService) {
                return;
            }
            
            try {
                const stats = await currentStorageService.getStorageInfo();
                const container = document.getElementById('statsGrid');
                
                container.innerHTML = '';
                
                // 显示基本统计
                addStatCard(container, stats.keyCount || 0, '数据条数');
                addStatCard(container, stats.type || 'Unknown', '存储类型');
                addStatCard(container, stats.isAvailable ? '可用' : '不可用', '状态');
                
                if (stats.hybridMode) {
                    addStatCard(container, '是', '混合模式');
                }
                
                if (stats.cloudSync) {
                    addStatCard(container, '启用', '云同步');
                }
                
                // 如果是混合存储，显示详细信息
                if (stats.local) {
                    addStatCard(container, stats.local.type, '本地存储');
                }
                
                if (stats.cloud) {
                    addStatCard(container, stats.cloud.type || '云存储', '云端存储');
                }
                
            } catch (error) {
                log(`❌ 统计信息获取失败: ${error.message}`, 'error');
            }
        };

        // 添加统计卡片
        function addStatCard(container, value, label) {
            const card = document.createElement('div');
            card.className = 'stat-card';
            card.innerHTML = `
                <div class="stat-value">${value}</div>
                <div class="stat-label">${label}</div>
            `;
            container.appendChild(card);
        }

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
            
            // 控制台也输出
            console.log(message);
        }

        // 清空日志
        window.clearLog = function() {
            document.getElementById('logArea').innerHTML = '';
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initDemo);
    </script>
</body>
</html>
