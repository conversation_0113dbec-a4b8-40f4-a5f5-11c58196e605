# 游戏目录路径映射参考

本文件记录了游戏目录从中文名到英文名的映射关系。

## 映射表

| 中文目录名 | 英文目录名 | 英文游戏名 |
|-----------|-----------|-----------|
| 时空织梦者 | temporal-weaver | Temporal Dream Weaver |
| 瞬光捕手 | spark-catcher | Split-Second Spark |
| 量子共鸣者 | quantum-resonator | Quantum Resonance |

## 路径更新说明

在项目构建过程中，所有游戏目录都会被重命名为对应的英文目录名：

- `/时空织梦者/` → `/temporal-weaver/`
- `/瞬光捕手/` → `/spark-catcher/`
- `/量子共鸣者/` → `/quantum-resonator/`

## 相关文件

以下文件中的路径引用已被自动更新：

- `manifest.json` - PWA清单文件中的shortcuts路径
- `*.html` - HTML文件中的链接和资源路径
- `*.js` - JavaScript文件中的路径引用

## 注意事项

1. 游戏目录内的文件不会被修改
2. 路径映射配置在 `game_config.py` 中定义
3. 构建脚本会自动处理目录重命名和路径更新

---

生成时间: 1754579708.3048337
