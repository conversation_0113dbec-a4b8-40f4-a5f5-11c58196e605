<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Split-Second Spark - 界面优化示例</title>
    
    <!-- 引入优化后的样式文件 -->
    <link rel="stylesheet" href="styles/main.css">
    <link rel="stylesheet" href="styles/enhanced-colors.css">
    <link rel="stylesheet" href="styles/enhanced-interactions.css">
    <link rel="stylesheet" href="styles/modern-components.css">
    <link rel="stylesheet" href="styles/enhanced-game-cards.css">
    <link rel="stylesheet" href="styles/responsive.css">
    
    <style>
        /* 示例页面特定样式 */
        .demo-section {
            margin: 3rem 0;
            padding: 2rem;
            background: rgba(255, 255, 255, 0.03);
            border: 1px solid rgba(255, 255, 255, 0.08);
            border-radius: 1rem;
            backdrop-filter: blur(20px);
        }
        
        .demo-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--spark-400);
            text-align: center;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }
        
        .component-showcase {
            padding: 1.5rem;
            background: rgba(255, 255, 255, 0.02);
            border-radius: 0.75rem;
            border: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .showcase-title {
            font-size: 1rem;
            font-weight: 500;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }
    </style>
</head>
<body>
    <!-- 主容器 -->
    <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 2rem;">
        
        <!-- 页面标题 -->
        <header class="text-center" style="margin-bottom: 3rem;">
            <h1 class="text-3xl font-bold mb-4" style="background: var(--gradient-primary); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">
                Split-Second Spark 界面优化示例
            </h1>
            <p class="text-lg text-secondary">展示现代化UI组件和增强交互效果</p>
        </header>

        <!-- 按钮组件示例 -->
        <section class="demo-section">
            <h2 class="demo-title">现代化按钮系统</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="showcase-title">基础按钮</h3>
                    <div class="flex-modern gap-4 flex-wrap">
                        <button class="btn-modern btn-primary-modern">主要按钮</button>
                        <button class="btn-modern btn-secondary-modern">次要按钮</button>
                        <button class="btn-modern btn-ghost-modern">幽灵按钮</button>
                    </div>
                </div>
                
                <div class="component-showcase">
                    <h3 class="showcase-title">增强交互按钮</h3>
                    <div class="flex-modern gap-4 flex-wrap">
                        <button class="btn-modern btn-primary-modern btn-enhanced ripple-effect">
                            <span>🎮</span>
                            涟漪效果
                        </button>
                        <button class="btn-modern btn-secondary-modern elastic-scale">
                            <span>⚡</span>
                            弹性缩放
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 卡片组件示例 -->
        <section class="demo-section">
            <h2 class="demo-title">增强游戏卡片</h2>
            <div class="demo-grid">
                <!-- 时空织梦者卡片 -->
                <div class="game-card-enhanced" data-game="temporal">
                    <div class="card-background-enhanced">
                        <div class="temporal-gradient-enhanced"></div>
                        <div class="card-particles-enhanced">
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                        </div>
                        <div class="temporal-waves">
                            <div class="time-ripple"></div>
                            <div class="time-ripple"></div>
                            <div class="time-ripple"></div>
                        </div>
                    </div>
                    
                    <div class="card-content-enhanced">
                        <div class="game-icon-enhanced">
                            <div class="game-symbol-enhanced">⏰</div>
                        </div>
                        
                        <div class="game-info-enhanced">
                            <h3 class="game-title-enhanced">时空织梦者</h3>
                            <p class="game-subtitle-enhanced">Temporal Dream Weaver</p>
                            <p class="game-description-enhanced">
                                通过操控时间流动来编织梦境，解决复杂的时空谜题。注重策略思考和时间管理的创新解谜游戏。
                            </p>
                            
                            <div class="game-features-enhanced">
                                <span class="feature-tag-enhanced">时间操控</span>
                                <span class="feature-tag-enhanced">策略解谜</span>
                                <span class="feature-tag-enhanced">梦境编织</span>
                            </div>
                        </div>
                        
                        <div class="card-actions-enhanced">
                            <button class="action-btn-enhanced play-btn-enhanced">
                                <span>🎮</span>
                                开始游戏
                            </button>
                            <button class="action-btn-enhanced preview-btn-enhanced">
                                <span>👁️</span>
                                预览
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 瞬光捕手卡片 -->
                <div class="game-card-enhanced" data-game="spark">
                    <div class="card-background-enhanced">
                        <div class="spark-gradient-enhanced"></div>
                        <div class="card-particles-enhanced">
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                        </div>
                        <div class="spark-lightning">
                            <div class="lightning-bolt" style="--rotation: 15deg;"></div>
                            <div class="lightning-bolt" style="--rotation: -20deg;"></div>
                            <div class="lightning-bolt" style="--rotation: 10deg;"></div>
                        </div>
                    </div>
                    
                    <div class="card-content-enhanced">
                        <div class="game-icon-enhanced">
                            <div class="game-symbol-enhanced">⚡</div>
                        </div>
                        
                        <div class="game-info-enhanced">
                            <h3 class="game-title-enhanced">瞬光捕手</h3>
                            <p class="game-subtitle-enhanced">Split-Second Spark</p>
                            <p class="game-description-enhanced">
                                考验反应速度和时机把握的休闲游戏。在光点达到最佳时机时精准点击，获得高分并解锁更多关卡。
                            </p>
                            
                            <div class="game-features-enhanced">
                                <span class="feature-tag-enhanced">精准时机</span>
                                <span class="feature-tag-enhanced">连击系统</span>
                                <span class="feature-tag-enhanced">反应挑战</span>
                            </div>
                        </div>
                        
                        <div class="card-actions-enhanced">
                            <button class="action-btn-enhanced play-btn-enhanced">
                                <span>🎮</span>
                                开始游戏
                            </button>
                            <button class="action-btn-enhanced preview-btn-enhanced">
                                <span>👁️</span>
                                预览
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 量子共鸣者卡片 -->
                <div class="game-card-enhanced" data-game="quantum">
                    <div class="card-background-enhanced">
                        <div class="quantum-gradient-enhanced"></div>
                        <div class="card-particles-enhanced">
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                            <div class="particle-enhanced"></div>
                        </div>
                        <div class="quantum-orbits">
                            <div class="quantum-orbit-enhanced">
                                <div class="quantum-electron"></div>
                            </div>
                            <div class="quantum-orbit-enhanced">
                                <div class="quantum-electron"></div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card-content-enhanced">
                        <div class="game-icon-enhanced">
                            <div class="game-symbol-enhanced">🌌</div>
                        </div>
                        
                        <div class="game-info-enhanced">
                            <h3 class="game-title-enhanced">量子共鸣者</h3>
                            <p class="game-subtitle-enhanced">Quantum Resonance</p>
                            <p class="game-description-enhanced">
                                音乐节奏与物理模拟结合的创新游戏。通过控制量子粒子的共鸣频率创建连锁反应，体验独特的量子物理机制。
                            </p>
                            
                            <div class="game-features-enhanced">
                                <span class="feature-tag-enhanced">量子共鸣</span>
                                <span class="feature-tag-enhanced">音乐节奏</span>
                                <span class="feature-tag-enhanced">物理模拟</span>
                            </div>
                        </div>
                        
                        <div class="card-actions-enhanced">
                            <button class="action-btn-enhanced play-btn-enhanced">
                                <span>🎮</span>
                                开始游戏
                            </button>
                            <button class="action-btn-enhanced preview-btn-enhanced">
                                <span>👁️</span>
                                预览
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 表单组件示例 -->
        <section class="demo-section">
            <h2 class="demo-title">现代化表单组件</h2>
            <div class="demo-grid">
                <div class="component-showcase">
                    <h3 class="showcase-title">输入框和选择器</h3>
                    <div class="flex-modern flex-col gap-4">
                        <div class="input-group">
                            <span class="input-icon">🔍</span>
                            <input type="text" class="input-modern" placeholder="搜索游戏...">
                        </div>
                        
                        <div class="select-modern">
                            <select>
                                <option>选择难度</option>
                                <option>简单</option>
                                <option>中等</option>
                                <option>困难</option>
                            </select>
                        </div>
                        
                        <div class="flex-modern items-center gap-3">
                            <span class="text-sm">启用音效</span>
                            <div class="switch-modern">
                                <input type="checkbox" id="sound-toggle">
                                <label for="sound-toggle" class="switch-slider"></label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="component-showcase">
                    <h3 class="showcase-title">进度条和标签</h3>
                    <div class="flex-modern flex-col gap-4">
                        <div>
                            <div class="flex-modern justify-between mb-2">
                                <span class="text-sm">游戏进度</span>
                                <span class="text-sm">75%</span>
                            </div>
                            <div class="progress-modern">
                                <div class="progress-bar-modern" style="width: 75%"></div>
                            </div>
                        </div>
                        
                        <div class="flex-modern gap-2 flex-wrap">
                            <span class="badge-modern badge-primary">新游戏</span>
                            <span class="badge-modern badge-secondary">热门</span>
                            <span class="badge-modern badge-accent">推荐</span>
                        </div>
                        
                        <button class="btn-modern btn-primary-modern tooltip-modern" data-tooltip="点击开始新游戏">
                            悬浮提示示例
                        </button>
                    </div>
                </div>
            </div>
        </section>

        <!-- 布局系统示例 -->
        <section class="demo-section">
            <h2 class="demo-title">现代化布局系统</h2>
            <div class="grid-modern grid-cols-auto gap-6">
                <div class="card-modern p-6">
                    <h3 class="text-lg font-semibold mb-3">网格布局</h3>
                    <p class="text-sm text-secondary">使用CSS Grid实现响应式网格布局，自动适配不同屏幕尺寸。</p>
                </div>
                
                <div class="card-modern p-6">
                    <h3 class="text-lg font-semibold mb-3">弹性布局</h3>
                    <p class="text-sm text-secondary">结合Flexbox提供灵活的组件排列和对齐方式。</p>
                </div>
                
                <div class="card-modern p-6">
                    <h3 class="text-lg font-semibold mb-3">工具类系统</h3>
                    <p class="text-sm text-secondary">提供丰富的工具类，快速实现常用的样式效果。</p>
                </div>
            </div>
        </section>

    </div>

    <script>
        // 简单的交互演示
        document.addEventListener('DOMContentLoaded', function() {
            // 为按钮添加点击效果
            const buttons = document.querySelectorAll('.btn-enhanced, .action-btn-enhanced');
            buttons.forEach(button => {
                button.addEventListener('click', function(e) {
                    // 创建涟漪效果
                    const ripple = document.createElement('span');
                    const rect = this.getBoundingClientRect();
                    const size = Math.max(rect.width, rect.height);
                    const x = e.clientX - rect.left - size / 2;
                    const y = e.clientY - rect.top - size / 2;
                    
                    ripple.style.width = ripple.style.height = size + 'px';
                    ripple.style.left = x + 'px';
                    ripple.style.top = y + 'px';
                    ripple.classList.add('ripple');
                    
                    this.appendChild(ripple);
                    
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            });
        });
    </script>
</body>
</html>
