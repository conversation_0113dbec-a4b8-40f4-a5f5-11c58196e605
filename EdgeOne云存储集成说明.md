# EdgeOne 云存储集成说明

## 概述

Split-Second Spark 现在支持 EdgeOne 云存储方案。当部署后的应用检测到全局变量 `edgeOneStorage` 时，存储服务会自动使用云存储逻辑，否则继续使用原有的本地存储逻辑。

## 功能特性

### 🌐 自动检测云存储
- 在存储服务初始化时自动检测 `window.edgeOneStorage` 全局变量
- 如果检测到云存储服务，优先使用云存储方案
- 如果未检测到，回退到原有的本地存储逻辑（IndexedDB > localStorage > Memory）

### 🔄 兼容多种 API 风格
支持两种 API 风格的 EdgeOne 云存储服务：

#### 标准 API 风格
```javascript
window.edgeOneStorage = {
    async put(key, value) { /* 保存数据 */ },
    async get(key) { /* 读取数据 */ },
    async delete(key) { /* 删除数据 */ },
    async list(prefix) { /* 列出键 */ },
    async clear() { /* 清空数据 */ },
    async init() { /* 初始化 */ },
    async test() { /* 连接测试 */ },
    getInfo() { /* 获取存储信息 */ }
};
```

#### localStorage 风格 API
```javascript
window.edgeOneStorage = {
    async setItem(key, value) { /* 保存数据 */ },
    async getItem(key) { /* 读取数据 */ },
    async removeItem(key) { /* 删除数据 */ },
    async keys() { /* 获取所有键 */ },
    async clear() { /* 清空数据 */ }
};
```

### 📊 详细的日志记录
- 初始化过程的详细日志
- 每个操作的成功/失败状态
- 云存储服务可用性检测
- 错误处理和降级机制

## 使用方法

### 1. 部署前准备
确保你的 EdgeOne 云存储服务实现了必要的接口方法。

### 2. 部署时注入
在应用部署时，将 EdgeOne 云存储服务注入到全局变量：

```javascript
// 在应用加载前注入
window.edgeOneStorage = yourEdgeOneStorageImplementation;
```

### 3. 自动使用
应用启动后，存储服务会自动检测并使用云存储：

```javascript
// 应用代码无需修改，存储服务会自动选择最佳方案
const data = await storageService.get('user-settings');
await storageService.put('game-progress', gameData);
```

## 技术实现

### 存储服务初始化流程

```mermaid
flowchart TD
    A[存储服务初始化] --> B{检测 edgeOneStorage}
    B -->|存在| C[使用 EdgeOne 云存储]
    B -->|不存在| D[使用本地存储逻辑]
    C --> E[初始化云存储服务]
    E --> F[测试连接]
    F --> G[云存储就绪]
    D --> H{支持 IndexedDB?}
    H -->|是| I[使用 IndexedDB]
    H -->|否| J{支持 localStorage?}
    J -->|是| K[使用 localStorage]
    J -->|否| L[使用内存存储]
```

### 配置管理器增强

- `StorageConfigManager.isEdgeOneStorageAvailable()` - 检查云存储可用性
- `StorageConfigManager.getEdgeOneStorageInfo()` - 获取云存储详细信息
- 自动配置选择优先考虑 EdgeOne 云存储
- 推荐系统将云存储设为最高优先级

### 错误处理机制

1. **初始化失败**: 如果云存储初始化失败，会记录错误并抛出异常
2. **操作失败**: 单个操作失败时会记录错误，读取操作返回 null，其他操作抛出异常
3. **服务不可用**: 如果云存储服务在运行时变得不可用，会记录警告信息

## 配置选项

### EdgeOne 云存储信息获取

```javascript
const configManager = StorageConfigManager.getInstance();

// 检查云存储是否可用
if (configManager.isEdgeOneStorageAvailable()) {
    // 获取云存储详细信息
    const info = configManager.getEdgeOneStorageInfo();
    console.log('云存储信息:', info);
}
```

### 存储服务信息

```javascript
const storageInfo = storageService.getStorageInfo();
console.log('当前存储类型:', storageInfo.type);

// 如果是云存储，会包含额外信息
if (storageInfo.type === 'edgeone') {
    console.log('云存储详情:', storageInfo.cloudStorage);
}
```

## 最佳实践

### 1. 云存储服务实现
```javascript
// 推荐的 EdgeOne 云存储服务实现
window.edgeOneStorage = {
    async init() {
        // 初始化云存储连接
        console.log('EdgeOne 云存储初始化');
    },
    
    async test() {
        // 测试连接可用性
        console.log('EdgeOne 云存储连接测试');
    },
    
    async put(key, value) {
        // 实现数据保存逻辑
        console.log(`保存数据: ${key}`);
    },
    
    async get(key) {
        // 实现数据读取逻辑
        console.log(`读取数据: ${key}`);
        return null; // 返回实际数据
    },
    
    async delete(key) {
        // 实现数据删除逻辑
        console.log(`删除数据: ${key}`);
    },
    
    async list(prefix = '') {
        // 实现键列表获取逻辑
        console.log(`列出键，前缀: ${prefix}`);
        return []; // 返回键数组
    },
    
    async clear() {
        // 实现数据清空逻辑
        console.log('清空所有数据');
    },
    
    getInfo() {
        // 返回存储服务信息
        return {
            provider: 'EdgeOne',
            version: '1.0.0',
            region: 'auto'
        };
    }
};
```

### 2. 错误处理
```javascript
try {
    await storageService.put('key', 'value');
} catch (error) {
    console.error('存储操作失败:', error);
    // 实现降级逻辑
}
```

### 3. 性能优化
- 批量操作时考虑使用适当的延迟
- 对于频繁访问的数据考虑本地缓存
- 监控云存储服务的响应时间

## 兼容性说明

- 完全向后兼容现有的本地存储逻辑
- 不影响现有应用的功能
- 可以在运行时动态切换存储方案
- 支持渐进式部署和测试

## 故障排除

### 常见问题

1. **云存储未被检测到**
   - 确认 `window.edgeOneStorage` 在应用初始化前已设置
   - 检查云存储对象是否包含必要的方法

2. **初始化失败**
   - 查看控制台错误日志
   - 确认云存储服务的 `init()` 和 `test()` 方法正常工作

3. **操作失败**
   - 检查网络连接
   - 确认云存储服务端点可访问
   - 查看详细的错误日志

### 调试技巧

```javascript
// 启用详细日志
localStorage.setItem('debug', 'true');

// 检查存储服务状态
console.log('存储服务信息:', storageService.getStorageInfo());

// 检查配置管理器状态
const configManager = StorageConfigManager.getInstance();
console.log('云存储可用:', configManager.isEdgeOneStorageAvailable());
console.log('云存储信息:', configManager.getEdgeOneStorageInfo());
```
