<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>存储服务系统使用示例</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', <PERSON>l, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .demo-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 2px solid #3498db;
            border-radius: 10px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        }
        
        .demo-section h2 {
            color: #2c3e50;
            margin-top: 0;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .demo-controls {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        .demo-controls input, .demo-controls button {
            padding: 10px 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            font-size: 14px;
        }
        
        .demo-controls button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            cursor: pointer;
            transition: all 0.3s;
            border: none;
        }
        
        .demo-controls button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        
        .demo-controls button:disabled {
            background: #bdc3c7;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }
        
        .demo-output {
            background: #2c3e50;
            color: #ecf0f1;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 200px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 15px;
        }
        
        .success { color: #27ae60; }
        .error { color: #e74c3c; }
        .info { color: #3498db; }
        .warning { color: #f39c12; }
        
        .config-panel {
            background: #34495e;
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
        }
        
        .config-panel h3 {
            margin-top: 0;
            color: #ecf0f1;
        }
        
        .config-row {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            gap: 15px;
        }
        
        .config-row label {
            min-width: 120px;
            font-weight: bold;
        }
        
        .config-row input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #7f8c8d;
            border-radius: 5px;
            background: #ecf0f1;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-connected { background-color: #27ae60; }
        .status-disconnected { background-color: #e74c3c; }
        .status-connecting { background-color: #f39c12; }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .feature-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-left: 4px solid #3498db;
        }
        
        .feature-card h4 {
            margin-top: 0;
            color: #2c3e50;
        }
        
        @media (max-width: 600px) {
            .demo-controls {
                flex-direction: column;
            }
            
            .config-row {
                flex-direction: column;
                align-items: stretch;
            }
            
            .config-row label {
                min-width: auto;
                margin-bottom: 5px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 存储服务系统使用示例</h1>
        
        <!-- 配置面板 -->
        <div class="config-panel">
            <h3>⚙️ 服务配置</h3>
            <div class="config-row">
                <label for="apiUrl">API 地址:</label>
                <input type="text" id="apiUrl" value="https://your-domain.com" placeholder="输入您的 API 基础 URL">
            </div>
            <div class="config-row">
                <label for="enableCache">启用缓存:</label>
                <input type="checkbox" id="enableCache" checked>
            </div>
            <div class="config-row">
                <label for="enableFallback">启用降级:</label>
                <input type="checkbox" id="enableFallback" checked>
            </div>
            <div class="config-row">
                <label>连接状态:</label>
                <span id="connectionStatus">
                    <span class="status-indicator status-disconnected"></span>
                    未连接
                </span>
            </div>
            <div class="config-row">
                <label>Functions 状态:</label>
                <span id="functionsStatus">
                    <span class="status-indicator status-disconnected"></span>
                    未检测
                </span>
            </div>
            <button onclick="initializeService()" style="background: linear-gradient(135deg, #27ae60 0%, #2ecc71 100%); color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                🔌 连接服务
            </button>
            <button onclick="checkFunctionsStatus()" style="background: linear-gradient(135deg, #3498db 0%, #2980b9 100%); color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                🔍 检测 Functions
            </button>
            <button onclick="debugStorageInit()" style="background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%); color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
                🔧 调试初始化
            </button>
        </div>

        <!-- 基本操作演示 -->
        <div class="demo-section">
            <h2>💾 基本存储操作</h2>
            <div class="demo-controls">
                <input type="text" id="basicKey" placeholder="键名" value="demo_key">
                <input type="text" id="basicValue" placeholder="值" value="Hello, EdgeOne Storage!">
                <button onclick="demoBasicPut()">存储</button>
                <button onclick="demoBasicGet()">获取</button>
                <button onclick="demoBasicDelete()">删除</button>
            </div>
            <div class="demo-output" id="basicOutput"></div>
        </div>

        <!-- 对象存储演示 -->
        <div class="demo-section">
            <h2>📦 对象存储演示</h2>
            <div class="demo-controls">
                <button onclick="demoObjectStorage()">存储用户配置</button>
                <button onclick="demoGetUserConfig()">获取用户配置</button>
                <button onclick="demoUpdateUserConfig()">更新配置</button>
                <button onclick="demoDeleteUserConfig()">删除配置</button>
            </div>
            <div class="demo-output" id="objectOutput"></div>
        </div>

        <!-- 批量操作演示 -->
        <div class="demo-section">
            <h2>🔄 批量操作演示</h2>
            <div class="demo-controls">
                <button onclick="demoBatchOperations()">批量存储游戏数据</button>
                <button onclick="demoGetAllGameData()">获取所有游戏数据</button>
                <button onclick="demoCleanupGameData()">清理游戏数据</button>
            </div>
            <div class="demo-output" id="batchOutput"></div>
        </div>

        <!-- 功能特性展示 -->
        <div class="demo-section">
            <h2>✨ 功能特性</h2>
            <div class="feature-grid">
                <div class="feature-card">
                    <h4>🌐 全球分布</h4>
                    <p>基于 Cloudflare 的全球边缘网络，提供低延迟访问</p>
                </div>
                <div class="feature-card">
                    <h4>💨 高性能缓存</h4>
                    <p>内置智能缓存机制，显著提升数据访问速度</p>
                </div>
                <div class="feature-card">
                    <h4>🔄 自动重试</h4>
                    <p>网络异常时自动重试，确保操作可靠性</p>
                </div>
                <div class="feature-card">
                    <h4>📊 批量操作</h4>
                    <p>支持批量存储、获取和删除，提高操作效率</p>
                </div>
                <div class="feature-card">
                    <h4>🔄 智能降级</h4>
                    <p>服务不可用时自动切换到本地存储，确保应用连续性</p>
                </div>
                <div class="feature-card">
                    <h4>🔍 可用性检测</h4>
                    <p>主动检测服务状态，支持手动和自动模式切换</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入存储服务类 -->
    <script src="js/utils/edgeOneStorageImpl.js"></script>
    
    <script>
        let storage = null;

        // 日志输出函数
        function log(message, type = 'info', containerId = 'basicOutput') {
            const container = document.getElementById(containerId);
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            
            container.innerHTML += `<span class="${type}">${logEntry}</span>`;
            container.scrollTop = container.scrollHeight;
        }

        // 更新连接状态
        function updateConnectionStatus(status, message) {
            const statusElement = document.getElementById('connectionStatus');
            const indicators = {
                'connected': '<span class="status-indicator status-connected"></span>已连接',
                'connecting': '<span class="status-indicator status-connecting"></span>连接中...',
                'disconnected': '<span class="status-indicator status-disconnected"></span>未连接'
            };

            statusElement.innerHTML = indicators[status] || indicators['disconnected'];
            if (message) {
                statusElement.innerHTML += ` - ${message}`;
            }
        }

        // 更新 Functions 状态
        function updateFunctionsStatus(status, message) {
            const statusElement = document.getElementById('functionsStatus');
            const indicators = {
                'deployed': '<span class="status-indicator status-connected"></span>已部署',
                'checking': '<span class="status-indicator status-connecting"></span>检测中...',
                'not-deployed': '<span class="status-indicator status-disconnected"></span>未部署',
                'unknown': '<span class="status-indicator status-disconnected"></span>未检测'
            };

            statusElement.innerHTML = indicators[status] || indicators['unknown'];
            if (message) {
                statusElement.innerHTML += ` - ${message}`;
            }
        }

        // 初始化服务
        async function initializeService() {
            const apiUrl = document.getElementById('apiUrl').value.trim();
            const enableCache = document.getElementById('enableCache').checked;
            const enableFallback = document.getElementById('enableFallback').checked;

            updateConnectionStatus('connecting');

            try {
                if (apiUrl) {
                    storage = new EdgeOneStorageImpl(apiUrl, {
                        enableCache: enableCache,
                        enableFallback: enableFallback,
                        timeout: 10000,
                        retryCount: 2
                    });
                    log(`🔗 API 地址: ${apiUrl}`, 'info');
                } else {
                    storage = new EdgeOneStorageImpl({
                        enableCache: enableCache,
                        enableFallback: enableFallback,
                        timeout: 10000,
                        retryCount: 2
                    });
                    log(`🔗 使用相对路径: /storage/`, 'info');
                }

                // 测试连接
                await storage.put('connection_test', 'test_value');
                await storage.delete('connection_test');

                const stats = storage.getStats();
                updateConnectionStatus('connected', `服务已就绪 (${stats.storageMode})`);

                // 更新 Functions 状态
                if (stats.storageMode === 'remote') {
                    updateFunctionsStatus('deployed', '运行正常');
                } else {
                    updateFunctionsStatus('not-deployed', '使用本地存储');
                }

                log('✅ 存储服务初始化成功', 'success');
                log(`💾 缓存状态: ${enableCache ? '启用' : '禁用'}`, 'info');
                log(`🔄 降级功能: ${enableFallback ? '启用' : '禁用'}`, 'info');
                log(`🔧 存储模式: ${stats.storageMode}`, 'info');
            } catch (error) {
                updateConnectionStatus('disconnected', '连接失败');
                log(`❌ 服务初始化失败: ${error.message}`, 'error');

                // 如果启用降级，显示当前模式
                if (storage) {
                    const stats = storage.getStats();
                    log(`🔧 当前存储模式: ${stats.storageMode}`, 'warning');
                }
            }
        }

        // 检查服务是否已初始化
        function checkService() {
            if (!storage) {
                alert('请先连接存储服务');
                return false;
            }
            return true;
        }

        // 基本存储操作演示
        async function demoBasicPut() {
            if (!checkService()) return;

            const key = document.getElementById('basicKey').value.trim();
            const value = document.getElementById('basicValue').value.trim();

            if (!key || !value) {
                log('⚠️ 请输入键名和值', 'warning', 'basicOutput');
                return;
            }

            try {
                log(`🔄 正在存储: ${key} = ${value}`, 'info', 'basicOutput');
                await storage.put(key, value);
                log(`✅ 存储成功!`, 'success', 'basicOutput');
            } catch (error) {
                log(`❌ 存储失败: ${error.message}`, 'error', 'basicOutput');
            }
        }

        async function demoBasicGet() {
            if (!checkService()) return;

            const key = document.getElementById('basicKey').value.trim();

            if (!key) {
                log('⚠️ 请输入键名', 'warning', 'basicOutput');
                return;
            }

            try {
                log(`🔄 正在获取: ${key}`, 'info', 'basicOutput');
                const value = await storage.get(key);
                
                if (value !== null) {
                    log(`✅ 获取成功: ${JSON.stringify(value)}`, 'success', 'basicOutput');
                } else {
                    log(`ℹ️ 键不存在: ${key}`, 'info', 'basicOutput');
                }
            } catch (error) {
                log(`❌ 获取失败: ${error.message}`, 'error', 'basicOutput');
            }
        }

        async function demoBasicDelete() {
            if (!checkService()) return;

            const key = document.getElementById('basicKey').value.trim();

            if (!key) {
                log('⚠️ 请输入键名', 'warning', 'basicOutput');
                return;
            }

            try {
                log(`🔄 正在删除: ${key}`, 'info', 'basicOutput');
                await storage.delete(key);
                log(`✅ 删除成功!`, 'success', 'basicOutput');
            } catch (error) {
                log(`❌ 删除失败: ${error.message}`, 'error', 'basicOutput');
            }
        }

        // 对象存储演示
        async function demoObjectStorage() {
            if (!checkService()) return;

            const userConfig = {
                username: '张三',
                email: '<EMAIL>',
                preferences: {
                    theme: 'dark',
                    language: 'zh-CN',
                    notifications: true
                },
                gameProgress: {
                    level: 15,
                    score: 98765,
                    achievements: ['first_win', 'speed_demon', 'perfectionist']
                }
            };

            try {
                log('🔄 正在存储用户配置对象...', 'info', 'objectOutput');
                await storage.put('user_config', userConfig);
                log('✅ 用户配置存储成功!', 'success', 'objectOutput');
                log(`📦 存储的数据: ${JSON.stringify(userConfig, null, 2)}`, 'info', 'objectOutput');
            } catch (error) {
                log(`❌ 存储失败: ${error.message}`, 'error', 'objectOutput');
            }
        }

        async function demoGetUserConfig() {
            if (!checkService()) return;

            try {
                log('🔄 正在获取用户配置...', 'info', 'objectOutput');
                const config = await storage.get('user_config');
                
                if (config) {
                    log('✅ 用户配置获取成功!', 'success', 'objectOutput');
                    log(`👤 用户名: ${config.username}`, 'info', 'objectOutput');
                    log(`📧 邮箱: ${config.email}`, 'info', 'objectOutput');
                    log(`🎨 主题: ${config.preferences.theme}`, 'info', 'objectOutput');
                    log(`🎮 游戏等级: ${config.gameProgress.level}`, 'info', 'objectOutput');
                } else {
                    log('ℹ️ 用户配置不存在', 'info', 'objectOutput');
                }
            } catch (error) {
                log(`❌ 获取失败: ${error.message}`, 'error', 'objectOutput');
            }
        }

        // 检测 Functions 状态
        async function checkFunctionsStatus() {
            if (!storage) {
                // 如果还没有存储实例，创建一个临时的来检测
                updateFunctionsStatus('checking');
                log('🔍 开始检测 Cloudflare Functions 部署状态...', 'info');

                try {
                    const tempStorage = new EdgeOneStorageImpl({
                        enableFallback: true,
                        timeout: 5000
                    });

                    log('📋 执行简化检测：HTTP状态 → JSON格式 → API字段', 'info');
                    const isDeployed = await tempStorage.checkFunctionsDeployment();
                    const status = tempStorage.getStorageStatus();

                    if (isDeployed) {
                        updateFunctionsStatus('deployed', 'Functions 已部署（基础检测通过）');
                        log('✅ Functions 已部署并通过基础检测', 'success');
                    } else {
                        updateFunctionsStatus('not-deployed', 'Functions 未部署或检测失败');
                        log('❌ Functions 未部署或基础检测失败', 'error');
                    }

                    log(`📊 检测详情: 存储模式 ${status.storageMode}`, 'info');
                    log(`🌐 服务可用性: ${status.isAvailable}`, 'info');

                } catch (error) {
                    updateFunctionsStatus('not-deployed', '检测失败');
                    log(`❌ Functions 状态检测失败: ${error.message}`, 'error');
                }
            } else {
                // 如果已有存储实例，直接检测
                updateFunctionsStatus('checking');
                log('🔍 检测当前存储服务的 Functions 状态...', 'info');

                try {
                    const isDeployed = await storage.checkFunctionsDeployment();
                    const status = storage.getStorageStatus();

                    if (isDeployed) {
                        updateFunctionsStatus('deployed', 'Functions 基础检测通过');
                        log('✅ Functions 基础检测通过', 'success');
                    } else {
                        updateFunctionsStatus('not-deployed', 'Functions 基础检测失败');
                        log('❌ Functions 基础检测失败', 'error');
                    }

                    log(`📊 当前状态: 存储模式 ${status.storageMode}`, 'info');
                    log(`🌐 服务可用性: ${status.isAvailable}`, 'info');

                } catch (error) {
                    updateFunctionsStatus('not-deployed', '检测失败');
                    log(`❌ Functions 状态检测失败: ${error.message}`, 'error');
                }
            }
        }

        // 调试存储初始化
        async function debugStorageInit() {
            log('🔧 开始调试存储初始化流程...', 'info');

            if (!storage) {
                log('⚠️ 存储服务未初始化，创建临时实例进行调试', 'warning');

                try {
                    const tempStorage = new EdgeOneStorageImpl({
                        enableFallback: true,
                        timeout: 5000
                    });

                    log('🔧 执行调试初始化...', 'info');
                    const debugInfo = await tempStorage.debugInitialization();

                    log('✅ 调试完成', 'success');
                    log(`📊 检测前: ${JSON.stringify(debugInfo.beforeCheck)}`, 'info');
                    log(`🔍 检测结果: ${JSON.stringify(debugInfo.checkResult)}`, 'info');
                    log(`📊 检测后: ${JSON.stringify(debugInfo.afterCheck)}`, 'info');

                    if (debugInfo.errors.length > 0) {
                        log(`❌ 错误: ${debugInfo.errors.join(', ')}`, 'error');
                    }

                } catch (error) {
                    log(`❌ 调试失败: ${error.message}`, 'error');
                }
            } else {
                try {
                    log('🔧 使用现有存储实例进行调试...', 'info');
                    const debugInfo = await storage.debugInitialization();

                    log('✅ 调试完成', 'success');
                    log(`📊 检测前: ${JSON.stringify(debugInfo.beforeCheck)}`, 'info');
                    log(`🔍 检测结果: ${JSON.stringify(debugInfo.checkResult)}`, 'info');
                    log(`📊 检测后: ${JSON.stringify(debugInfo.afterCheck)}`, 'info');

                    if (debugInfo.errors.length > 0) {
                        log(`❌ 错误: ${debugInfo.errors.join(', ')}`, 'error');
                    }

                    // 更新状态显示
                    const stats = storage.getStats();
                    if (stats.storageMode === 'remote') {
                        updateFunctionsStatus('deployed', '调试检测通过');
                    } else {
                        updateFunctionsStatus('not-deployed', '调试检测失败');
                    }

                } catch (error) {
                    log(`❌ 调试失败: ${error.message}`, 'error');
                }
            }
        }

        // 页面加载时的初始化
        window.addEventListener('load', function() {
            log('🚀 存储服务演示页面已加载');
            log('📋 请配置 API 地址并连接服务开始演示');
            log('💡 提示：可以先点击"检测 Functions"或"调试初始化"查看部署状态');
        });
    </script>
</body>
</html>
