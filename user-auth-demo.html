<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Split-Second Spark - 用户认证与数据同步演示</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .demo-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .demo-section h2 {
            font-size: 1.8em;
            margin-bottom: 20px;
            color: #4ecdc4;
        }

        .auth-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .auth-card {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .auth-card h3 {
            color: #ff6b6b;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            opacity: 0.9;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-radius: 5px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
        }

        .form-group input::placeholder {
            color: rgba(255, 255, 255, 0.6);
        }

        .btn {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            border: none;
            color: white;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1em;
            transition: all 0.3s ease;
            margin: 5px;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
        }

        .btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
            transform: none;
        }

        .btn-secondary {
            background: linear-gradient(45deg, #6c757d, #495057);
        }

        .user-info {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .user-avatar {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.5em;
            margin-right: 15px;
        }

        .user-details {
            flex: 1;
        }

        .user-status {
            display: flex;
            align-items: center;
            margin-bottom: 20px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-online {
            background: #51cf66;
        }

        .status-offline {
            background: #ff6b6b;
        }

        .status-guest {
            background: #ffd43b;
        }

        .data-demo {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 20px;
        }

        .data-panel {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
        }

        .log-area {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 3px;
        }

        .log-info { color: #4ecdc4; }
        .log-success { color: #51cf66; }
        .log-warning { color: #ffd43b; }
        .log-error { color: #ff6b6b; }

        .sync-status {
            display: flex;
            align-items: center;
            justify-content: space-between;
            background: rgba(255, 255, 255, 0.05);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
        }

        .sync-indicator {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #51cf66;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .device-list {
            background: rgba(255, 255, 255, 0.05);
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
        }

        .device-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .device-item:last-child {
            border-bottom: none;
        }

        @media (max-width: 768px) {
            .auth-grid {
                grid-template-columns: 1fr;
            }
            
            .data-demo {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 用户认证与数据同步</h1>
            <p>体验跨设备的游戏数据同步功能</p>
        </div>

        <!-- 用户状态显示 -->
        <div class="demo-section">
            <h2>👤 用户状态</h2>
            <div class="user-status" id="userStatus">
                <div class="status-indicator status-offline"></div>
                <span>未登录</span>
            </div>
            
            <div class="user-info" id="userInfo" style="display: none;">
                <div style="display: flex; align-items: center;">
                    <div class="user-avatar" id="userAvatar">👤</div>
                    <div class="user-details">
                        <h3 id="userName">用户名</h3>
                        <p id="userEmail">邮箱</p>
                        <small id="userType">用户类型</small>
                    </div>
                </div>
            </div>

            <div class="sync-status" id="syncStatus" style="display: none;">
                <div>
                    <strong>数据同步状态</strong>
                    <br><small id="syncStatusText">正在同步...</small>
                </div>
                <div class="sync-indicator"></div>
            </div>
        </div>

        <!-- 认证操作 -->
        <div class="demo-section">
            <h2>🔑 用户认证</h2>
            <div class="auth-grid">
                <!-- 访客模式 -->
                <div class="auth-card">
                    <h3>👥 访客模式</h3>
                    <p>快速开始游戏，数据保存在本地</p>
                    <button class="btn" onclick="signInAsGuest()">以访客身份开始</button>
                </div>

                <!-- 匿名登录 -->
                <div class="auth-card">
                    <h3>🎭 匿名登录</h3>
                    <p>创建临时账户，支持云端数据同步</p>
                    <button class="btn" onclick="signInAnonymously()">匿名登录</button>
                </div>

                <!-- 邮箱注册 -->
                <div class="auth-card">
                    <h3>📧 邮箱注册</h3>
                    <div class="form-group">
                        <label>邮箱地址</label>
                        <input type="email" id="signupEmail" placeholder="输入邮箱地址">
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input type="password" id="signupPassword" placeholder="输入密码">
                    </div>
                    <div class="form-group">
                        <label>用户名</label>
                        <input type="text" id="signupDisplayName" placeholder="输入用户名">
                    </div>
                    <button class="btn" onclick="signUpWithEmail()">注册账户</button>
                </div>

                <!-- 邮箱登录 -->
                <div class="auth-card">
                    <h3>🔐 邮箱登录</h3>
                    <div class="form-group">
                        <label>邮箱地址</label>
                        <input type="email" id="signinEmail" placeholder="输入邮箱地址">
                    </div>
                    <div class="form-group">
                        <label>密码</label>
                        <input type="password" id="signinPassword" placeholder="输入密码">
                    </div>
                    <button class="btn" onclick="signInWithEmail()">登录</button>
                </div>
            </div>

            <!-- 账户升级 -->
            <div class="auth-card" id="upgradeCard" style="display: none;">
                <h3>⬆️ 账户升级</h3>
                <p>将匿名账户升级为正式账户，永久保存数据</p>
                <div class="form-group">
                    <label>邮箱地址</label>
                    <input type="email" id="upgradeEmail" placeholder="输入邮箱地址">
                </div>
                <div class="form-group">
                    <label>密码</label>
                    <input type="password" id="upgradePassword" placeholder="设置密码">
                </div>
                <div class="form-group">
                    <label>用户名</label>
                    <input type="text" id="upgradeDisplayName" placeholder="设置用户名">
                </div>
                <button class="btn" onclick="upgradeAccount()">升级账户</button>
            </div>

            <div style="text-align: center; margin-top: 20px;">
                <button class="btn btn-secondary" onclick="signOut()" id="signOutBtn" style="display: none;">登出</button>
            </div>
        </div>

        <!-- 数据同步演示 -->
        <div class="demo-section">
            <h2>🔄 数据同步演示</h2>
            <div class="data-demo">
                <div class="data-panel">
                    <h3>💾 游戏数据操作</h3>
                    <div class="form-group">
                        <label>数据键名</label>
                        <input type="text" id="dataKey" placeholder="例如：player-level">
                    </div>
                    <div class="form-group">
                        <label>数据值</label>
                        <input type="text" id="dataValue" placeholder="例如：10">
                    </div>
                    <button class="btn" onclick="saveGameData()">保存数据</button>
                    <button class="btn btn-secondary" onclick="loadGameData()">读取数据</button>
                    <button class="btn btn-secondary" onclick="deleteGameData()">删除数据</button>
                </div>

                <div class="data-panel">
                    <h3>📊 数据统计</h3>
                    <div id="dataStats">
                        <p>请先登录查看数据统计</p>
                    </div>
                    <button class="btn btn-secondary" onclick="refreshStats()">刷新统计</button>
                    <button class="btn btn-secondary" onclick="generateTestData()">生成测试数据</button>
                </div>
            </div>
        </div>

        <!-- 设备管理 -->
        <div class="demo-section">
            <h2>📱 设备管理</h2>
            <div class="device-list" id="deviceList">
                <p>登录后可查看已同步的设备列表</p>
            </div>
        </div>

        <!-- 操作日志 -->
        <div class="demo-section">
            <h2>📝 操作日志</h2>
            <div class="log-area" id="logArea">
                <div class="log-entry log-info">🔐 用户认证系统已准备就绪</div>
            </div>
            <button class="btn btn-secondary" onclick="clearLog()">清空日志</button>
        </div>
    </div>

    <script type="module">
        // 全局变量
        let userIdentityManager = null;
        let userCloudStorage = null;
        let isInitialized = false;

        // Firebase 配置（示例配置，实际使用时需要替换）
        const firebaseConfig = {
            apiKey: "demo-api-key",
            authDomain: "split-second-spark-demo.firebaseapp.com",
            projectId: "split-second-spark-demo",
            storageBucket: "split-second-spark-demo.appspot.com",
            messagingSenderId: "123456789",
            appId: "demo-app-id"
        };

        // 初始化系统
        async function initSystem() {
            try {
                log('🔐 初始化用户认证系统...', 'info');
                
                // 动态导入用户身份管理器
                const { default: UserIdentityManager } = await import('./js/utils/user-identity-manager.js');
                const { default: UserCloudStorageAdapter } = await import('./js/utils/user-cloud-storage.js');
                
                // 创建用户身份管理器
                userIdentityManager = new UserIdentityManager({
                    enableAnonymousAuth: true,
                    enableEmailAuth: true,
                    enableGuestMode: true,
                    autoUpgrade: true
                });
                
                // 初始化（注意：这里使用模拟配置，实际使用时需要真实的 Firebase 配置）
                try {
                    await userIdentityManager.init(firebaseConfig);
                    
                    // 创建用户云存储适配器
                    userCloudStorage = new UserCloudStorageAdapter(userIdentityManager, {
                        dataEncryption: false,
                        compressionEnabled: true,
                        enableDataVersioning: true
                    });
                    
                    await userCloudStorage.init();
                    
                } catch (error) {
                    log('⚠️ Firebase 连接失败，使用本地模式', 'warning');
                    // 在实际应用中，这里可以降级到本地存储
                }
                
                // 设置事件监听器
                setupEventListeners();
                
                isInitialized = true;
                log('✅ 用户认证系统初始化完成', 'success');
                
            } catch (error) {
                log(`❌ 系统初始化失败: ${error.message}`, 'error');
                console.error('初始化错误:', error);
            }
        }

        // 设置事件监听器
        function setupEventListeners() {
            // 监听用户登录事件
            window.addEventListener('userIdentity:userSignedIn', (event) => {
                const { user, isAnonymous } = event.detail;
                updateUserUI(user, isAnonymous);
                log(`👤 用户已登录: ${user.displayName || user.uid}`, 'success');
            });

            // 监听用户登出事件
            window.addEventListener('userIdentity:userSignedOut', () => {
                updateUserUI(null);
                log('👤 用户已登出', 'info');
            });
        }

        // 更新用户界面
        function updateUserUI(user, isAnonymous = false) {
            const userStatus = document.getElementById('userStatus');
            const userInfo = document.getElementById('userInfo');
            const upgradeCard = document.getElementById('upgradeCard');
            const signOutBtn = document.getElementById('signOutBtn');
            const syncStatus = document.getElementById('syncStatus');

            if (user) {
                // 更新状态指示器
                const indicator = userStatus.querySelector('.status-indicator');
                const statusText = userStatus.querySelector('span');
                
                if (user.isGuest) {
                    indicator.className = 'status-indicator status-guest';
                    statusText.textContent = '访客模式';
                } else if (isAnonymous) {
                    indicator.className = 'status-indicator status-guest';
                    statusText.textContent = '匿名用户';
                } else {
                    indicator.className = 'status-indicator status-online';
                    statusText.textContent = '已登录';
                }

                // 显示用户信息
                document.getElementById('userName').textContent = user.displayName || '匿名用户';
                document.getElementById('userEmail').textContent = user.email || '无邮箱';
                document.getElementById('userType').textContent = user.isGuest ? '访客' : (isAnonymous ? '匿名用户' : '注册用户');
                
                // 设置头像
                const avatar = document.getElementById('userAvatar');
                avatar.textContent = user.displayName ? user.displayName.charAt(0).toUpperCase() : '👤';

                userInfo.style.display = 'block';
                signOutBtn.style.display = 'inline-block';
                
                // 显示升级选项（仅匿名用户）
                if (isAnonymous && !user.isGuest) {
                    upgradeCard.style.display = 'block';
                }
                
                // 显示同步状态
                if (!user.isGuest) {
                    syncStatus.style.display = 'flex';
                    document.getElementById('syncStatusText').textContent = '数据已同步到云端';
                }

            } else {
                // 用户未登录
                const indicator = userStatus.querySelector('.status-indicator');
                const statusText = userStatus.querySelector('span');
                
                indicator.className = 'status-indicator status-offline';
                statusText.textContent = '未登录';
                
                userInfo.style.display = 'none';
                upgradeCard.style.display = 'none';
                signOutBtn.style.display = 'none';
                syncStatus.style.display = 'none';
            }
            
            // 刷新数据统计
            refreshStats();
        }

        // 访客登录
        window.signInAsGuest = function() {
            if (!isInitialized) {
                log('❌ 系统未初始化', 'error');
                return;
            }
            
            log('👥 以访客身份登录...', 'info');
            const guestUser = userIdentityManager.createGuestUser();
            updateUserUI(guestUser);
        };

        // 匿名登录
        window.signInAnonymously = async function() {
            if (!isInitialized) {
                log('❌ 系统未初始化', 'error');
                return;
            }
            
            try {
                log('🎭 匿名登录中...', 'info');
                await userIdentityManager.autoSignIn();
            } catch (error) {
                log(`❌ 匿名登录失败: ${error.message}`, 'error');
            }
        };

        // 邮箱注册
        window.signUpWithEmail = async function() {
            const email = document.getElementById('signupEmail').value.trim();
            const password = document.getElementById('signupPassword').value.trim();
            const displayName = document.getElementById('signupDisplayName').value.trim();
            
            if (!email || !password) {
                log('❌ 请输入邮箱和密码', 'error');
                return;
            }
            
            try {
                log('📧 邮箱注册中...', 'info');
                await userIdentityManager.signUpWithEmail(email, password, displayName);
                
                // 清空表单
                document.getElementById('signupEmail').value = '';
                document.getElementById('signupPassword').value = '';
                document.getElementById('signupDisplayName').value = '';
                
            } catch (error) {
                log(`❌ 注册失败: ${error.message}`, 'error');
            }
        };

        // 邮箱登录
        window.signInWithEmail = async function() {
            const email = document.getElementById('signinEmail').value.trim();
            const password = document.getElementById('signinPassword').value.trim();
            
            if (!email || !password) {
                log('❌ 请输入邮箱和密码', 'error');
                return;
            }
            
            try {
                log('🔐 邮箱登录中...', 'info');
                await userIdentityManager.signInWithEmail(email, password);
                
                // 清空表单
                document.getElementById('signinEmail').value = '';
                document.getElementById('signinPassword').value = '';
                
            } catch (error) {
                log(`❌ 登录失败: ${error.message}`, 'error');
            }
        };

        // 升级账户
        window.upgradeAccount = async function() {
            const email = document.getElementById('upgradeEmail').value.trim();
            const password = document.getElementById('upgradePassword').value.trim();
            const displayName = document.getElementById('upgradeDisplayName').value.trim();
            
            if (!email || !password) {
                log('❌ 请输入邮箱和密码', 'error');
                return;
            }
            
            try {
                log('⬆️ 升级账户中...', 'info');
                await userIdentityManager.upgradeAnonymousUser(email, password, displayName);
                
                // 清空表单并隐藏升级卡片
                document.getElementById('upgradeEmail').value = '';
                document.getElementById('upgradePassword').value = '';
                document.getElementById('upgradeDisplayName').value = '';
                document.getElementById('upgradeCard').style.display = 'none';
                
            } catch (error) {
                log(`❌ 账户升级失败: ${error.message}`, 'error');
            }
        };

        // 登出
        window.signOut = async function() {
            try {
                log('👤 登出中...', 'info');
                await userIdentityManager.signOut();
            } catch (error) {
                log(`❌ 登出失败: ${error.message}`, 'error');
            }
        };

        // 保存游戏数据
        window.saveGameData = async function() {
            const key = document.getElementById('dataKey').value.trim();
            const value = document.getElementById('dataValue').value.trim();
            
            if (!key || !value) {
                log('❌ 请输入数据键名和值', 'error');
                return;
            }
            
            try {
                if (userCloudStorage && userIdentityManager.currentUser && !userIdentityManager.currentUser.isGuest) {
                    // 使用云存储
                    await userCloudStorage.put(key, value);
                    log(`💾 数据已保存到云端: ${key} = ${value}`, 'success');
                } else {
                    // 使用本地存储
                    localStorage.setItem(`game_${key}`, value);
                    log(`💾 数据已保存到本地: ${key} = ${value}`, 'success');
                }
                
                // 清空表单
                document.getElementById('dataKey').value = '';
                document.getElementById('dataValue').value = '';
                
                refreshStats();
                
            } catch (error) {
                log(`❌ 数据保存失败: ${error.message}`, 'error');
            }
        };

        // 读取游戏数据
        window.loadGameData = async function() {
            const key = document.getElementById('dataKey').value.trim();
            
            if (!key) {
                log('❌ 请输入数据键名', 'error');
                return;
            }
            
            try {
                let value;
                
                if (userCloudStorage && userIdentityManager.currentUser && !userIdentityManager.currentUser.isGuest) {
                    // 从云存储读取
                    value = await userCloudStorage.get(key);
                } else {
                    // 从本地存储读取
                    value = localStorage.getItem(`game_${key}`);
                }
                
                if (value !== null) {
                    log(`📥 读取数据成功: ${key} = ${value}`, 'success');
                    document.getElementById('dataValue').value = value;
                } else {
                    log(`⚠️ 未找到数据: ${key}`, 'warning');
                }
                
            } catch (error) {
                log(`❌ 数据读取失败: ${error.message}`, 'error');
            }
        };

        // 删除游戏数据
        window.deleteGameData = async function() {
            const key = document.getElementById('dataKey').value.trim();
            
            if (!key) {
                log('❌ 请输入数据键名', 'error');
                return;
            }
            
            try {
                if (userCloudStorage && userIdentityManager.currentUser && !userIdentityManager.currentUser.isGuest) {
                    // 从云存储删除
                    await userCloudStorage.delete(key);
                } else {
                    // 从本地存储删除
                    localStorage.removeItem(`game_${key}`);
                }
                
                log(`🗑️ 数据已删除: ${key}`, 'success');
                document.getElementById('dataValue').value = '';
                
                refreshStats();
                
            } catch (error) {
                log(`❌ 数据删除失败: ${error.message}`, 'error');
            }
        };

        // 刷新统计信息
        window.refreshStats = async function() {
            const statsContainer = document.getElementById('dataStats');
            
            try {
                let stats;
                
                if (userCloudStorage && userIdentityManager.currentUser && !userIdentityManager.currentUser.isGuest) {
                    stats = await userCloudStorage.getStorageInfo();
                } else {
                    // 统计本地数据
                    const localKeys = Object.keys(localStorage).filter(key => key.startsWith('game_'));
                    stats = {
                        type: 'Local Storage',
                        keyCount: localKeys.length,
                        isAvailable: true
                    };
                }
                
                statsContainer.innerHTML = `
                    <p><strong>存储类型:</strong> ${stats.type}</p>
                    <p><strong>数据条数:</strong> ${stats.keyCount}</p>
                    <p><strong>状态:</strong> ${stats.isAvailable ? '可用' : '不可用'}</p>
                    ${stats.user ? `<p><strong>用户:</strong> ${stats.user.displayName || stats.user.uid}</p>` : ''}
                `;
                
            } catch (error) {
                statsContainer.innerHTML = `<p style="color: #ff6b6b;">统计信息获取失败: ${error.message}</p>`;
            }
        };

        // 生成测试数据
        window.generateTestData = async function() {
            const testData = {
                'player-name': '测试玩家',
                'player-level': '15',
                'player-score': '25000',
                'game-settings': JSON.stringify({
                    volume: 0.8,
                    difficulty: 'hard'
                })
            };
            
            try {
                for (const [key, value] of Object.entries(testData)) {
                    if (userCloudStorage && userIdentityManager.currentUser && !userIdentityManager.currentUser.isGuest) {
                        await userCloudStorage.put(key, value);
                    } else {
                        localStorage.setItem(`game_${key}`, value);
                    }
                }
                
                log(`🎲 已生成 ${Object.keys(testData).length} 条测试数据`, 'success');
                refreshStats();
                
            } catch (error) {
                log(`❌ 测试数据生成失败: ${error.message}`, 'error');
            }
        };

        // 日志函数
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
            
            console.log(message);
        }

        // 清空日志
        window.clearLog = function() {
            document.getElementById('logArea').innerHTML = '';
        };

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initSystem);
    </script>
</body>
</html>
