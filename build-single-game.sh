#!/bin/bash

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
WHITE='\033[1;37m'
NC='\033[0m' # No Color

# 显示标题
echo -e "${CYAN}============================================================${NC}"
echo -e "${WHITE}🎮 Split-Second Spark 单游戏部署打包工具${NC}"
echo -e "${CYAN}============================================================${NC}"
echo

# 检查Python是否安装
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_CMD="python3"
        PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
    elif command -v python &> /dev/null; then
        PYTHON_CMD="python"
        PYTHON_VERSION=$(python --version 2>&1 | cut -d' ' -f2)
    else
        echo -e "${RED}❌ 错误: 未找到Python${NC}"
        echo -e "${YELLOW}请先安装Python 3.6或更高版本${NC}"
        echo -e "${BLUE}Ubuntu/Debian: sudo apt install python3${NC}"
        echo -e "${BLUE}CentOS/RHEL: sudo yum install python3${NC}"
        echo -e "${BLUE}macOS: brew install python3${NC}"
        echo
        exit 1
    fi
    
    echo -e "${GREEN}✅ 检测到Python版本: ${PYTHON_VERSION}${NC}"
    echo
}

# 运行打包脚本
run_packager() {
    echo -e "${BLUE}🚀 启动单游戏打包程序...${NC}"
    echo
    
    $PYTHON_CMD build-single-game.py
    
    if [ $? -ne 0 ]; then
        echo
        echo -e "${RED}❌ 打包失败！${NC}"
        echo -e "${YELLOW}请检查错误信息并重试${NC}"
        echo
        exit 1
    fi
}

# 显示完成信息
show_completion() {
    echo
    echo -e "${CYAN}============================================================${NC}"
    echo -e "${GREEN}🎉 单游戏打包完成！${NC}"
    echo -e "${CYAN}============================================================${NC}"
    echo
}

# 询问是否打开输出目录
open_output_directory() {
    read -p "是否打开输出目录? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${BLUE}📁 正在打开输出目录...${NC}"
        
        # 根据操作系统选择打开命令
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            open .
        elif [[ "$OSTYPE" == "linux-gnu"* ]]; then
            # Linux
            if command -v xdg-open &> /dev/null; then
                xdg-open .
            elif command -v nautilus &> /dev/null; then
                nautilus . &
            elif command -v dolphin &> /dev/null; then
                dolphin . &
            else
                echo -e "${YELLOW}⚠️ 无法自动打开文件管理器${NC}"
                echo -e "${BLUE}请手动查看当前目录的文件${NC}"
            fi
        else
            echo -e "${YELLOW}⚠️ 不支持的操作系统，无法自动打开目录${NC}"
        fi
    fi
}

# 启动本地服务器测试
start_local_server() {
    echo
    read -p "是否启动本地服务器进行测试? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo
        echo -e "${BLUE}🌐 启动本地服务器...${NC}"
        echo -e "${GREEN}服务器地址: http://localhost:8000${NC}"
        echo -e "${YELLOW}按 Ctrl+C 停止服务器${NC}"
        echo
        
        # 查找独立部署目录
        STANDALONE_DIR=$(find . -maxdepth 1 -name "*-standalone" -type d | head -n 1)
        
        if [ -n "$STANDALONE_DIR" ]; then
            echo -e "${BLUE}📂 进入目录: ${STANDALONE_DIR}${NC}"
            cd "$STANDALONE_DIR"
            
            # 启动服务器
            if command -v python3 &> /dev/null; then
                python3 -m http.server 8000
            else
                python -m http.server 8000
            fi
            
            cd ..
        else
            echo -e "${RED}❌ 未找到独立部署目录${NC}"
        fi
    fi
}

# 显示结束信息
show_farewell() {
    echo
    echo -e "${PURPLE}👋 感谢使用 Split-Second Spark 单游戏部署打包工具！${NC}"
    echo
}

# 主函数
main() {
    check_python
    run_packager
    show_completion
    open_output_directory
    start_local_server
    show_farewell
}

# 错误处理
trap 'echo -e "\n${RED}❌ 脚本执行被中断${NC}"; exit 1' INT TERM

# 运行主函数
main
