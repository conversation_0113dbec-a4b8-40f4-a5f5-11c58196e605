# Split-Second Spark 单游戏部署指南

> 🎯 专门用于单独部署某个游戏的完整解决方案

## 📋 概述

有时您可能只想部署 Split-Second Spark 中的某一个游戏，而不是整个游戏集合。单游戏部署工具可以帮您：

- 🎮 **选择性部署** - 只部署您需要的游戏
- 📦 **独立打包** - 生成完全独立的部署包
- 🚀 **快速部署** - 更小的文件大小，更快的部署速度
- 🎨 **专属界面** - 为单个游戏定制的入口页面

## 🎮 可用游戏

| 游戏名称 | 英文名称 | 描述 | 特性 |
|---------|---------|------|------|
| 时空织梦者 | Temporal Dream Weaver | 时间操控解谜游戏 | 时间操控、策略解谜、梦境编织 |
| 瞬光捕手 | Split-Second Spark | 反应速度挑战游戏 | 精准时机、连击系统、反应挑战 |
| 量子共鸣者 | Quantum Resonance | 音乐节奏物理模拟游戏 | 量子共鸣、音乐节奏、物理模拟 |

## 🛠️ 使用方法

### Windows 用户
```bash
# 双击运行批处理脚本
build-single-game.bat
```

### Linux/Mac 用户
```bash
# 给脚本执行权限
chmod +x build-single-game.sh

# 运行脚本
./build-single-game.sh
```

### 手动运行 (所有平台)
```bash
# 直接运行Python脚本
python3 build-single-game.py

# 或使用Python 2/3兼容方式
python build-single-game.py
```

## 📦 打包过程

### 1. 游戏选择
运行脚本后，会显示可用的游戏列表：
```
🎮 可用的游戏:
  1. 时空织梦者 ✅
  2. 瞬光捕手 ✅
  3. 量子共鸣者 ✅

请选择要打包的游戏 (输入数字 1-3):
```

### 2. 自动打包
选择游戏后，工具会自动：
- 📁 复制游戏核心文件
- 🚫 排除测试和开发文件
- 🎨 生成专属入口页面
- 📋 创建部署信息文件
- 📖 生成说明文档
- 📦 创建压缩包

### 3. 打包结果
```
🎉 单游戏打包完成！
📁 部署目录: 瞬光捕手-standalone
📦 压缩包: 瞬光捕手-standalone.zip
📊 文件统计: 复制 19 个文件, 排除 29 个文件
```

## 📁 生成的文件结构

```
游戏名-standalone/
├── index.html              # 独立入口页面
├── deployment-info.json    # 部署信息
├── README.md              # 说明文档
└── 游戏名/                 # 游戏文件目录
    ├── index.html         # 游戏主页面
    ├── js/               # JavaScript文件
    │   ├── utils/        # 工具模块
    │   ├── core/         # 核心模块
    │   ├── ui/           # 界面模块
    │   └── main.js       # 主程序
    ├── styles/           # 样式文件
    │   ├── main.css      # 主样式
    │   └── responsive.css # 响应式样式
    └── assets/           # 资源文件 (如果有)
```

## 🌐 部署方式

### 方式一：直接上传
```bash
# 1. 解压生成的压缩包
unzip 游戏名-standalone.zip

# 2. 上传到Web服务器
# 3. 访问 http://your-domain.com/index.html
```

### 方式二：GitHub Pages
```bash
# 1. 创建新的GitHub仓库
# 2. 上传解压后的文件
# 3. 启用GitHub Pages
# 4. 访问 https://username.github.io/repository-name/
```

### 方式三：Vercel/Netlify
```bash
# Vercel
cd 游戏名-standalone && vercel --prod

# Netlify
cd 游戏名-standalone && netlify deploy --prod

# 或直接拖拽文件夹到对应平台
```

## 🎯 访问方式

部署完成后，有两种访问方式：

### 1. 通过入口页面 (推荐)
```
http://your-domain.com/index.html
```
- 显示游戏介绍和特性
- 美观的专属界面
- 一键进入游戏

### 2. 直接访问游戏
```
http://your-domain.com/游戏名/index.html
```
- 直接进入游戏界面
- 适合书签收藏
- 更快的加载速度

## 📊 优势对比

| 特性 | 完整部署 | 单游戏部署 |
|------|---------|-----------|
| 文件大小 | 403KB | ~96KB |
| 包含游戏 | 3个游戏 | 1个游戏 |
| 加载速度 | 较慢 | 更快 |
| 存储空间 | 较大 | 更小 |
| 部署复杂度 | 中等 | 简单 |
| 适用场景 | 游戏集合展示 | 单游戏专门部署 |

## 🔧 自定义配置

### 修改入口页面样式
编辑生成的 `index.html` 文件，可以自定义：
- 页面标题和描述
- 颜色主题
- 布局样式
- 游戏介绍内容

### 修改游戏配置
在游戏目录的 `js/` 文件夹中，可以调整：
- 游戏难度参数
- 界面语言设置
- 音效和视觉效果
- 控制方式

## 🚨 注意事项

### 文件过滤
单游戏打包工具会自动排除：
- 测试文件 (`*test*.html`, `*debug*.html`)
- 开发工具 (`*.py`, `*.bat`, `*.sh`)
- 文档文件 (`*.md`)
- 临时文件和缓存

### 依赖关系
每个游戏都是独立的，不依赖其他游戏文件，可以安全地单独部署。

### 浏览器兼容性
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

## 🔍 故障排除

### 问题1：游戏选择时显示"目录不存在"
**原因**: 游戏目录被移动或删除
**解决方案**: 确保在项目根目录运行脚本，且游戏目录完整

### 问题2：打包后游戏无法启动
**原因**: 文件路径错误或缺少必要文件
**解决方案**: 
- 检查浏览器控制台错误信息
- 确保所有 `js/` 和 `styles/` 文件都已复制
- 验证文件编码为UTF-8

### 问题3：部署后页面空白
**原因**: 服务器配置问题或文件路径错误
**解决方案**:
- 确保服务器支持中文文件名
- 检查文件权限设置
- 验证所有文件都已上传

## 📈 性能优化

### 服务器配置
```nginx
# Nginx 配置示例
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/game-standalone;
    index index.html;
    
    # 启用gzip压缩
    gzip on;
    gzip_types text/css application/javascript;
    
    # 缓存静态资源
    location ~* \.(js|css|png|jpg|gif|ico)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### CDN加速
- 将静态资源部署到CDN
- 配置合适的缓存策略
- 启用HTTP/2支持

## 🎉 使用示例

### 示例1：部署瞬光捕手到GitHub Pages
```bash
# 1. 运行打包工具
python3 build-single-game.py
# 选择: 2 (瞬光捕手)

# 2. 创建GitHub仓库
git init
git add .
git commit -m "Deploy 瞬光捕手 standalone"

# 3. 推送到GitHub
git remote add origin https://github.com/username/spark-game.git
git push -u origin main

# 4. 启用GitHub Pages
# 访问: https://username.github.io/spark-game/
```

### 示例2：本地测试
```bash
# 1. 打包游戏
python3 build-single-game.py

# 2. 进入生成的目录
cd 瞬光捕手-standalone

# 3. 启动本地服务器
python3 -m http.server 8000

# 4. 访问测试
# 浏览器打开: http://localhost:8000
```

## 📞 技术支持

如果遇到问题，请：
1. 查看生成的 `README.md` 文件
2. 检查 `deployment-info.json` 中的配置信息
3. 查看浏览器控制台的错误信息
4. 确认服务器配置和文件权限

---

**🎮 享受单游戏部署带来的便利！**
