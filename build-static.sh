#!/bin/bash
# Split-Second Spark 静态部署打包工具 (Linux/Mac版)

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 设置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SOURCE_DIR="$SCRIPT_DIR"
OUTPUT_DIR="$SOURCE_DIR/dist"
PACKAGE_NAME="split-second-spark-static"

echo -e "${CYAN}================================================================${NC}"
echo -e "${CYAN}🎮 Split-Second Spark 静态部署打包工具 (Linux/Mac版)${NC}"
echo -e "${CYAN}================================================================${NC}"
echo

# 检查Python是否安装
if ! command -v python3 &> /dev/null; then
    if ! command -v python &> /dev/null; then
        echo -e "${RED}❌ 错误: 未找到Python，请先安装Python 3.6+${NC}"
        echo -e "${YELLOW}   Ubuntu/Debian: sudo apt install python3${NC}"
        echo -e "${YELLOW}   CentOS/RHEL: sudo yum install python3${NC}"
        echo -e "${YELLOW}   macOS: brew install python3${NC}"
        exit 1
    else
        PYTHON_CMD="python"
    fi
else
    PYTHON_CMD="python3"
fi

echo -e "${GREEN}✅ 检测到Python环境${NC}"
$PYTHON_CMD --version
echo

# 运行Python打包脚本
echo -e "${BLUE}🚀 开始执行打包脚本...${NC}"
$PYTHON_CMD "$SOURCE_DIR/build-static.py"

if [ $? -ne 0 ]; then
    echo
    echo -e "${RED}❌ 打包失败！请检查错误信息${NC}"
    exit 1
fi

echo
echo -e "${CYAN}================================================================${NC}"
echo -e "${GREEN}🎉 打包完成！${NC}"
echo -e "${CYAN}================================================================${NC}"
echo
echo -e "${YELLOW}📁 部署文件位置: ${OUTPUT_DIR}${NC}"
echo -e "${YELLOW}📦 压缩包位置: ${SOURCE_DIR}/${PACKAGE_NAME}.zip${NC}"
echo
echo -e "${PURPLE}📋 接下来的步骤:${NC}"
echo -e "   1. 将压缩包上传到您的Web服务器"
echo -e "   2. 解压到服务器的Web根目录"
echo -e "   3. 访问 http://your-domain.com/index.html"
echo -e "   4. 推荐使用HTTPS以获得最佳体验"
echo
echo -e "${PURPLE}🌐 支持的部署平台:${NC}"
echo -e "   • GitHub Pages"
echo -e "   • Vercel"
echo -e "   • Netlify"
echo -e "   • 阿里云OSS"
echo -e "   • 腾讯云COS"
echo -e "   • 任何静态文件服务器"
echo

# 询问是否打开输出目录
read -p "是否打开输出目录? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v xdg-open &> /dev/null; then
        xdg-open "$OUTPUT_DIR"
    elif command -v open &> /dev/null; then
        open "$OUTPUT_DIR"
    else
        echo -e "${YELLOW}请手动打开目录: $OUTPUT_DIR${NC}"
    fi
fi

# 询问是否启动本地测试服务器
read -p "是否启动本地测试服务器? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo
    echo -e "${BLUE}🌐 启动本地测试服务器...${NC}"
    echo -e "${YELLOW}   访问地址: http://localhost:8000${NC}"
    echo -e "${YELLOW}   按 Ctrl+C 停止服务器${NC}"
    echo
    cd "$OUTPUT_DIR"
    
    # 尝试不同的HTTP服务器
    if command -v python3 &> /dev/null; then
        python3 -m http.server 8000
    elif command -v python &> /dev/null; then
        python -m http.server 8000 2>/dev/null || python -m SimpleHTTPServer 8000
    elif command -v npx &> /dev/null; then
        npx http-server -p 8000
    elif command -v php &> /dev/null; then
        php -S localhost:8000
    else
        echo -e "${RED}❌ 未找到可用的HTTP服务器${NC}"
        echo -e "${YELLOW}请手动启动Web服务器或安装以下工具之一:${NC}"
        echo -e "   • Python: python3 -m http.server 8000"
        echo -e "   • Node.js: npx http-server -p 8000"
        echo -e "   • PHP: php -S localhost:8000"
    fi
fi

echo
echo -e "${GREEN}✨ 感谢使用 Split-Second Spark 打包工具！${NC}"
