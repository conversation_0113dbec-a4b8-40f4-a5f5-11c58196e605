/**
 * Cloudflare Functions - 存储键值对数据 API 端点
 * 
 * 功能说明：
 * - 接收 HTTP 请求，从请求中提取键(key)和值(value)参数
 * - 调用 Cloudflare KV 存储服务存储数据
 * - 返回 JSON 格式的响应，包含操作结果
 * - 支持 CORS 跨域访问
 * 
 * 请求方式：POST
 * 请求参数：
 * - key: 存储的键名（字符串）
 * - value: 存储的值（任意类型，会被序列化为字符串）
 * 
 * 响应格式：
 * {
 *   "success": true/false,
 *   "message": "操作结果描述",
 *   "key": "存储的键名",
 *   "timestamp": "操作时间戳"
 * }
 */

export async function onRequest({ request, params, env }) {
  try {
    // 设置 CORS 响应头
    const corsHeaders = {
      'Content-Type': 'application/json; charset=UTF-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // 处理 OPTIONS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders,
      });
    }

    // 只允许 POST 请求
    if (request.method !== 'POST') {
      return new Response(JSON.stringify({
        success: false,
        message: '仅支持 POST 请求方法',
        error: 'METHOD_NOT_ALLOWED'
      }), {
        status: 405,
        headers: corsHeaders,
      });
    }

    let key, value;

    // 从请求中提取参数
    const contentType = request.headers.get('content-type') || '';
    
    if (contentType.includes('application/json')) {
      // 处理 JSON 格式的请求体
      const requestData = await request.json();
      key = requestData.key;
      value = requestData.value;
    } else if (contentType.includes('application/x-www-form-urlencoded')) {
      // 处理表单格式的请求体
      const formData = await request.formData();
      key = formData.get('key');
      value = formData.get('value');
    } else {
      // 尝试从 URL 查询参数中获取
      const url = new URL(request.url);
      key = url.searchParams.get('key');
      value = url.searchParams.get('value');
    }

    // 验证必需参数
    if (!key) {
      return new Response(JSON.stringify({
        success: false,
        message: '缺少必需参数：key（键名）',
        error: 'MISSING_KEY_PARAMETER'
      }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    if (value === null || value === undefined) {
      return new Response(JSON.stringify({
        success: false,
        message: '缺少必需参数：value（值）',
        error: 'MISSING_VALUE_PARAMETER'
      }), {
        status: 400,
        headers: corsHeaders,
      });
    }

    // 确保 value 是字符串格式（Cloudflare KV 存储要求）
    const stringValue = typeof value === 'string' ? value : JSON.stringify(value);

    // 调用 Cloudflare KV 存储服务存储数据
    // 使用环境变量中的 KV 命名空间，通常命名为 STORAGE 或 KV_STORAGE
    const kvNamespace = edgeOneStorage;

    if (!kvNamespace) {
      throw new Error('KV 存储命名空间未配置，请检查环境变量设置');
    }

    await kvNamespace.put(key, stringValue);

    // 返回成功响应
    const response = {
      success: true,
      message: '数据存储成功',
      key: key,
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    });

  } catch (error) {
    // 错误处理
    console.error('存储数据时发生错误:', error);
    
    const errorResponse = {
      success: false,
      message: '存储数据时发生内部错误',
      error: error.message || 'INTERNAL_SERVER_ERROR',
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}
