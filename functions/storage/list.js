/**
 * Cloudflare Functions - 列出所有存储的键 API 端点
 * 
 * 功能说明：
 * - 接收 HTTP 请求，无需额外参数
 * - 调用 Cloudflare KV 存储服务获取所有存储的键
 * - 返回 JSON 格式的响应，包含键列表和统计信息
 * - 支持 CORS 跨域访问
 * - 支持分页和过滤功能
 * 
 * 请求方式：GET
 * 请求参数（可选）：
 * - prefix: 键名前缀过滤（字符串）
 * - limit: 返回结果数量限制（数字，默认100）
 * - cursor: 分页游标（字符串）
 * 
 * 响应格式：
 * {
 *   "success": true/false,
 *   "message": "操作结果描述",
 *   "keys": ["key1", "key2", ...],
 *   "count": 键的数量,
 *   "hasMore": true/false,
 *   "cursor": "下一页游标",
 *   "timestamp": "操作时间戳"
 * }
 */

export async function onRequest({ request, params, env }) {
  try {
    // 设置 CORS 响应头
    const corsHeaders = {
      'Content-Type': 'application/json; charset=UTF-8',
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    };

    // 处理 OPTIONS 预检请求
    if (request.method === 'OPTIONS') {
      return new Response(null, {
        status: 200,
        headers: corsHeaders,
      });
    }

    // 只允许 GET 请求
    if (request.method !== 'GET') {
      return new Response(JSON.stringify({
        success: false,
        message: '仅支持 GET 请求方法',
        error: 'METHOD_NOT_ALLOWED'
      }), {
        status: 405,
        headers: corsHeaders,
      });
    }

    // 从 URL 查询参数中提取可选参数
    const url = new URL(request.url);
    const prefix = url.searchParams.get('prefix') || '';
    const limitParam = url.searchParams.get('limit');
    const cursor = url.searchParams.get('cursor') || '';

    // 解析限制参数，默认为100，最大为1000
    let limit = 100;
    if (limitParam) {
      const parsedLimit = parseInt(limitParam, 10);
      if (!isNaN(parsedLimit) && parsedLimit > 0) {
        limit = Math.min(parsedLimit, 1000); // 限制最大值为1000
      }
    }

    // 构建 EdgeOne 存储查询选项
    const listOptions = {
      limit: limit
    };

    // 添加前缀过滤
    if (prefix) {
      listOptions.prefix = prefix;
    }

    // 添加分页游标
    if (cursor) {
      listOptions.cursor = cursor;
    }

    // 调用 Cloudflare KV 存储服务获取键列表
    // 使用环境变量中的 KV 命名空间，通常命名为 STORAGE 或 KV_STORAGE
    const kvNamespace = edgeOneStorage;

    if (!kvNamespace) {
      throw new Error('KV 存储命名空间未配置，请检查环境变量设置');
    }

    const listResult = await kvNamespace.list(listOptions);

    // 处理返回结果
    let keys = [];
    let hasMore = false;
    let nextCursor = null;

    if (listResult && listResult.keys) {
      // 如果返回的是对象格式
      keys = listResult.keys.map(item => 
        typeof item === 'string' ? item : item.name || item.key
      );
      hasMore = listResult.list_complete === false || listResult.hasMore === true;
      nextCursor = listResult.cursor || null;
    } else if (Array.isArray(listResult)) {
      // 如果返回的是数组格式
      keys = listResult;
      hasMore = keys.length >= limit;
    } else {
      // 处理其他格式或空结果
      keys = [];
      hasMore = false;
    }

    // 过滤结果（如果 EdgeOne API 不支持前缀过滤）
    if (prefix && !listOptions.prefix) {
      keys = keys.filter(key => key.startsWith(prefix));
    }

    // 返回成功响应
    const response = {
      success: true,
      message: `成功获取键列表，共找到 ${keys.length} 个键`,
      keys: keys,
      count: keys.length,
      hasMore: hasMore,
      cursor: nextCursor,
      filter: {
        prefix: prefix || null,
        limit: limit
      },
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(response), {
      status: 200,
      headers: corsHeaders,
    });

  } catch (error) {
    // 错误处理
    console.error('获取键列表时发生错误:', error);
    
    const errorResponse = {
      success: false,
      message: '获取键列表时发生内部错误',
      error: error.message || 'INTERNAL_SERVER_ERROR',
      keys: [],
      count: 0,
      hasMore: false,
      cursor: null,
      timestamp: new Date().toISOString()
    };

    return new Response(JSON.stringify(errorResponse), {
      status: 500,
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Access-Control-Allow-Origin': '*',
      },
    });
  }
}
