/**
 * EdgeOneStorageImpl 完整验证脚本
 * 用于彻底排查和验证所有逻辑流程
 */

class EdgeOneVerificationScript {
    constructor() {
        this.results = [];
        this.storage = null;
        this.networkRequests = [];
        this.originalFetch = null;
    }

    // 记录测试结果
    log(test, status, message, details = null) {
        const result = {
            test,
            status, // 'PASS', 'FAIL', 'WARN', 'INFO'
            message,
            details,
            timestamp: new Date().toISOString()
        };
        
        this.results.push(result);
        
        const emoji = {
            'PASS': '✅',
            'FAIL': '❌',
            'WARN': '⚠️',
            'INFO': 'ℹ️'
        }[status];
        
        console.log(`${emoji} [${test}] ${message}`);
        if (details) {
            console.log('   详情:', details);
        }
    }

    // 启动网络监控
    startNetworkMonitoring() {
        this.networkRequests = [];
        
        if (!this.originalFetch) {
            this.originalFetch = window.fetch;
        }
        
        window.fetch = async (...args) => {
            const [url, options] = args;
            const startTime = Date.now();
            
            const requestInfo = {
                url: typeof url === 'string' ? url : url.toString(),
                method: options?.method || 'GET',
                startTime,
                timestamp: new Date().toISOString()
            };
            
            if (requestInfo.url.includes('/storage/')) {
                this.networkRequests.push(requestInfo);
                console.log(`📡 网络请求: ${requestInfo.method} ${requestInfo.url}`);
            }
            
            try {
                const response = await this.originalFetch.apply(this, args);
                const responseTime = Date.now() - startTime;
                
                if (requestInfo.url.includes('/storage/')) {
                    requestInfo.status = response.status;
                    requestInfo.responseTime = responseTime;
                    requestInfo.success = response.ok;
                    
                    console.log(`📡 响应: ${response.status} (${responseTime}ms)`);
                }
                
                return response;
            } catch (error) {
                const responseTime = Date.now() - startTime;
                
                if (requestInfo.url.includes('/storage/')) {
                    requestInfo.error = error.message;
                    requestInfo.responseTime = responseTime;
                    requestInfo.success = false;
                    
                    console.log(`📡 错误: ${error.message} (${responseTime}ms)`);
                }
                
                throw error;
            }
        };
        
        this.log('NETWORK_MONITOR', 'INFO', '网络监控已启动');
    }

    // 停止网络监控
    stopNetworkMonitoring() {
        if (this.originalFetch) {
            window.fetch = this.originalFetch;
        }
        
        this.log('NETWORK_MONITOR', 'INFO', '网络监控已停止');
    }

    // 1. 验证构造函数和初始化
    async verifyConstructorAndInitialization() {
        this.log('CONSTRUCTOR', 'INFO', '开始验证构造函数和初始化流程');
        
        try {
            // 测试默认构造函数
            this.storage = new EdgeOneStorageImpl();
            this.log('CONSTRUCTOR', 'PASS', '默认构造函数执行成功');
            
            // 检查初始状态
            const initialStatus = this.storage.getStorageStatus();
            this.log('CONSTRUCTOR', 'INFO', '初始状态', initialStatus);
            
            // 验证配置
            if (initialStatus.fallbackEnabled !== true) {
                this.log('CONSTRUCTOR', 'FAIL', 'enableFallback 默认值不正确', { expected: true, actual: initialStatus.fallbackEnabled });
            } else {
                this.log('CONSTRUCTOR', 'PASS', 'enableFallback 默认值正确');
            }
            
            // 验证初始化 Promise
            if (this.storage.initializationPromise) {
                this.log('CONSTRUCTOR', 'PASS', '初始化 Promise 已创建');
                
                // 等待初始化完成
                const initResult = await this.storage.initializationPromise;
                this.log('CONSTRUCTOR', 'PASS', '初始化 Promise 执行完成', { result: initResult });
                
                // 检查最终状态
                const finalStatus = this.storage.getStorageStatus();
                this.log('CONSTRUCTOR', 'INFO', '初始化后状态', finalStatus);
                
                if (finalStatus.isAvailable !== null) {
                    this.log('CONSTRUCTOR', 'PASS', '初始化检测已完成');
                } else {
                    this.log('CONSTRUCTOR', 'FAIL', '初始化检测未完成');
                }
                
            } else {
                this.log('CONSTRUCTOR', 'FAIL', '初始化 Promise 未创建');
            }
            
        } catch (error) {
            this.log('CONSTRUCTOR', 'FAIL', '构造函数验证失败', { error: error.message });
        }
    }

    // 2. 验证网络请求检测
    async verifyNetworkDetection() {
        this.log('NETWORK_DETECTION', 'INFO', '开始验证网络请求检测');
        
        try {
            // 清空之前的网络请求记录
            this.networkRequests = [];
            
            // 手动触发检测
            if (this.storage) {
                const isDeployed = await this.storage.checkFunctionsDeployment();
                this.log('NETWORK_DETECTION', 'INFO', '手动检测结果', { deployed: isDeployed });
                
                // 检查是否有网络请求
                const storageRequests = this.networkRequests.filter(req => req.url.includes('/storage/'));
                
                if (storageRequests.length > 0) {
                    this.log('NETWORK_DETECTION', 'PASS', '检测到网络请求', { count: storageRequests.length });
                    
                    // 验证请求详情
                    for (const req of storageRequests) {
                        this.log('NETWORK_DETECTION', 'INFO', '请求详情', req);
                        
                        if (req.url.includes('__deployment_test__')) {
                            this.log('NETWORK_DETECTION', 'PASS', '部署检测请求正确');
                        }
                        
                        if (req.method === 'GET') {
                            this.log('NETWORK_DETECTION', 'PASS', 'HTTP 方法正确');
                        }
                        
                        if (req.responseTime) {
                            if (req.responseTime < 5000) {
                                this.log('NETWORK_DETECTION', 'PASS', '响应时间正常', { time: req.responseTime });
                            } else {
                                this.log('NETWORK_DETECTION', 'WARN', '响应时间较长', { time: req.responseTime });
                            }
                        }
                    }
                } else {
                    this.log('NETWORK_DETECTION', 'FAIL', '未检测到网络请求');
                }
            } else {
                this.log('NETWORK_DETECTION', 'FAIL', '存储实例不存在');
            }
            
        } catch (error) {
            this.log('NETWORK_DETECTION', 'FAIL', '网络检测验证失败', { error: error.message });
        }
    }

    // 3. 验证存储模式选择
    async verifyStorageModeSelection() {
        this.log('STORAGE_MODE', 'INFO', '开始验证存储模式选择');
        
        if (!this.storage) {
            this.log('STORAGE_MODE', 'FAIL', '存储实例不存在');
            return;
        }
        
        try {
            const status = this.storage.getStorageStatus();
            
            // 验证模式设置
            if (status.storageMode === 'remote' || status.storageMode === 'local') {
                this.log('STORAGE_MODE', 'PASS', '存储模式设置正确', { mode: status.storageMode });
            } else {
                this.log('STORAGE_MODE', 'FAIL', '存储模式设置错误', { mode: status.storageMode });
            }
            
            // 验证可用性状态
            if (typeof status.isAvailable === 'boolean' || status.isAvailable === null) {
                this.log('STORAGE_MODE', 'PASS', '可用性状态类型正确', { available: status.isAvailable });
            } else {
                this.log('STORAGE_MODE', 'FAIL', '可用性状态类型错误', { available: status.isAvailable });
            }
            
            // 测试模式切换
            const originalMode = status.storageMode;
            const newMode = originalMode === 'remote' ? 'local' : 'remote';
            
            this.storage.setStorageMode(newMode);
            const switchedStatus = this.storage.getStorageStatus();
            
            if (switchedStatus.storageMode === newMode) {
                this.log('STORAGE_MODE', 'PASS', '模式切换成功', { from: originalMode, to: newMode });
            } else {
                this.log('STORAGE_MODE', 'FAIL', '模式切换失败', { expected: newMode, actual: switchedStatus.storageMode });
            }
            
            // 恢复原始模式
            this.storage.setStorageMode(originalMode);
            
        } catch (error) {
            this.log('STORAGE_MODE', 'FAIL', '存储模式验证失败', { error: error.message });
        }
    }

    // 4. 验证 CRUD 操作
    async verifyCRUDOperations() {
        this.log('CRUD', 'INFO', '开始验证 CRUD 操作');
        
        if (!this.storage) {
            this.log('CRUD', 'FAIL', '存储实例不存在');
            return;
        }
        
        try {
            const testKey = 'verification_test_key';
            const testValue = { test: 'data', timestamp: Date.now() };
            
            // 测试 PUT
            await this.storage.put(testKey, testValue);
            this.log('CRUD', 'PASS', 'PUT 操作成功');
            
            // 测试 GET
            const retrievedValue = await this.storage.get(testKey);
            if (JSON.stringify(retrievedValue) === JSON.stringify(testValue)) {
                this.log('CRUD', 'PASS', 'GET 操作成功，数据一致');
            } else {
                this.log('CRUD', 'FAIL', 'GET 操作失败，数据不一致', { 
                    expected: testValue, 
                    actual: retrievedValue 
                });
            }
            
            // 测试 LIST
            const keys = await this.storage.list();
            if (Array.isArray(keys) && keys.includes(testKey)) {
                this.log('CRUD', 'PASS', 'LIST 操作成功，包含测试键');
            } else {
                this.log('CRUD', 'WARN', 'LIST 操作结果异常', { keys, searchKey: testKey });
            }
            
            // 测试 DELETE
            await this.storage.delete(testKey);
            this.log('CRUD', 'PASS', 'DELETE 操作成功');
            
            // 验证删除
            const deletedValue = await this.storage.get(testKey);
            if (deletedValue === null) {
                this.log('CRUD', 'PASS', '删除验证成功');
            } else {
                this.log('CRUD', 'FAIL', '删除验证失败，数据仍存在', { value: deletedValue });
            }
            
        } catch (error) {
            this.log('CRUD', 'FAIL', 'CRUD 操作验证失败', { error: error.message });
        }
    }

    // 5. 验证异步流程
    async verifyAsyncFlow() {
        this.log('ASYNC_FLOW', 'INFO', '开始验证异步流程');
        
        try {
            // 创建新实例测试异步流程
            const asyncStorage = new EdgeOneStorageImpl();
            
            // 立即执行操作（测试首次使用等待）
            const startTime = Date.now();
            await asyncStorage.put('async_test', 'async_value');
            const operationTime = Date.now() - startTime;
            
            this.log('ASYNC_FLOW', 'PASS', '首次使用操作成功', { time: operationTime });
            
            // 检查状态
            const status = asyncStorage.getStorageStatus();
            if (status.isAvailable !== null) {
                this.log('ASYNC_FLOW', 'PASS', '异步初始化已完成');
            } else {
                this.log('ASYNC_FLOW', 'WARN', '异步初始化状态异常');
            }
            
            // 清理
            await asyncStorage.delete('async_test');
            
        } catch (error) {
            this.log('ASYNC_FLOW', 'FAIL', '异步流程验证失败', { error: error.message });
        }
    }

    // 6. 验证错误处理
    async verifyErrorHandling() {
        this.log('ERROR_HANDLING', 'INFO', '开始验证错误处理');
        
        if (!this.storage) {
            this.log('ERROR_HANDLING', 'FAIL', '存储实例不存在');
            return;
        }
        
        try {
            // 测试无效键名
            try {
                await this.storage.put('', 'value');
                this.log('ERROR_HANDLING', 'FAIL', '应该拒绝空键名');
            } catch (error) {
                this.log('ERROR_HANDLING', 'PASS', '正确拒绝空键名');
            }
            
            // 测试无效键类型
            try {
                await this.storage.put(null, 'value');
                this.log('ERROR_HANDLING', 'FAIL', '应该拒绝 null 键名');
            } catch (error) {
                this.log('ERROR_HANDLING', 'PASS', '正确拒绝 null 键名');
            }
            
            // 测试获取不存在的键
            const nonExistent = await this.storage.get('non_existent_key_12345');
            if (nonExistent === null) {
                this.log('ERROR_HANDLING', 'PASS', '正确处理不存在的键');
            } else {
                this.log('ERROR_HANDLING', 'WARN', '不存在的键返回值异常', { value: nonExistent });
            }
            
        } catch (error) {
            this.log('ERROR_HANDLING', 'FAIL', '错误处理验证失败', { error: error.message });
        }
    }

    // 执行完整验证
    async runCompleteVerification() {
        console.log('🚀 开始 EdgeOneStorageImpl 完整验证');
        console.log('=' .repeat(60));
        
        this.startNetworkMonitoring();
        
        try {
            await this.verifyConstructorAndInitialization();
            await this.verifyNetworkDetection();
            await this.verifyStorageModeSelection();
            await this.verifyCRUDOperations();
            await this.verifyAsyncFlow();
            await this.verifyErrorHandling();
            
        } finally {
            this.stopNetworkMonitoring();
        }
        
        // 生成报告
        this.generateReport();
    }

    // 生成验证报告
    generateReport() {
        console.log('\n' + '=' .repeat(60));
        console.log('📊 验证报告');
        console.log('=' .repeat(60));
        
        const summary = {
            total: this.results.length,
            pass: this.results.filter(r => r.status === 'PASS').length,
            fail: this.results.filter(r => r.status === 'FAIL').length,
            warn: this.results.filter(r => r.status === 'WARN').length,
            info: this.results.filter(r => r.status === 'INFO').length
        };
        
        console.log(`总测试数: ${summary.total}`);
        console.log(`✅ 通过: ${summary.pass}`);
        console.log(`❌ 失败: ${summary.fail}`);
        console.log(`⚠️ 警告: ${summary.warn}`);
        console.log(`ℹ️ 信息: ${summary.info}`);
        
        const successRate = ((summary.pass / (summary.pass + summary.fail)) * 100).toFixed(1);
        console.log(`\n成功率: ${successRate}%`);
        
        // 显示失败的测试
        const failures = this.results.filter(r => r.status === 'FAIL');
        if (failures.length > 0) {
            console.log('\n❌ 失败的测试:');
            failures.forEach(f => {
                console.log(`   [${f.test}] ${f.message}`);
                if (f.details) {
                    console.log(`      详情: ${JSON.stringify(f.details)}`);
                }
            });
        }
        
        // 网络请求统计
        if (this.networkRequests.length > 0) {
            console.log('\n🌐 网络请求统计:');
            console.log(`   总请求数: ${this.networkRequests.length}`);
            
            const successfulRequests = this.networkRequests.filter(r => r.success);
            console.log(`   成功请求: ${successfulRequests.length}`);
            
            const avgResponseTime = this.networkRequests
                .filter(r => r.responseTime)
                .reduce((sum, r) => sum + r.responseTime, 0) / this.networkRequests.length;
            console.log(`   平均响应时间: ${avgResponseTime.toFixed(0)}ms`);
        }
        
        console.log('\n' + '=' .repeat(60));
        
        return {
            summary,
            results: this.results,
            networkRequests: this.networkRequests
        };
    }
}

// 导出验证脚本
window.EdgeOneVerificationScript = EdgeOneVerificationScript;
