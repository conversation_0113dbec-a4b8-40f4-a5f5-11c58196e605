# Functions 部署检测问题排查指南

## 🔍 问题诊断步骤

### 1. 检查浏览器控制台日志

打开浏览器开发者工具（F12），查看控制台是否有以下日志输出：

```
[EdgeOneStorage] 启动异步初始化检测...
[EdgeOneStorage] 开始检测 Cloudflare Functions 部署状态...
[EdgeOneStorage] 🔍 测试 Functions 端点: /storage/get?key=__deployment_test__
[EdgeOneStorage] 🔍 发送 HTTP 请求...
[EdgeOneStorage] 📡 收到响应: 200 OK
[EdgeOneStorage] ✅ Functions 已部署并生效
```

**如果没有看到这些日志**：
- 检查 `enableFallback` 选项是否启用（默认启用）
- 确认 EdgeOneStorageImpl 实例是否正确创建

### 2. 使用调试功能

#### 方法一：使用测试页面调试
1. 打开 `test-storage-system.html`
2. 点击"🔧 调试初始化"按钮
3. 查看详细的检测流程和状态信息

#### 方法二：使用演示页面调试
1. 打开 `storage-service-demo.html`
2. 点击"🔧 调试初始化"按钮
3. 查看调试输出

#### 方法三：代码中手动调试
```javascript
const storage = new EdgeOneStorageImpl();

// 等待一段时间让初始化完成
setTimeout(async () => {
    const debugInfo = await storage.debugInitialization();
    console.log('调试信息:', debugInfo);
}, 2000);
```

### 3. 验证 Functions 端点

#### 使用专用测试工具
1. 打开 `functions-test.html`
2. 点击"模拟部署检测"按钮
3. 查看检测流程是否正常

#### 手动测试端点
在浏览器中直接访问：
```
/storage/get?key=test
```

**预期响应格式**：
```json
{
  "success": true,
  "message": "未找到指定键的数据",
  "key": "test",
  "value": null,
  "found": false,
  "timestamp": "2024-08-04T..."
}
```

### 4. 检查部署状态

#### 确认 Functions 文件存在
检查以下文件是否存在：
- `functions/storage/get.js`
- `functions/storage/put.js`
- `functions/storage/delete.js`
- `functions/storage/list.js`

#### 验证 Cloudflare 部署
1. 检查 Cloudflare Pages 部署状态
2. 确认 Functions 是否正确部署到 `/storage/` 路径
3. 查看部署日志是否有错误

## ❌ 常见问题和解决方案

### 问题 1：控制台没有检测日志

**可能原因**：
- `enableFallback` 被设置为 `false`
- 异步初始化被跳过

**解决方案**：
```javascript
// 确保启用降级功能
const storage = new EdgeOneStorageImpl({
    enableFallback: true  // 明确启用
});

// 或者手动触发检测
await storage.debugInitialization();
```

### 问题 2：检测超时

**现象**：
```
[EdgeOneStorage] ⏰ 请求超时，取消请求
[EdgeOneStorage] ⏰ Functions 检测超时（5秒）
```

**可能原因**：
- Functions 未部署或部署失败
- 网络连接问题
- 服务器响应慢

**解决方案**：
1. 检查 Functions 部署状态
2. 手动访问端点验证可用性
3. 检查网络连接

### 问题 3：HTTP 状态码异常

**现象**：
```
[EdgeOneStorage] ❌ Functions 响应异常: 404 Not Found
```

**可能原因**：
- Functions 路径配置错误
- 端点未正确部署

**解决方案**：
1. 确认 Functions 文件在正确的路径
2. 检查 Cloudflare Pages 路由配置
3. 验证部署是否成功

### 问题 4：JSON 格式不符合预期

**现象**：
```
[EdgeOneStorage] ⚠️ Functions 响应格式不符合预期: {...}
```

**可能原因**：
- Functions 返回的不是标准 API 格式
- 响应被代理或修改

**解决方案**：
1. 检查 Functions 代码是否正确
2. 验证响应是否包含 `success`、`message` 或 `found` 字段
3. 查看实际响应内容

### 问题 5：网络请求失败

**现象**：
```
[EdgeOneStorage] 🌐 网络请求失败: fetch failed
```

**可能原因**：
- 网络连接问题
- CORS 配置问题
- 服务器不可达

**解决方案**：
1. 检查网络连接
2. 验证 CORS 配置
3. 确认服务器状态

## 🔧 调试技巧

### 1. 启用详细日志
```javascript
const storage = new EdgeOneStorageImpl({
    enableFallback: true,
    timeout: 10000  // 增加超时时间便于调试
});
```

### 2. 手动控制检测时机
```javascript
const storage = new EdgeOneStorageImpl({
    enableFallback: false  // 禁用自动检测
});

// 手动触发检测
const isAvailable = await storage.checkAvailability(true);
console.log('Functions 可用性:', isAvailable);
```

### 3. 监控状态变化
```javascript
const storage = new EdgeOneStorageImpl();

// 定期检查状态
setInterval(() => {
    const status = storage.getStorageStatus();
    console.log('当前状态:', status);
}, 5000);
```

### 4. 测试特定端点
```javascript
// 直接测试端点
async function testEndpoint() {
    try {
        const response = await fetch('/storage/get?key=test');
        const data = await response.json();
        console.log('端点测试结果:', data);
    } catch (error) {
        console.error('端点测试失败:', error);
    }
}
```

## 📊 状态信息解读

### getStorageStatus() 返回值说明
```javascript
{
    storageMode: 'remote',           // 当前存储模式
    isAvailable: true,               // 服务可用性
    functionsDeployed: true,         // Functions 部署状态
    useRelativePath: true,           // 是否使用相对路径
    baseUrl: '',                     // API 基础 URL
    fallbackEnabled: true,           // 是否启用降级
    initializationPending: false,    // 初始化是否进行中
    lastCheck: '2024-08-04T...'      // 最后检测时间
}
```

### 状态组合含义
- `storageMode: 'remote'` + `isAvailable: true` = Functions 正常工作
- `storageMode: 'local'` + `isAvailable: false` = Functions 不可用，已降级
- `storageMode: 'remote'` + `isAvailable: null` = 尚未检测
- `initializationPending: true` = 检测正在进行中

## 🚀 最佳实践

### 1. 生产环境监控
```javascript
const storage = new EdgeOneStorageImpl();

// 监控存储模式变化
let lastMode = null;
setInterval(() => {
    const currentMode = storage.getStats().storageMode;
    if (lastMode && lastMode !== currentMode) {
        console.warn(`存储模式变化: ${lastMode} → ${currentMode}`);
        // 发送告警或记录日志
    }
    lastMode = currentMode;
}, 30000);
```

### 2. 错误恢复
```javascript
// 定期重新检测
setInterval(async () => {
    const status = storage.getStorageStatus();
    if (status.storageMode === 'local') {
        console.log('尝试恢复远程存储...');
        await storage.recheckAvailability();
    }
}, 60000);
```

### 3. 性能优化
```javascript
const storage = new EdgeOneStorageImpl({
    enableFallback: true,
    enableCache: true,      // 启用缓存
    timeout: 5000,          // 适当的超时时间
    retryCount: 2           // 减少重试次数
});
```
