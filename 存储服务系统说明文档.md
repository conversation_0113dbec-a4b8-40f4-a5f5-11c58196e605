# 存储服务系统说明文档

## 概述

本存储服务系统是一个完整的云存储解决方案，基于 Cloudflare Functions 和 EdgeOne 存储服务构建。系统提供了完整的 CRUD（创建、读取、更新、删除）操作，支持键值对数据存储，具有高可用性、低延迟和全球分布的特点。

## 系统架构

```
客户端应用
    ↓
EdgeOneStorageImpl 服务类
    ↓
Cloudflare Functions API 端点
    ↓
EdgeOne 存储服务
```

## 核心组件

### 1. Cloudflare Functions API 端点

位于 `functions/storage/` 目录下，包含四个核心 API 端点：

- **put.js** - 存储键值对数据
- **get.js** - 根据键获取值
- **delete.js** - 删除指定键的数据
- **list.js** - 列出所有存储的键

### 2. EdgeOneStorageImpl 服务类

位于 `js/utils/edgeOneStorageImpl.js`，提供统一的客户端接口，封装了所有存储操作。

## API 端点详细说明

### PUT 端点 - 存储数据

**文件**: `functions/storage/put.js`

**请求方式**: POST

**请求参数**:
- `key` (string, 必需): 存储的键名
- `value` (any, 必需): 存储的值

**请求示例**:
```javascript
// JSON 格式
POST /storage/put
Content-Type: application/json

{
  "key": "user_settings",
  "value": {
    "theme": "dark",
    "language": "zh-CN"
  }
}
```

**响应格式**:
```json
{
  "success": true,
  "message": "数据存储成功",
  "key": "user_settings",
  "timestamp": "2024-08-04T10:30:00.000Z"
}
```

### GET 端点 - 获取数据

**文件**: `functions/storage/get.js`

**请求方式**: GET

**请求参数**:
- `key` (string, 必需): 要获取的键名

**请求示例**:
```
GET /storage/get?key=user_settings
```

**响应格式**:
```json
{
  "success": true,
  "message": "数据获取成功",
  "key": "user_settings",
  "value": {
    "theme": "dark",
    "language": "zh-CN"
  },
  "found": true,
  "timestamp": "2024-08-04T10:30:00.000Z"
}
```

### DELETE 端点 - 删除数据

**文件**: `functions/storage/delete.js`

**请求方式**: DELETE

**请求参数**:
- `key` (string, 必需): 要删除的键名

**请求示例**:
```
DELETE /storage/delete?key=user_settings
```

**响应格式**:
```json
{
  "success": true,
  "message": "数据删除成功",
  "key": "user_settings",
  "deleted": true,
  "timestamp": "2024-08-04T10:30:00.000Z"
}
```

### LIST 端点 - 列出键

**文件**: `functions/storage/list.js`

**请求方式**: GET

**请求参数** (可选):
- `prefix` (string): 键名前缀过滤
- `limit` (number): 返回结果数量限制，默认100，最大1000
- `cursor` (string): 分页游标

**请求示例**:
```
GET /storage/list?prefix=user_&limit=50
```

**响应格式**:
```json
{
  "success": true,
  "message": "成功获取键列表，共找到 2 个键",
  "keys": ["user_settings", "user_profile"],
  "count": 2,
  "hasMore": false,
  "cursor": null,
  "filter": {
    "prefix": "user_",
    "limit": 50
  },
  "timestamp": "2024-08-04T10:30:00.000Z"
}
```

## EdgeOneStorageImpl 服务类使用指南

### 初始化

```javascript
// 基本初始化（使用相对路径，调用本地 functions/storage/ 端点）
const storage = new EdgeOneStorageImpl();

// 使用远程 API 端点
const storage = new EdgeOneStorageImpl('https://your-domain.com');

// 带配置选项的初始化
const storage = new EdgeOneStorageImpl('https://your-domain.com', {
  timeout: 30000,        // 请求超时时间（毫秒）
  retryCount: 3,         // 重试次数
  retryDelay: 1000,      // 重试延迟（毫秒）
  enableCache: true,     // 启用本地缓存
  cacheTimeout: 300000,  // 缓存超时时间（毫秒）
  enableFallback: true   // 启用本地存储降级（默认启用）
});

// 仅使用配置选项（使用相对路径）
const storage = new EdgeOneStorageImpl({
  enableCache: true,
  enableFallback: true,
  timeout: 10000
});
```

### 基本操作

#### 存储数据
```javascript
// 存储字符串
await storage.put('username', 'john_doe');

// 存储对象
await storage.put('user_profile', {
  name: '张三',
  age: 25,
  email: '<EMAIL>'
});

// 存储数组
await storage.put('favorite_games', ['时空织梦者', '瞬光捕手', '量子共鸣者']);
```

#### 获取数据
```javascript
// 获取数据
const username = await storage.get('username');
console.log(username); // 'john_doe'

// 获取对象
const profile = await storage.get('user_profile');
console.log(profile.name); // '张三'

// 获取不存在的键
const nonExistent = await storage.get('non_existent_key');
console.log(nonExistent); // null
```

#### 删除数据
```javascript
// 删除单个键
await storage.delete('username');

// 检查删除结果
const result = await storage.delete('user_profile');
console.log(result.deleted); // true
```

#### 列出键
```javascript
// 获取所有键
const allKeys = await storage.list();
console.log(allKeys.keys); // ['key1', 'key2', ...]

// 带前缀过滤
const userKeys = await storage.list({ prefix: 'user_' });
console.log(userKeys.keys); // ['user_settings', 'user_profile', ...]

// 分页获取
const firstPage = await storage.list({ limit: 10 });
if (firstPage.hasMore) {
  const secondPage = await storage.list({ 
    limit: 10, 
    cursor: firstPage.cursor 
  });
}
```

### 批量操作

#### 批量存储
```javascript
const data = {
  'config_theme': 'dark',
  'config_language': 'zh-CN',
  'config_notifications': true
};

const results = await storage.putBatch(data);
console.log(`成功存储: ${results.filter(r => r.success).length} 个键`);
```

#### 批量获取
```javascript
const keys = ['config_theme', 'config_language', 'config_notifications'];
const data = await storage.getBatch(keys);
console.log(data);
// {
//   'config_theme': 'dark',
//   'config_language': 'zh-CN',
//   'config_notifications': true
// }
```

#### 批量删除
```javascript
const keysToDelete = ['old_config1', 'old_config2', 'temp_data'];
const results = await storage.deleteBatch(keysToDelete);
console.log(`成功删除: ${results.filter(r => r.success).length} 个键`);
```

### 实用方法

#### 检查键是否存在
```javascript
const exists = await storage.exists('user_profile');
if (exists) {
  console.log('用户配置文件存在');
}
```

#### 清空缓存
```javascript
storage.clearCache();
```

#### 获取统计信息
```javascript
const stats = storage.getStats();
console.log(`已发送请求: ${stats.requestCount} 次`);
console.log(`缓存大小: ${stats.cacheSize} 个条目`);
console.log(`存储模式: ${stats.storageMode}`); // 'remote' 或 'local'
console.log(`服务可用性: ${stats.isAvailable}`);
console.log(`降级功能: ${stats.fallbackEnabled ? '启用' : '禁用'}`);
```

#### 可用性检测和存储模式管理
```javascript
// 检查服务可用性
const isAvailable = await storage.checkAvailability();
console.log(`远程存储服务${isAvailable ? '可用' : '不可用'}`);

// 重新检测可用性
await storage.recheckAvailability();

// 手动切换存储模式
storage.setStorageMode('local');  // 强制使用本地存储
storage.setStorageMode('remote'); // 强制使用远程存储

// 查看当前存储模式
const stats = storage.getStats();
console.log(`当前存储模式: ${stats.storageMode}`);
```

## 新增功能特性 🆕

### 1. 智能降级机制与 Functions 部署检测

系统在初始化时会自动检测 Cloudflare Functions 是否已部署并生效，根据检测结果选择最佳的存储方式：

```javascript
// 启用降级机制（默认启用）
const storage = new EdgeOneStorageImpl({
  enableFallback: true
});

// 系统会自动执行以下流程：
// 1. 检测 Functions 是否已部署
// 2. 验证 API 端点是否响应正常
// 3. 根据检测结果选择远程存储或本地存储
await storage.put('key', 'value'); // 首次使用时触发检测
```

**检测流程说明**：
1. **部署检测**：向 `/storage/get?key=__deployment_test__` 发送 GET 请求（5秒超时）
2. **状态验证**：检查 HTTP 响应状态码是否为 200
3. **格式验证**：验证响应是否为有效 JSON 且包含 API 字段（success/message/found）
4. **自动选择**：根据检测结果自动选择存储方式

### 2. 灵活的初始化方式

支持多种初始化方式，适应不同的部署场景：

```javascript
// 方式1：无参数初始化，使用相对路径调用本地端点
const storage1 = new EdgeOneStorageImpl();

// 方式2：仅传递配置选项
const storage2 = new EdgeOneStorageImpl({
  enableCache: true,
  timeout: 10000
});

// 方式3：传递远程 URL
const storage3 = new EdgeOneStorageImpl('https://api.example.com');

// 方式4：传递 URL 和配置选项
const storage4 = new EdgeOneStorageImpl('https://api.example.com', {
  enableFallback: true
});
```

### 3. Functions 部署检测和监控

提供专门的 Functions 部署状态检测功能：

```javascript
// 检查 Functions 是否已部署
const isDeployed = await storage.checkFunctionsDeployment();
if (isDeployed) {
  console.log('✅ Cloudflare Functions 已部署并生效');
} else {
  console.log('❌ Functions 未部署，将使用本地存储');
}

// 获取详细的存储状态
const status = storage.getStorageStatus();
console.log('存储状态:', {
  storageMode: status.storageMode,           // 当前存储模式
  functionsDeployed: status.functionsDeployed, // Functions 部署状态
  isAvailable: status.isAvailable,          // 服务可用性
  lastCheck: status.lastCheck               // 最后检测时间
});

// 完整的可用性检测（包含 Functions 检测）
const isAvailable = await storage.checkAvailability();

// 重新检测（例如重新部署 Functions 后）
await storage.recheckAvailability();

// 手动控制存储模式
storage.setStorageMode('local');  // 强制使用本地存储
storage.setStorageMode('remote'); // 强制使用远程存储
```

### 4. 增强的统计信息

新增存储模式和可用性相关的统计信息：

```javascript
const stats = storage.getStats();
console.log('存储统计信息:', {
  requestCount: stats.requestCount,      // 请求总数
  storageMode: stats.storageMode,        // 当前存储模式
  isAvailable: stats.isAvailable,        // 远程服务可用性
  fallbackEnabled: stats.fallbackEnabled, // 是否启用降级
  useRelativePath: stats.useRelativePath  // 是否使用相对路径
});
```

## 错误处理

### 常见错误类型

1. **参数错误**
   - `MISSING_KEY_PARAMETER`: 缺少键名参数
   - `MISSING_VALUE_PARAMETER`: 缺少值参数

2. **HTTP 错误**
   - `METHOD_NOT_ALLOWED`: 请求方法不被允许
   - `KEY_NOT_FOUND`: 指定的键不存在

3. **系统错误**
   - `INTERNAL_SERVER_ERROR`: 服务器内部错误
   - 网络超时或连接失败

4. **存储相关错误**
   - `KV 存储命名空间未配置`: Cloudflare KV 环境变量未设置
   - `本地存储操作失败`: localStorage 访问受限或存储空间不足
   - `不支持的本地存储操作`: 调用了不支持的本地存储方法

### 错误处理示例

```javascript
try {
  await storage.put('test_key', 'test_value');
} catch (error) {
  console.error('存储失败:', error.message);

  // 根据错误类型进行处理
  if (error.message.includes('timeout')) {
    console.log('请求超时，请稍后重试');
  } else if (error.message.includes('网络')) {
    console.log('网络连接失败，已自动切换到本地存储');
  } else if (error.message.includes('本地存储')) {
    console.log('本地存储操作失败，请检查浏览器设置');
  }

  // 检查当前存储模式
  const stats = storage.getStats();
  console.log(`当前使用${stats.storageMode === 'local' ? '本地' : '远程'}存储`);
}

// 监听存储模式变化
const originalMode = storage.getStats().storageMode;
await storage.put('test', 'value');
const newMode = storage.getStats().storageMode;

if (originalMode !== newMode) {
  console.log(`存储模式已从 ${originalMode} 切换到 ${newMode}`);
}
```

## 性能优化建议

### 1. 启用缓存
```javascript
const storage = new EdgeOneStorageImpl('https://your-domain.com', {
  enableCache: true,
  cacheTimeout: 300000 // 5分钟缓存
});
```

### 2. 使用批量操作
```javascript
// 推荐：批量操作
await storage.putBatch(multipleData);

// 不推荐：循环单个操作
for (const [key, value] of Object.entries(multipleData)) {
  await storage.put(key, value);
}
```

### 3. 合理设置重试参数
```javascript
const storage = new EdgeOneStorageImpl('https://your-domain.com', {
  retryCount: 2,     // 减少重试次数以提高响应速度
  retryDelay: 500    // 减少重试延迟
});
```

## 部署说明

### 1. Cloudflare Functions 部署

将 `functions/storage/` 目录下的所有文件部署到 Cloudflare Pages 或 Workers。

### 2. 域名配置

确保 API 端点可以通过以下 URL 访问：
- `https://your-domain.com/storage/put`
- `https://your-domain.com/storage/get`
- `https://your-domain.com/storage/delete`
- `https://your-domain.com/storage/list`

### 3. CORS 配置

所有 API 端点已内置 CORS 支持，允许跨域访问。

## 安全注意事项

1. **访问控制**: 在生产环境中，建议添加身份验证和授权机制
2. **数据验证**: 对输入数据进行严格验证，防止恶意数据注入
3. **速率限制**: 实施 API 调用频率限制，防止滥用
4. **数据加密**: 对敏感数据进行加密存储

## 故障排除

### 常见问题

1. **API 端点无法访问**
   - 检查域名配置是否正确
   - 确认 Cloudflare Functions 部署状态

2. **请求超时**
   - 增加超时时间配置
   - 检查网络连接状态

3. **数据不一致**
   - 清空本地缓存
   - 检查并发操作是否正确处理

### 调试模式

启用详细日志输出：
```javascript
// 在浏览器控制台中查看详细日志
const storage = new EdgeOneStorageImpl('https://your-domain.com', {
  enableCache: true
});

// 查看统计信息
console.log(storage.getStats());
```

## 更新日志

- **v1.0.0** (2024-08-04): 初始版本发布
  - 实现基本的 CRUD 操作
  - 支持批量操作
  - 内置缓存和重试机制
  - 完整的错误处理
